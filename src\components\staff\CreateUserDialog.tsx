import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import {
  UserPlus,
  Key,
  AlertCircle,
  Copy,
  Eye,
  EyeOff
} from 'lucide-react';
import { useStaffManagement } from '@/hooks/useStaffManagement';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';

type UserRole = Database['public']['Enums']['user_role'];

interface CreateUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface UserFormData {
  email: string;
  name: string;
  role: UserRole;
  division_id: string;
  phone: string;
}

const CreateUserDialog: React.FC<CreateUserDialogProps> = ({ open, onOpenChange }) => {
  const { toast } = useToast();
  const { profile } = useAuth();
  const {
    createUser,
    cleanupOrphanedUsers,
    isLoading
  } = useStaffManagement();

  const [formData, setFormData] = useState<UserFormData>({
    email: '',
    name: '',
    role: 'staff',
    division_id: '',
    phone: ''
  });

  const [generatedPassword, setGeneratedPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Generate secure password function
  const generateSecurePassword = () => {
    const length = 12;
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  };

  // Get available roles based on current user's role
  const getAvailableRoles = () => {
    const currentUserRole = profile?.role;

    if (currentUserRole === 'admin') {
      // Admins can create any role
      return [
        { value: 'staff', label: 'Staff' },
        { value: 'field_staff', label: 'Field Staff' },
        { value: 'partner', label: 'Partner' },
        { value: 'accountant', label: 'Accountant' },
        { value: 'social_media_manager', label: 'Social Media Manager' },
        { value: 'program_officer', label: 'Program Officer' },
        { value: 'admin', label: 'Admin' },
      ];
    } else if (currentUserRole === 'program_officer') {
      // Program officers can only create field-level roles
      return [
        { value: 'staff', label: 'Staff' },
        { value: 'field_staff', label: 'Field Staff' },
        { value: 'partner', label: 'Partner' },
        { value: 'accountant', label: 'Accountant' },
        { value: 'social_media_manager', label: 'Social Media Manager' },
      ];
    } else {
      // Other roles shouldn't be able to create users, but just in case
      return [
        { value: 'staff', label: 'Staff' },
        { value: 'field_staff', label: 'Field Staff' },
      ];
    }
  };

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setFormData({
        email: '',
        name: '',
        role: 'staff',
        division_id: '',
        phone: ''
      });
      setGeneratedPassword('');
      setErrors({});
      setShowPassword(false);
    }
  }, [open]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (formData.phone && !/^\+?[\d\s-()]+$/.test(formData.phone)) {
      newErrors.phone = 'Invalid phone number format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof UserFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleGeneratePassword = () => {
    const password = generateSecurePassword();
    setGeneratedPassword(password);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to clipboard",
      description: "Password has been copied to your clipboard",
    });
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      // Create user account directly
      if (!generatedPassword) {
        setErrors({ password: 'Please generate a password first' });
        return;
      }

      createUser.mutate({
        email: formData.email,
        name: formData.name,
        role: formData.role,
        division_id: formData.division_id || undefined,
        phone: formData.phone || undefined,
        password: generatedPassword
      });

      // Close dialog on success (handled by mutation onSuccess callback)
    } catch (error) {
      console.error('Error creating user:', error);
    }
  };

  const isSubmitting = createUser.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <UserPlus className="h-5 w-5 mr-2" />
            Create New User
          </DialogTitle>
          <DialogDescription>
            Create a new user account with a secure password
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 mt-6">
            {/* User Information Form */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">User Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      className={errors.email ? 'border-red-500' : ''}
                    />
                    {errors.email && (
                      <p className="text-sm text-red-500">{errors.email}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="John Doe"
                      className={errors.name ? 'border-red-500' : ''}
                    />
                    {errors.name && (
                      <p className="text-sm text-red-500">{errors.name}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="role">Role *</Label>
                    <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value as UserRole)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        {getAvailableRoles().map((role) => (
                          <SelectItem key={role.value} value={role.value}>
                            {role.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="+256700000000"
                      className={errors.phone ? 'border-red-500' : ''}
                    />
                    {errors.phone && (
                      <p className="text-sm text-red-500">{errors.phone}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="division">Division (Optional)</Label>
                  <Input
                    id="division"
                    value={formData.division_id}
                    onChange={(e) => handleInputChange('division_id', e.target.value)}
                    placeholder="Leave empty if not assigned to a specific division"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Password Generation */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Key className="h-5 w-5 mr-2" />
                    Direct Account Creation
                  </CardTitle>
                  <CardDescription>
                    Create the account immediately with a generated password
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      You will need to securely share the generated password with the user.
                      They will be required to change it on first login.
                    </AlertDescription>
                  </Alert>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Button 
                        type="button" 
                        onClick={handleGeneratePassword}
                        variant="outline"
                      >
                        Generate Secure Password
                      </Button>
                      {generatedPassword && (
                        <Badge variant="default">Password Generated</Badge>
                      )}
                    </div>

                    {generatedPassword && (
                      <div className="space-y-2">
                        <Label>Generated Password</Label>
                        <div className="flex items-center space-x-2">
                          <Input
                            type={showPassword ? 'text' : 'password'}
                            value={generatedPassword}
                            readOnly
                            className="font-mono"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(generatedPassword)}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>
                        <p className="text-sm text-gray-500">
                          Make sure to copy and securely share this password with the user
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
          </div>

        <DialogFooter className="flex justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => cleanupOrphanedUsers()}
            className="text-xs"
          >
            🧹 Cleanup
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={createUser.isPending || !generatedPassword}
            >
              {createUser.isPending ? 'Creating...' : 'Create Account'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreateUserDialog;
