/**
 * Photo Upload Progress Tracker
 * Real-time progress monitoring with detailed statistics and queue management
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Upload, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Zap,
  BarChart3,
  RefreshCw,
  Trash2,
  Play,
  Pause
} from 'lucide-react';
import { photoUploadQueue, type QueueStats } from '@/utils/photoUploadQueueManager';
import { toast } from 'sonner';

// ============================================================================
// TYPES
// ============================================================================

interface ProgressTrackerProps {
  className?: string;
  showDetailedStats?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface QueueMetrics {
  throughput: number; // uploads per minute
  averageFileSize: number;
  compressionEfficiency: number;
  errorRate: number;
  queueWaitTime: number;
}

// ============================================================================
// PHOTO UPLOAD PROGRESS TRACKER COMPONENT
// ============================================================================

const PhotoUploadProgressTracker: React.FC<ProgressTrackerProps> = ({
  className = '',
  showDetailedStats = true,
  autoRefresh = true,
  refreshInterval = 2000,
}) => {
  const [stats, setStats] = useState<QueueStats | null>(null);
  const [metrics, setMetrics] = useState<QueueMetrics | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // ============================================================================
  // DATA FETCHING
  // ============================================================================

  const fetchStats = useCallback(async () => {
    try {
      setIsRefreshing(true);
      const queueStats = photoUploadQueue.getStats();
      setStats(queueStats);
      
      // Calculate derived metrics
      const calculatedMetrics: QueueMetrics = {
        throughput: queueStats.completedTasks > 0 && queueStats.averageUploadTime > 0 
          ? (60000 / queueStats.averageUploadTime) : 0,
        averageFileSize: queueStats.completedTasks > 0 
          ? queueStats.totalDataUploaded / queueStats.completedTasks : 0,
        compressionEfficiency: queueStats.compressionSavings,
        errorRate: queueStats.totalTasks > 0 
          ? (queueStats.failedTasks / queueStats.totalTasks) * 100 : 0,
        queueWaitTime: queueStats.pendingTasks * (queueStats.averageUploadTime || 5000),
      };
      
      setMetrics(calculatedMetrics);
      setLastUpdate(new Date());
      
    } catch (error) {
      console.error('Failed to fetch upload stats:', error);
      toast.error('Failed to refresh upload statistics');
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchStats, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchStats]);

  // ============================================================================
  // QUEUE MANAGEMENT ACTIONS
  // ============================================================================

  const handleClearCompleted = useCallback(() => {
    photoUploadQueue.clearCompleted();
    fetchStats();
    toast.success('Cleared completed uploads');
  }, [fetchStats]);

  const handleRetryFailed = useCallback(() => {
    photoUploadQueue.retryFailed();
    fetchStats();
    toast.success('Retrying failed uploads');
  }, [fetchStats]);

  // ============================================================================
  // RENDER HELPERS
  // ============================================================================

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const getStatusColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.warning) return 'text-yellow-600';
    return 'text-red-600';
  };

  // ============================================================================
  // RENDER COMPONENTS
  // ============================================================================

  const renderOverviewCards = () => {
    if (!stats) return null;

    const cards = [
      {
        title: 'Queue Status',
        value: `${stats.pendingTasks + stats.processingTasks + stats.uploadingTasks}`,
        subtitle: 'Active uploads',
        icon: <Activity className="h-5 w-5" />,
        color: 'text-blue-600',
      },
      {
        title: 'Completed',
        value: stats.completedTasks.toString(),
        subtitle: 'Successful uploads',
        icon: <CheckCircle className="h-5 w-5" />,
        color: 'text-green-600',
      },
      {
        title: 'Failed',
        value: stats.failedTasks.toString(),
        subtitle: 'Failed uploads',
        icon: <AlertCircle className="h-5 w-5" />,
        color: 'text-red-600',
      },
      {
        title: 'Data Uploaded',
        value: formatFileSize(stats.totalDataUploaded),
        subtitle: `${stats.compressionSavings.toFixed(1)}% saved`,
        icon: <Upload className="h-5 w-5" />,
        color: 'text-purple-600',
      },
    ];

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {cards.map((card, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{card.value}</p>
                  <p className="text-sm text-gray-500">{card.subtitle}</p>
                </div>
                <div className={`p-2 rounded-lg bg-gray-50 ${card.color}`}>
                  {card.icon}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const renderQueueProgress = () => {
    if (!stats) return null;

    const totalActive = stats.pendingTasks + stats.processingTasks + stats.uploadingTasks;
    const totalCompleted = stats.completedTasks + stats.failedTasks;
    const totalTasks = totalActive + totalCompleted;

    if (totalTasks === 0) {
      return (
        <Card>
          <CardContent className="p-8 text-center">
            <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Uploads</h3>
            <p className="text-gray-600">Upload queue is empty</p>
          </CardContent>
        </Card>
      );
    }

    const completionRate = totalTasks > 0 ? (totalCompleted / totalTasks) * 100 : 0;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Upload Progress</span>
            <Badge variant="outline">
              {totalCompleted} / {totalTasks} completed
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Overall Progress</span>
                <span>{completionRate.toFixed(1)}%</span>
              </div>
              <Progress value={completionRate} className="h-3" />
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <p className="font-medium text-gray-900">{stats.pendingTasks}</p>
                <p className="text-gray-600">Pending</p>
              </div>
              <div className="text-center">
                <p className="font-medium text-blue-600">{stats.processingTasks + stats.uploadingTasks}</p>
                <p className="text-gray-600">Active</p>
              </div>
              <div className="text-center">
                <p className="font-medium text-green-600">{stats.completedTasks}</p>
                <p className="text-gray-600">Completed</p>
              </div>
              <div className="text-center">
                <p className="font-medium text-red-600">{stats.failedTasks}</p>
                <p className="text-gray-600">Failed</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderPerformanceMetrics = () => {
    if (!metrics || !stats) return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Zap className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">Throughput</span>
              </div>
              <p className="text-2xl font-bold">{metrics.throughput.toFixed(1)}</p>
              <p className="text-sm text-gray-600">uploads/min</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <BarChart3 className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">Avg File Size</span>
              </div>
              <p className="text-2xl font-bold">{formatFileSize(metrics.averageFileSize)}</p>
              <p className="text-sm text-gray-600">per upload</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Activity className="h-4 w-4 text-purple-500" />
                <span className="text-sm font-medium">Compression</span>
              </div>
              <p className="text-2xl font-bold">{metrics.compressionEfficiency.toFixed(1)}%</p>
              <p className="text-sm text-gray-600">space saved</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <span className="text-sm font-medium">Error Rate</span>
              </div>
              <p className={`text-2xl font-bold ${getStatusColor(metrics.errorRate, { good: 5, warning: 15 })}`}>
                {metrics.errorRate.toFixed(1)}%
              </p>
              <p className="text-sm text-gray-600">failure rate</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-yellow-500" />
                <span className="text-sm font-medium">Avg Upload Time</span>
              </div>
              <p className="text-2xl font-bold">{formatDuration(stats.averageUploadTime)}</p>
              <p className="text-sm text-gray-600">per file</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-orange-500" />
                <span className="text-sm font-medium">Queue Wait</span>
              </div>
              <p className="text-2xl font-bold">{formatDuration(metrics.queueWaitTime)}</p>
              <p className="text-sm text-gray-600">estimated</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderQueueActions = () => {
    if (!stats) return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle>Queue Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={fetchStats}
              disabled={isRefreshing}
              variant="outline"
              size="sm"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>

            <Button
              onClick={handleClearCompleted}
              disabled={stats.completedTasks === 0}
              variant="outline"
              size="sm"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear Completed ({stats.completedTasks})
            </Button>

            <Button
              onClick={handleRetryFailed}
              disabled={stats.failedTasks === 0}
              variant="outline"
              size="sm"
            >
              <Play className="h-4 w-4 mr-2" />
              Retry Failed ({stats.failedTasks})
            </Button>
          </div>

          <div className="mt-4 text-sm text-gray-600">
            <p>Last updated: {lastUpdate.toLocaleTimeString()}</p>
            {autoRefresh && (
              <p>Auto-refresh: every {refreshInterval / 1000}s</p>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  // ============================================================================
  // MAIN RENDER
  // ============================================================================

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overview Cards */}
      {renderOverviewCards()}

      {/* Main Content */}
      {showDetailedStats ? (
        <Tabs defaultValue="progress" className="space-y-4">
          <TabsList>
            <TabsTrigger value="progress">Progress</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="management">Management</TabsTrigger>
          </TabsList>

          <TabsContent value="progress" className="space-y-4">
            {renderQueueProgress()}
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            {renderPerformanceMetrics()}
          </TabsContent>

          <TabsContent value="management" className="space-y-4">
            {renderQueueActions()}
          </TabsContent>
        </Tabs>
      ) : (
        <div className="space-y-4">
          {renderQueueProgress()}
          {renderQueueActions()}
        </div>
      )}
    </div>
  );
};

export default PhotoUploadProgressTracker;
