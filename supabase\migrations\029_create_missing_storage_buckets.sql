-- Create Missing Storage Buckets Migration
-- Migration 029: Ensure all required storage buckets exist for photo uploads

-- ============================================================================
-- STORAGE BUCKETS CREATION
-- ============================================================================

-- Create field-report-photos bucket for field report photos
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types, created_at, updated_at)
VALUES (
  'field-report-photos',
  'field-report-photos',
  true,
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic', 'image/heif'],
  NOW(),
  NOW()
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types,
  updated_at = NOW();

-- Create distribution-photos bucket for book distribution photos
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types, created_at, updated_at)
VALUES (
  'distribution-photos',
  'distribution-photos',
  true,
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic', 'image/heif'],
  NOW(),
  NOW()
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types,
  updated_at = NOW();

-- Create field-photos bucket (alternative/backup bucket)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types, created_at, updated_at)
VALUES (
  'field-photos',
  'field-photos',
  true,
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic', 'image/heif'],
  NOW(),
  NOW()
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types,
  updated_at = NOW();

-- Ensure general-files bucket exists (fallback bucket)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types, created_at, updated_at)
VALUES (
  'general-files',
  'general-files',
  true,
  52428800, -- 50MB limit for general files
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic', 'image/heif', 'application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  NOW(),
  NOW()
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types,
  updated_at = NOW();

-- ============================================================================
-- STORAGE POLICIES
-- ============================================================================

-- Policy for field-report-photos bucket
-- Allow authenticated users to upload photos
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Allow authenticated users to upload field report photos" ON storage.objects;
  DROP POLICY IF EXISTS "Allow public read access to field report photos" ON storage.objects;
  DROP POLICY IF EXISTS "Allow users to delete their own field report photos" ON storage.objects;

  -- Create upload policy for field-report-photos
  CREATE POLICY "Allow authenticated users to upload field report photos"
  ON storage.objects FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'field-report-photos');

  -- Create read policy for field-report-photos
  CREATE POLICY "Allow public read access to field report photos"
  ON storage.objects FOR SELECT
  TO public
  USING (bucket_id = 'field-report-photos');

  -- Create delete policy for field-report-photos
  CREATE POLICY "Allow users to delete their own field report photos"
  ON storage.objects FOR DELETE
  TO authenticated
  USING (bucket_id = 'field-report-photos' AND auth.uid()::text = (storage.foldername(name))[1]);

EXCEPTION
  WHEN duplicate_object THEN
    -- Policy already exists, skip
    NULL;
END $$;

-- Policy for distribution-photos bucket
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Allow authenticated users to upload distribution photos" ON storage.objects;
  DROP POLICY IF EXISTS "Allow public read access to distribution photos" ON storage.objects;
  DROP POLICY IF EXISTS "Allow users to delete their own distribution photos" ON storage.objects;

  -- Create upload policy for distribution-photos
  CREATE POLICY "Allow authenticated users to upload distribution photos"
  ON storage.objects FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'distribution-photos');

  -- Create read policy for distribution-photos
  CREATE POLICY "Allow public read access to distribution photos"
  ON storage.objects FOR SELECT
  TO public
  USING (bucket_id = 'distribution-photos');

  -- Create delete policy for distribution-photos
  CREATE POLICY "Allow users to delete their own distribution photos"
  ON storage.objects FOR DELETE
  TO authenticated
  USING (bucket_id = 'distribution-photos' AND auth.uid()::text = (storage.foldername(name))[1]);

EXCEPTION
  WHEN duplicate_object THEN
    -- Policy already exists, skip
    NULL;
END $$;

-- Policy for field-photos bucket
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Allow authenticated users to upload field photos" ON storage.objects;
  DROP POLICY IF EXISTS "Allow public read access to field photos" ON storage.objects;
  DROP POLICY IF EXISTS "Allow users to delete their own field photos" ON storage.objects;

  -- Create upload policy for field-photos
  CREATE POLICY "Allow authenticated users to upload field photos"
  ON storage.objects FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'field-photos');

  -- Create read policy for field-photos
  CREATE POLICY "Allow public read access to field photos"
  ON storage.objects FOR SELECT
  TO public
  USING (bucket_id = 'field-photos');

  -- Create delete policy for field-photos
  CREATE POLICY "Allow users to delete their own field photos"
  ON storage.objects FOR DELETE
  TO authenticated
  USING (bucket_id = 'field-photos' AND auth.uid()::text = (storage.foldername(name))[1]);

EXCEPTION
  WHEN duplicate_object THEN
    -- Policy already exists, skip
    NULL;
END $$;

-- Policy for general-files bucket
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Allow authenticated users to upload general files" ON storage.objects;
  DROP POLICY IF EXISTS "Allow public read access to general files" ON storage.objects;
  DROP POLICY IF EXISTS "Allow users to delete their own general files" ON storage.objects;

  -- Create upload policy for general-files
  CREATE POLICY "Allow authenticated users to upload general files"
  ON storage.objects FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'general-files');

  -- Create read policy for general-files
  CREATE POLICY "Allow public read access to general files"
  ON storage.objects FOR SELECT
  TO public
  USING (bucket_id = 'general-files');

  -- Create delete policy for general-files
  CREATE POLICY "Allow users to delete their own general files"
  ON storage.objects FOR DELETE
  TO authenticated
  USING (bucket_id = 'general-files' AND auth.uid()::text = (storage.foldername(name))[1]);

EXCEPTION
  WHEN duplicate_object THEN
    -- Policy already exists, skip
    NULL;
END $$;

-- ============================================================================
-- BUCKET VERIFICATION FUNCTION
-- ============================================================================

-- Function to verify bucket existence and configuration
CREATE OR REPLACE FUNCTION verify_storage_buckets()
RETURNS TABLE (
  bucket_name TEXT,
  exists BOOLEAN,
  is_public BOOLEAN,
  file_size_limit BIGINT,
  allowed_mime_types TEXT[]
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    bucket_info.name,
    bucket_info.name IS NOT NULL,
    COALESCE(bucket_info.public, false),
    COALESCE(bucket_info.file_size_limit, 0),
    COALESCE(bucket_info.allowed_mime_types, ARRAY[]::TEXT[])
  FROM (
    VALUES 
      ('field-report-photos'),
      ('distribution-photos'),
      ('field-photos'),
      ('general-files')
  ) AS required_buckets(name)
  LEFT JOIN storage.buckets bucket_info ON bucket_info.id = required_buckets.name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION verify_storage_buckets() TO authenticated;

-- ============================================================================
-- BUCKET CLEANUP FUNCTION
-- ============================================================================

-- Function to clean up old files in buckets (for maintenance)
CREATE OR REPLACE FUNCTION cleanup_old_bucket_files(
  p_bucket_name TEXT,
  p_days_old INTEGER DEFAULT 365
)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER := 0;
BEGIN
  -- Only allow cleanup for specific buckets
  IF p_bucket_name NOT IN ('field-report-photos', 'distribution-photos', 'field-photos', 'general-files') THEN
    RAISE EXCEPTION 'Cleanup not allowed for bucket: %', p_bucket_name;
  END IF;

  -- Delete files older than specified days
  DELETE FROM storage.objects
  WHERE bucket_id = p_bucket_name
    AND created_at < NOW() - INTERVAL '%s days' % p_days_old;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to service role only
GRANT EXECUTE ON FUNCTION cleanup_old_bucket_files(TEXT, INTEGER) TO service_role;

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- Log bucket creation results
DO $$
DECLARE
  bucket_record RECORD;
BEGIN
  RAISE NOTICE 'Storage buckets verification:';
  
  FOR bucket_record IN 
    SELECT * FROM verify_storage_buckets()
  LOOP
    IF bucket_record.exists THEN
      RAISE NOTICE 'Bucket "%" exists: Public=%, Size Limit=%MB, MIME Types=%', 
        bucket_record.bucket_name,
        bucket_record.is_public,
        bucket_record.file_size_limit / 1048576,
        array_to_string(bucket_record.allowed_mime_types, ', ');
    ELSE
      RAISE WARNING 'Bucket "%" does not exist!', bucket_record.bucket_name;
    END IF;
  END LOOP;
END $$;
