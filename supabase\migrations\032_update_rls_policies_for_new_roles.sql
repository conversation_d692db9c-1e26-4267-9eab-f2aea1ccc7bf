-- Migration: Update RLS policies to include new user roles
-- Description: Update all Row Level Security policies to include the new roles (staff, partner, accountant, social_media_manager)
-- Date: 2025-01-14

-- Update RLS policies for tasks table
DROP POLICY IF EXISTS "Users can view tasks assigned to them or created by them" ON tasks;
CREATE POLICY "Users can view tasks assigned to them or created by them" ON tasks
    FOR SELECT USING (
        assigned_to = auth.uid() OR 
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

DROP POLICY IF EXISTS "Users can create tasks" ON tasks;
CREATE POLICY "Users can create tasks" ON tasks
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager')
        )
    );

DROP POLICY IF EXISTS "Users can update their own tasks or assigned tasks" ON tasks;
CREATE POLICY "Users can update their own tasks or assigned tasks" ON tasks
    FOR UPDATE USING (
        assigned_to = auth.uid() OR 
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- Update RLS policies for user_invitations table
DROP POLICY IF EXISTS "Admin and program officers can manage invitations" ON user_invitations;
CREATE POLICY "Admin and program officers can manage invitations" ON user_invitations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'program_officer')
    )
  );

-- Update RLS policies for attendance_sessions table
DROP POLICY IF EXISTS "Users can view sessions from their schools" ON attendance_sessions;
CREATE POLICY "Users can view sessions from their schools" ON attendance_sessions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        facilitator_id = auth.uid() OR
        school_id IN (
            SELECT school_id FROM tasks
            WHERE assigned_to = auth.uid() OR created_by = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Staff can create sessions" ON attendance_sessions;
CREATE POLICY "Staff can create sessions" ON attendance_sessions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager')
        )
    );

-- Update RLS policies for student_attendance table
DROP POLICY IF EXISTS "Users can view attendance from their schools" ON student_attendance;
CREATE POLICY "Users can view attendance from their schools" ON student_attendance
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        recorded_by = auth.uid() OR
        school_id IN (
            SELECT school_id FROM tasks
            WHERE assigned_to = auth.uid() OR created_by = auth.uid()
        ) OR
        session_id IN (
            SELECT id FROM attendance_sessions
            WHERE facilitator_id = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Staff can record attendance" ON student_attendance;
CREATE POLICY "Staff can record attendance" ON student_attendance
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager')
        )
    );

-- Update RLS policies for field_staff_attendance table
DROP POLICY IF EXISTS "Staff can view their own attendance records" ON field_staff_attendance;
CREATE POLICY "Staff can view their own attendance records" ON field_staff_attendance
    FOR SELECT USING (
        staff_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

DROP POLICY IF EXISTS "Staff can create their own attendance records" ON field_staff_attendance;
CREATE POLICY "Staff can create their own attendance records" ON field_staff_attendance
    FOR INSERT WITH CHECK (
        staff_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager')
        )
    );

-- Update RLS policies for field_reports table
DROP POLICY IF EXISTS "Staff can view their own reports" ON field_reports;
CREATE POLICY "Staff can view their own reports" ON field_reports
    FOR SELECT USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

DROP POLICY IF EXISTS "Staff can create reports" ON field_reports;
CREATE POLICY "Staff can create reports" ON field_reports
    FOR INSERT WITH CHECK (
        created_by = auth.uid() AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager')
        )
    );

-- Update RLS policies for daily_timesheets table
DROP POLICY IF EXISTS "System can manage timesheets" ON daily_timesheets;
CREATE POLICY "System can manage timesheets" ON daily_timesheets
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager')
        )
    );

-- Update RLS policies for field_staff_analytics table
DROP POLICY IF EXISTS "Staff can view their own analytics" ON field_staff_analytics;
CREATE POLICY "Staff can view their own analytics" ON field_staff_analytics
    FOR SELECT USING (
        staff_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- Update RLS policies for ilead_assessments table
DROP POLICY IF EXISTS "Users can view assessments from their schools" ON ilead_assessments;
CREATE POLICY "Users can view assessments from their schools" ON ilead_assessments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        created_by = auth.uid() OR
        school_id IN (
            SELECT school_id FROM tasks
            WHERE assigned_to = auth.uid() OR created_by = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Users can insert assessments" ON ilead_assessments;
CREATE POLICY "Users can insert assessments" ON ilead_assessments
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager')
        )
    );

-- Update RLS policies for participant_responses table
DROP POLICY IF EXISTS "Users can view participant responses based on role" ON participant_responses;
CREATE POLICY "Users can view participant responses based on role" ON participant_responses
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid()
            AND (p.role = 'admin' OR p.role = 'program_officer' OR completed_by = auth.uid())
        )
    );

DROP POLICY IF EXISTS "Users can create participant responses" ON participant_responses;
CREATE POLICY "Users can create participant responses" ON participant_responses
    FOR INSERT WITH CHECK (
        completed_by = auth.uid() AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager')
        )
    );

-- Update RLS policies for activities table (activity feed)
DROP POLICY IF EXISTS "Users can view relevant activities" ON activities;
CREATE POLICY "Users can view relevant activities" ON activities
    FOR SELECT USING (
        -- Users can see their own activities
        user_id = auth.uid() OR
        -- Users can see activities for tasks they're involved in
        (entity_type = 'task' AND EXISTS (
            SELECT 1 FROM tasks
            WHERE id = activities.entity_id AND (
                assigned_to = auth.uid() OR
                created_by = auth.uid()
            )
        )) OR
        -- Admin and program officers can see all activities
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

DROP POLICY IF EXISTS "Users can create activities" ON activities;
CREATE POLICY "Users can create activities" ON activities
    FOR INSERT WITH CHECK (
        user_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager')
        )
    );

-- Update RLS policies for profiles table (if not already updated)
DROP POLICY IF EXISTS "Users can view their own profile and admins can view all" ON profiles;
CREATE POLICY "Users can view their own profile and admins can view all" ON profiles
    FOR SELECT USING (
        id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() AND p.role IN ('admin', 'program_officer')
        )
    );

DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
CREATE POLICY "Users can update their own profile" ON profiles
    FOR UPDATE USING (
        id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() AND p.role IN ('admin', 'program_officer')
        )
    );

-- Update RLS policies for schools table (if exists)
DROP POLICY IF EXISTS "Users can view schools based on role" ON schools;
CREATE POLICY "Users can view schools based on role" ON schools
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager')
        )
    );

-- Update RLS policies for books table (accountants get special access)
DROP POLICY IF EXISTS "Users can view books based on role" ON books;
CREATE POLICY "Users can view books based on role" ON books
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager')
        )
    );

DROP POLICY IF EXISTS "Users can manage books based on role" ON books;
CREATE POLICY "Users can manage books based on role" ON books
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'accountant')
        )
    );

-- Update RLS policies for book_inventory table (accountants get special access)
DROP POLICY IF EXISTS "Users can view book inventory based on role" ON book_inventory;
CREATE POLICY "Users can view book inventory based on role" ON book_inventory
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager')
        )
    );

DROP POLICY IF EXISTS "Users can manage book inventory based on role" ON book_inventory;
CREATE POLICY "Users can manage book inventory based on role" ON book_inventory
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'accountant')
        )
    );

-- Update RLS policies for book_distributions table (accountants get special access)
DROP POLICY IF EXISTS "Users can view book distributions based on role" ON book_distributions;
CREATE POLICY "Users can view book distributions based on role" ON book_distributions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager')
        )
    );

DROP POLICY IF EXISTS "Users can manage book distributions based on role" ON book_distributions;
CREATE POLICY "Users can manage book distributions based on role" ON book_distributions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'accountant')
        )
    );

-- Add comment for documentation
COMMENT ON MIGRATION '032_update_rls_policies_for_new_roles' IS 'Updated all RLS policies to include new user roles: staff, partner, accountant, social_media_manager. These roles inherit field_staff permissions with accountant having additional book management access.';
