import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart3,
  RefreshCw,
  Settings,
  Bell,
  Filter,
  Calendar,
  Users,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { PageLayout, PageHeader } from '@/components/layout';
import { useAuth } from '@/hooks/useAuth';
import { useAccessControl } from '@/hooks/useAccessControl';
import { useToast } from '@/hooks/use-toast';
import { 
  useDashboardMetrics, 
  useActivitySummary, 
  useStaffPerformance 
} from '@/hooks/dashboard/useDashboardMetrics';
import { ExecutiveSummaryCards } from './ExecutiveSummaryCards';
import { RealTimeActivityMonitor } from './RealTimeActivityMonitor';
import { PerformanceTrends } from './PerformanceTrends';
import { ImpactIndicators } from './ImpactIndicators';

interface ComprehensiveAdminDashboardProps {
  onViewChange?: (view: string) => void;
}

const QuickActionButton: React.FC<{
  icon: React.ElementType;
  label: string;
  onClick: () => void;
  variant?: 'default' | 'destructive' | 'outline';
}> = ({ icon: Icon, label, onClick, variant = 'outline' }) => (
  <Button variant={variant} onClick={onClick} className="flex-1">
    <Icon className="h-4 w-4 mr-2" />
    {label}
  </Button>
);

const SystemStatusIndicator: React.FC<{
  isOnline: boolean;
  lastUpdate: Date;
}> = ({ isOnline, lastUpdate }) => (
  <div className="flex items-center space-x-2 text-sm">
    <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-500' : 'bg-red-500'}`} />
    <span className="text-gray-600">
      {isOnline ? 'Live' : 'Offline'} • Updated {lastUpdate.toLocaleTimeString()}
    </span>
  </div>
);

export const ComprehensiveAdminDashboard: React.FC<ComprehensiveAdminDashboardProps> = ({
  onViewChange
}) => {
  const { profile, loading } = useAuth();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');
  const [lastRefresh, setLastRefresh] = useState(new Date());

  // Always call useAccessControl hook - never conditionally
  const { roleChecker } = useAccessControl();

  // Data hooks
  const {
    data: metrics,
    isLoading: metricsLoading,
    refetch: refetchMetrics
  } = useDashboardMetrics();

  const {
    data: activitySummary,
    isLoading: activityLoading,
    refetch: refetchActivity
  } = useActivitySummary();

  const {
    data: staffPerformance,
    isLoading: staffLoading,
    refetch: refetchStaff
  } = useStaffPerformance();

  const isLoading = metricsLoading || activityLoading || staffLoading;

  // Show loading state while authentication is being resolved
  if (loading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-ilead-green mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Admin Dashboard</h3>
            <p className="text-gray-600">Verifying access permissions...</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  const handleRefreshAll = async () => {
    try {
      await Promise.all([
        refetchMetrics(),
        refetchActivity(),
        refetchStaff()
      ]);
      setLastRefresh(new Date());
      toast({
        title: "Dashboard Updated",
        description: "All data has been refreshed successfully",
      });
    } catch (error) {
      toast({
        title: "Refresh Failed",
        description: "Failed to update dashboard data",
        variant: "destructive",
      });
    }
  };



  const handleViewDetails = (section: string) => {
    const viewMap: Record<string, string> = {
      'progress': 'impact',
      'staff': 'staff-management',
      'schools': 'schools',
      'reports': 'field-visits',
      'tasks': 'tasks',
      'distributions': 'books',
    };

    const targetView = viewMap[section];
    if (targetView && onViewChange) {
      onViewChange(targetView);
    }
  };

  // Show loading state while authentication is being resolved
  if (loading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-ilead-green mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Admin Dashboard</h3>
            <p className="text-gray-600">Verifying access permissions...</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Check if user has admin access (only after loading is complete)
  if (!roleChecker.isAdminOrProgramOfficer()) {
    return (
      <PageLayout>
        <PageHeader
          title="Access Denied"
          description="You don't have permission to view the admin dashboard"
          icon={AlertTriangle}
        />
        <Card>
          <CardContent className="text-center py-8">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-yellow-500" />
            <h3 className="text-lg font-medium mb-2">Admin Access Required</h3>
            <p className="text-gray-600">
              This dashboard is only available to administrators and program officers.
            </p>
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <PageHeader
        title="Admin Dashboard"
        description="Comprehensive overview of field activities and program performance"
        icon={BarChart3}
        actions={[
          {
            label: 'Refresh',
            onClick: handleRefreshAll,
            icon: RefreshCw,
            disabled: isLoading,
          },
          {
            label: 'Settings',
            onClick: () => onViewChange?.('settings'),
            icon: Settings,
            variant: 'outline',
          },
        ]}
      />

      {/* System Status Bar */}
      <Card className="mb-6">
        <CardContent className="py-3">
          <div className="flex items-center justify-between">
            <SystemStatusIndicator 
              isOnline={navigator.onLine} 
              lastUpdate={lastRefresh} 
            />
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Users className="h-4 w-4" />
                <span>{staffPerformance?.filter(s => s.isCheckedIn).length || 0} Active Staff</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Executive Summary */}
      {metrics && (
        <div className="mb-6">
          <ExecutiveSummaryCards metrics={metrics} isLoading={isLoading} />
        </div>
      )}

      {/* Quick Actions */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <QuickActionButton
              icon={Users}
              label="View Staff"
              onClick={() => handleViewDetails('staff')}
            />
            <QuickActionButton
              icon={BarChart3}
              label="Field Reports"
              onClick={() => handleViewDetails('reports')}
            />
            <QuickActionButton
              icon={Calendar}
              label="Manage Tasks"
              onClick={() => handleViewDetails('tasks')}
            />
            <QuickActionButton
              icon={CheckCircle}
              label="Impact Overview"
              onClick={() => handleViewDetails('progress')}
            />
          </div>
        </CardContent>
      </Card>

      {/* Main Dashboard Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Real-time Activity */}
            <div className="lg:col-span-1">
              {activitySummary && (
                <RealTimeActivityMonitor
                  activitySummary={activitySummary}
                  isLoading={isLoading}
                  onRefresh={handleRefreshAll}
                />
              )}
            </div>
            
            {/* Right Columns - Impact Indicators */}
            <div className="lg:col-span-2">
              {metrics && (
                <ImpactIndicators
                  metrics={metrics}
                  isLoading={isLoading}
                  onViewDetails={handleViewDetails}
                />
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {activitySummary && (
            <PerformanceTrends
              activitySummary={activitySummary}
              isLoading={isLoading}
            />
          )}
        </TabsContent>


      </Tabs>
    </PageLayout>
  );
};
