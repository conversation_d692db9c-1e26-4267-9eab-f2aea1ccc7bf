import { z } from 'zod';

/**
 * Validation result interface
 */
export interface ValidationResult<T = unknown> {
  success: boolean;
  data?: T;
  errors?: ValidationError[];
  fieldErrors?: Record<string, string>;
}

/**
 * Validation error interface
 */
export interface ValidationError {
  field?: string;
  message: string;
  code?: string;
}

/**
 * Form validation state
 */
export interface FormValidationState {
  isValid: boolean;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
}

/**
 * Validate data against a Zod schema
 */
export function validateData<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): ValidationResult<T> {
  try {
    const result = schema.safeParse(data);
    
    if (result.success) {
      return {
        success: true,
        data: result.data,
      };
    }
    
    const errors: ValidationError[] = [];
    const fieldErrors: Record<string, string> = {};
    
    result.error.errors.forEach((error) => {
      const field = error.path.join('.');
      const message = error.message;
      
      errors.push({ field, message, code: error.code });
      fieldErrors[field] = message;
    });
    
    return {
      success: false,
      errors,
      fieldErrors,
    };
  } catch (error) {
    return {
      success: false,
      errors: [{ message: 'Validation failed due to an unexpected error' }],
    };
  }
}

/**
 * Validate a single field
 */
export function validateField<T>(
  schema: z.ZodSchema<T>,
  fieldName: string,
  value: unknown
): { isValid: boolean; error?: string } {
  try {
    // Create a partial schema for the specific field
    const fieldSchema = schema.pick({ [fieldName]: true } as Record<string, true>);
    const result = fieldSchema.safeParse({ [fieldName]: value });
    
    if (result.success) {
      return { isValid: true };
    }
    
    const fieldError = result.error.errors.find(
      (error) => error.path.join('.') === fieldName
    );
    
    return {
      isValid: false,
      error: fieldError?.message || 'Invalid value',
    };
  } catch (error) {
    return {
      isValid: false,
      error: 'Validation error',
    };
  }
}

/**
 * Async validation function type
 */
export type AsyncValidator<T> = (value: T) => Promise<ValidationResult>;

/**
 * Combine multiple validation results
 */
export function combineValidationResults(
  ...results: ValidationResult[]
): ValidationResult {
  const allErrors: ValidationError[] = [];
  const allFieldErrors: Record<string, string> = {};
  let hasSuccess = false;
  let combinedData: Record<string, unknown> = {};
  
  results.forEach((result) => {
    if (result.success) {
      hasSuccess = true;
      if (result.data) {
        combinedData = { ...combinedData, ...result.data };
      }
    } else {
      if (result.errors) {
        allErrors.push(...result.errors);
      }
      if (result.fieldErrors) {
        Object.assign(allFieldErrors, result.fieldErrors);
      }
    }
  });
  
  return {
    success: allErrors.length === 0,
    data: hasSuccess ? combinedData : undefined,
    errors: allErrors.length > 0 ? allErrors : undefined,
    fieldErrors: Object.keys(allFieldErrors).length > 0 ? allFieldErrors : undefined,
  };
}

/**
 * Create a validation function from a schema
 */
export function createValidator<T>(schema: z.ZodSchema<T>) {
  return (data: unknown): ValidationResult<T> => {
    return validateData(schema, data);
  };
}

/**
 * Create an async validation function
 */
export function createAsyncValidator<T>(
  schema: z.ZodSchema<T>,
  asyncValidators: AsyncValidator<T>[] = []
) {
  return async (data: unknown): Promise<ValidationResult<T>> => {
    // First, run synchronous validation
    const syncResult = validateData(schema, data);
    
    if (!syncResult.success) {
      return syncResult;
    }
    
    // Then run async validators
    const asyncResults = await Promise.all(
      asyncValidators.map((validator) => validator(syncResult.data!))
    );
    
    return combineValidationResults(syncResult, ...asyncResults);
  };
}

/**
 * Validation utilities for common patterns
 */
export const validationUtils = {
  /**
   * Check if email is unique (example async validator)
   */
  isEmailUnique: async (email: string, excludeId?: string): Promise<ValidationResult> => {
    // This would typically make an API call
    // For now, return success
    return { success: true };
  },
  
  /**
   * Check if school code is unique
   */
  isSchoolCodeUnique: async (code: string, excludeId?: string): Promise<ValidationResult> => {
    // This would typically make an API call
    return { success: true };
  },
  
  /**
   * Validate file upload
   */
  validateFile: (
    file: File,
    options: {
      maxSize?: number;
      allowedTypes?: string[];
      maxFiles?: number;
    } = {}
  ): ValidationResult => {
    const { maxSize = 5 * 1024 * 1024, allowedTypes = [] } = options;
    
    if (file.size > maxSize) {
      return {
        success: false,
        errors: [{ message: `File size must be less than ${maxSize / 1024 / 1024}MB` }],
      };
    }
    
    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
      return {
        success: false,
        errors: [{ message: `File type ${file.type} is not allowed` }],
      };
    }
    
    return { success: true };
  },
  
  /**
   * Validate date range
   */
  validateDateRange: (startDate: string, endDate: string): ValidationResult => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (start > end) {
      return {
        success: false,
        errors: [{ message: 'Start date must be before end date' }],
        fieldErrors: { endDate: 'End date must be after start date' },
      };
    }
    
    return { success: true };
  },
  
  /**
   * Validate numeric range
   */
  validateNumericRange: (
    min: number,
    max: number,
    fieldName = 'value'
  ): ValidationResult => {
    if (min > max) {
      return {
        success: false,
        errors: [{ message: 'Minimum value cannot be greater than maximum value' }],
        fieldErrors: { [fieldName]: 'Invalid range' },
      };
    }
    
    return { success: true };
  },
};

/**
 * Error formatting utilities
 */
export const errorUtils = {
  /**
   * Format validation errors for display
   */
  formatErrors: (errors: ValidationError[]): string[] => {
    return errors.map((error) => {
      if (error.field) {
        return `${error.field}: ${error.message}`;
      }
      return error.message;
    });
  },
  
  /**
   * Get first error for a field
   */
  getFieldError: (
    fieldErrors: Record<string, string> | undefined,
    fieldName: string
  ): string | undefined => {
    return fieldErrors?.[fieldName];
  },
  
  /**
   * Check if field has error
   */
  hasFieldError: (
    fieldErrors: Record<string, string> | undefined,
    fieldName: string
  ): boolean => {
    return Boolean(fieldErrors?.[fieldName]);
  },
};

// Re-export schemas for convenience
export { validationSchemas } from './schemas';
export type { UserRole } from '@/utils/rbac';
