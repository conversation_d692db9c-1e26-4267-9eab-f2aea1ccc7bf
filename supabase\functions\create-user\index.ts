import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface CreateUserRequest {
  email: string;
  name: string;
  role: string;
  division_id?: string;
  phone?: string;
}

interface CreateUserResponse {
  success: boolean;
  user?: {
    id: string;
    email: string;
    name: string;
    role: string;
    tempPassword: string;
  };
  error?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('🔧 create-user function called')
    console.log('Method:', req.method)
    console.log('Headers:', Object.fromEntries(req.headers.entries()))

    // Only allow POST requests
    if (req.method !== 'POST') {
      console.log('❌ Method not allowed:', req.method)
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Initialize Supabase admin client
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('✅ Supabase admin client initialized')

    // Get the authorization header
    const authHeader = req.headers.get('authorization')
    console.log('Auth header present:', !!authHeader)

    if (!authHeader) {
      console.log('❌ No authorization header')
      return new Response(
        JSON.stringify({ error: 'Authorization header required' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Verify the user's JWT token using admin client
    const token = authHeader.replace('Bearer ', '')
    console.log('Token length:', token.length)

    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)
    console.log('Auth result - User:', !!user, 'Error:', !!authError)
    
    if (authError || !user) {
      console.log('❌ Auth verification failed:', authError?.message)
      return new Response(
        JSON.stringify({
          error: 'Invalid authorization token',
          details: authError?.message
        }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log('✅ User authenticated:', user.email)

    // Get the user's profile to check permissions (use admin client for reliable access)
    const { data: userProfile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !userProfile) {
      return new Response(
        JSON.stringify({ error: 'Unable to verify user permissions' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body
    const requestData: CreateUserRequest = await req.json()

    // Validate required fields
    if (!requestData.email || !requestData.name || !requestData.role) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: email, name, role' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Validate permissions
    const hasPermission = await validatePermissions(userProfile.role, requestData.role)
    if (!hasPermission) {
      return new Response(
        JSON.stringify({ error: 'Insufficient permissions to create user with this role' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check for duplicates
    const exists = await checkUserExists(supabaseAdmin, requestData.email)
    if (exists) {
      return new Response(
        JSON.stringify({ error: `User with email ${requestData.email} already exists` }),
        { status: 409, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Generate secure password
    const tempPassword = generateSecurePassword()

    // Create auth user
    const { data: authData, error: authUserError } = await supabaseAdmin.auth.admin.createUser({
      email: requestData.email.toLowerCase(),
      password: tempPassword,
      email_confirm: true,
    })

    if (authUserError || !authData.user) {
      return new Response(
        JSON.stringify({ 
          error: `Failed to create auth user: ${authUserError?.message || 'Unknown error'}` 
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create profile
    const { error: profileInsertError } = await supabaseAdmin
      .from('profiles')
      .insert({
        id: authData.user.id,
        email: requestData.email.toLowerCase(),
        name: requestData.name,
        role: requestData.role,
        division_id: requestData.division_id || null,
        phone: requestData.phone || null,
        country: 'Uganda',
        is_active: true,
      })

    if (profileInsertError) {
      // Cleanup auth user if profile creation fails
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id)
      return new Response(
        JSON.stringify({ error: `Failed to create profile: ${profileInsertError.message}` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const response: CreateUserResponse = {
      success: true,
      user: {
        id: authData.user.id,
        email: requestData.email,
        name: requestData.name,
        role: requestData.role,
        tempPassword
      }
    }

    return new Response(
      JSON.stringify(response),
      { status: 201, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in create-user function:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

// Validate user creation permissions
async function validatePermissions(creatorRole: string, targetRole: string): Promise<boolean> {
  // Admins can create any role
  if (creatorRole === 'admin') return true

  // Program officers can only create field-level roles
  if (creatorRole === 'program_officer') {
    const allowedRoles = ['staff', 'field_staff', 'partner', 'accountant', 'social_media_manager']
    return allowedRoles.includes(targetRole)
  }

  return false
}

// Generate secure password
function generateSecurePassword(): string {
  const length = 16
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const lowercase = 'abcdefghijklmnopqrstuvwxyz'
  const numbers = '**********'
  const symbols = '!@#$%^&*'
  const allChars = uppercase + lowercase + numbers + symbols
  
  let password = ''
  
  // Ensure at least one character from each category
  password += uppercase[Math.floor(Math.random() * uppercase.length)]
  password += lowercase[Math.floor(Math.random() * lowercase.length)]
  password += numbers[Math.floor(Math.random() * numbers.length)]
  password += symbols[Math.floor(Math.random() * symbols.length)]
  
  // Fill remaining length
  for (let i = 4; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)]
  }
  
  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('')
}

// Check if user already exists
async function checkUserExists(supabaseAdmin: any, email: string): Promise<boolean> {
  const { data } = await supabaseAdmin
    .from('profiles')
    .select('id')
    .eq('email', email.toLowerCase())
    .single()
    
  return !!data
}
