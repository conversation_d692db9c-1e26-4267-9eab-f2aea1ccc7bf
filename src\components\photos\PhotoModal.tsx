/**
 * Photo Modal Component
 * Full-size photo viewing with navigation and controls
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  X, 
  ChevronLeft, 
  ChevronRight, 
  Download, 
  ZoomIn, 
  ZoomOut, 
  RotateCw,
  AlertCircle,
  Loader2,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { toast } from 'sonner';
import { usePhotoValidation } from '@/hooks/usePhotoValidation';

// ============================================================================
// TYPES
// ============================================================================

export interface PhotoModalProps {
  photos: string[];
  initialIndex?: number;
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  showNavigation?: boolean;
  showControls?: boolean;
  allowDownload?: boolean;
}

// ============================================================================
// PHOTO MODAL COMPONENT
// ============================================================================

export const PhotoModal: React.FC<PhotoModalProps> = ({
  photos,
  initialIndex = 0,
  isOpen,
  onClose,
  title,
  showNavigation = true,
  showControls = true,
  allowDownload = true,
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  // Validate photos
  const { isPhotoValid, getValidationResult } = usePhotoValidation(photos, {
    enabled: isOpen,
    timeout: 5000,
  });

  const currentPhoto = photos[currentIndex];
  const validationResult = getValidationResult(currentPhoto);

  // Reset states when modal opens/closes or photo changes
  useEffect(() => {
    if (isOpen) {
      setCurrentIndex(Math.max(0, Math.min(initialIndex, photos.length - 1)));
      setZoom(1);
      setRotation(0);
      setImageLoading(true);
      setImageError(false);
    }
  }, [isOpen, initialIndex, photos.length]);

  useEffect(() => {
    setZoom(1);
    setRotation(0);
    setImageLoading(true);
    setImageError(false);
  }, [currentIndex]);

  // Navigation functions
  const goToPrevious = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  }, [currentIndex]);

  const goToNext = useCallback(() => {
    if (currentIndex < photos.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  }, [currentIndex, photos.length]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          event.preventDefault();
          goToPrevious();
          break;
        case 'ArrowRight':
          event.preventDefault();
          goToNext();
          break;
        case '+':
        case '=':
          event.preventDefault();
          setZoom(prev => Math.min(prev + 0.25, 3));
          break;
        case '-':
          event.preventDefault();
          setZoom(prev => Math.max(prev - 0.25, 0.25));
          break;
        case '0':
          event.preventDefault();
          setZoom(1);
          setRotation(0);
          break;
        case 'r':
        case 'R':
          event.preventDefault();
          setRotation(prev => (prev + 90) % 360);
          break;
        case 'f':
        case 'F':
          event.preventDefault();
          setIsFullscreen(prev => !prev);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose, goToPrevious, goToNext]);

  // Download function
  const downloadPhoto = useCallback(async () => {
    if (!currentPhoto) return;

    try {
      const response = await fetch(currentPhoto);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `photo_${currentIndex + 1}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      window.URL.revokeObjectURL(url);
      
      toast.success('Photo downloaded successfully');
    } catch (error) {
      console.error('Download failed:', error);
      toast.error('Failed to download photo');
    }
  }, [currentPhoto, currentIndex]);

  // Image load handlers
  const handleImageLoad = useCallback(() => {
    setImageLoading(false);
    setImageError(false);
  }, []);

  const handleImageError = useCallback(() => {
    setImageLoading(false);
    setImageError(true);
  }, []);

  if (!isOpen || photos.length === 0) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent 
        className={`max-w-none max-h-none p-0 bg-black/95 border-0 ${
          isFullscreen ? 'w-screen h-screen' : 'w-[90vw] h-[90vh]'
        }`}
        hideCloseButton
      >
        {/* Header */}
        <div className="absolute top-0 left-0 right-0 z-50 bg-black/50 backdrop-blur-sm">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              {title && (
                <h3 className="text-white font-medium">{title}</h3>
              )}
              <Badge variant="secondary" className="bg-white/20 text-white">
                {currentIndex + 1} of {photos.length}
              </Badge>
              {validationResult && !validationResult.isValid && (
                <Badge variant="destructive" className="flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Invalid
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              {showControls && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setZoom(prev => Math.max(prev - 0.25, 0.25))}
                    className="text-white hover:bg-white/20"
                    disabled={zoom <= 0.25}
                  >
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  
                  <span className="text-white text-sm min-w-[3rem] text-center">
                    {Math.round(zoom * 100)}%
                  </span>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setZoom(prev => Math.min(prev + 0.25, 3))}
                    className="text-white hover:bg-white/20"
                    disabled={zoom >= 3}
                  >
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setRotation(prev => (prev + 90) % 360)}
                    className="text-white hover:bg-white/20"
                  >
                    <RotateCw className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsFullscreen(prev => !prev)}
                    className="text-white hover:bg-white/20"
                  >
                    {isFullscreen ? (
                      <Minimize2 className="h-4 w-4" />
                    ) : (
                      <Maximize2 className="h-4 w-4" />
                    )}
                  </Button>
                </>
              )}
              
              {allowDownload && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={downloadPhoto}
                  className="text-white hover:bg-white/20"
                  disabled={imageError || !isPhotoValid(currentPhoto)}
                >
                  <Download className="h-4 w-4" />
                </Button>
              )}
              
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-white hover:bg-white/20"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="relative w-full h-full flex items-center justify-center overflow-hidden">
          {/* Navigation Buttons */}
          {showNavigation && photos.length > 1 && (
            <>
              <Button
                variant="ghost"
                size="lg"
                onClick={goToPrevious}
                disabled={currentIndex === 0}
                className="absolute left-4 z-40 text-white hover:bg-white/20 disabled:opacity-30"
              >
                <ChevronLeft className="h-8 w-8" />
              </Button>
              
              <Button
                variant="ghost"
                size="lg"
                onClick={goToNext}
                disabled={currentIndex === photos.length - 1}
                className="absolute right-4 z-40 text-white hover:bg-white/20 disabled:opacity-30"
              >
                <ChevronRight className="h-8 w-8" />
              </Button>
            </>
          )}

          {/* Image Container */}
          <div className="relative w-full h-full flex items-center justify-center">
            {imageLoading && (
              <div className="absolute inset-0 flex items-center justify-center">
                <Loader2 className="h-8 w-8 text-white animate-spin" />
              </div>
            )}
            
            {imageError ? (
              <div className="flex flex-col items-center justify-center text-white">
                <AlertCircle className="h-16 w-16 mb-4 opacity-50" />
                <p className="text-lg mb-2">Failed to load image</p>
                <p className="text-sm opacity-70">The image may have been moved or deleted</p>
                {validationResult?.error && (
                  <p className="text-xs opacity-50 mt-2">{validationResult.error}</p>
                )}
              </div>
            ) : (
              <img
                src={currentPhoto}
                alt={`Photo ${currentIndex + 1}`}
                className="max-w-full max-h-full object-contain transition-transform duration-200"
                style={{
                  transform: `scale(${zoom}) rotate(${rotation}deg)`,
                  transformOrigin: 'center',
                }}
                onLoad={handleImageLoad}
                onError={handleImageError}
              />
            )}
          </div>
        </div>

        {/* Footer with thumbnails */}
        {photos.length > 1 && (
          <div className="absolute bottom-0 left-0 right-0 z-50 bg-black/50 backdrop-blur-sm">
            <div className="flex items-center justify-center gap-2 p-4 overflow-x-auto">
              {photos.map((photo, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                    index === currentIndex 
                      ? 'border-white shadow-lg' 
                      : 'border-transparent opacity-60 hover:opacity-80'
                  }`}
                >
                  <img
                    src={photo}
                    alt={`Thumbnail ${index + 1}`}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </button>
              ))}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default PhotoModal;
