// Test the simple function to verify Edge Functions are working
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function testSimpleFunction() {
  console.log('🧪 Testing Simple Edge Function...\n');

  try {
    console.log('1. Testing simple function without auth...');
    
    const { data, error } = await supabase.functions.invoke('test-simple', {
      body: { test: 'data', timestamp: new Date().toISOString() }
    });

    if (error) {
      console.log('❌ Function error:', error);
      console.log('Error message:', error.message);
      console.log('Error details:', JSON.stringify(error, null, 2));
    } else {
      console.log('✅ Function success:', data);
    }

    // Also test with direct fetch
    console.log('\n2. Testing with direct fetch...');
    
    try {
      const response = await fetch('https://bygrspebofyofymivmib.supabase.co/functions/v1/test-simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': process.env.VITE_SUPABASE_ANON_KEY
        },
        body: JSON.stringify({ test: 'direct fetch', timestamp: new Date().toISOString() })
      });
      
      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);
      
      const responseText = await response.text();
      console.log('Response body:', responseText);
      
      if (response.ok) {
        try {
          const jsonData = JSON.parse(responseText);
          console.log('Parsed JSON:', jsonData);
        } catch (e) {
          console.log('Could not parse as JSON');
        }
      }
      
    } catch (fetchError) {
      console.log('❌ Fetch error:', fetchError.message);
    }

  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

// Run the test
testSimpleFunction().catch(console.error);
