import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';

type AttendanceSession = Database['public']['Tables']['attendance_sessions']['Row'];

interface SessionControlsResult {
  startSession: (sessionId: string) => Promise<void>;
  endSession: (sessionId: string) => Promise<void>;
  cancelSession: (sessionId: string, reason?: string) => Promise<void>;
  isStarting: boolean;
  isEnding: boolean;
  isCancelling: boolean;
}

export const useSessionControls = (): SessionControlsResult => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const startSessionMutation = useMutation({
    mutationFn: async (sessionId: string) => {
      const { data, error } = await supabase.rpc('start_attendance_session', {
        session_id: sessionId,
      });

      if (error) {
        console.error('Error starting session:', error);
        throw error;
      }

      return data as AttendanceSession;
    },
    onSuccess: (data) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['attendance-sessions'] });
      queryClient.invalidateQueries({ queryKey: ['session-details', data.id] });
      
      toast({
        title: "Session Started",
        description: "Attendance session has been started successfully",
      });
    },
    onError: (error: Error) => {
      console.error('Failed to start session:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to start session",
        variant: "destructive",
      });
    },
  });

  const endSessionMutation = useMutation({
    mutationFn: async (sessionId: string) => {
      const { data, error } = await supabase.rpc('end_attendance_session', {
        session_id: sessionId,
      });

      if (error) {
        console.error('Error ending session:', error);
        throw error;
      }

      return data as AttendanceSession;
    },
    onSuccess: (data) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['attendance-sessions'] });
      queryClient.invalidateQueries({ queryKey: ['session-details', data.id] });
      
      toast({
        title: "Session Ended",
        description: "Attendance session has been ended successfully",
      });
    },
    onError: (error: Error) => {
      console.error('Failed to end session:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to end session",
        variant: "destructive",
      });
    },
  });

  const cancelSessionMutation = useMutation({
    mutationFn: async ({ sessionId, reason }: { sessionId: string; reason?: string }) => {
      const { data, error } = await supabase.rpc('cancel_attendance_session', {
        session_id: sessionId,
        cancellation_reason: reason || null,
      });

      if (error) {
        console.error('Error cancelling session:', error);
        throw error;
      }

      return data as AttendanceSession;
    },
    onSuccess: (data) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['attendance-sessions'] });
      queryClient.invalidateQueries({ queryKey: ['session-details', data.id] });
      
      toast({
        title: "Session Cancelled",
        description: "Attendance session has been cancelled",
      });
    },
    onError: (error: Error) => {
      console.error('Failed to cancel session:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to cancel session",
        variant: "destructive",
      });
    },
  });

  const startSession = async (sessionId: string) => {
    await startSessionMutation.mutateAsync(sessionId);
  };

  const endSession = async (sessionId: string) => {
    await endSessionMutation.mutateAsync(sessionId);
  };

  const cancelSession = async (sessionId: string, reason?: string) => {
    await cancelSessionMutation.mutateAsync({ sessionId, reason });
  };

  return {
    startSession,
    endSession,
    cancelSession,
    isStarting: startSessionMutation.isPending,
    isEnding: endSessionMutation.isPending,
    isCancelling: cancelSessionMutation.isPending,
  };
};
