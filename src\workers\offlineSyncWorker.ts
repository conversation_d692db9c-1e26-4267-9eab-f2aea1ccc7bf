/**
 * Offline Sync Web Worker
 * Handles concurrent sync processing with priority queuing and batch operations
 */

import { createClient } from '@supabase/supabase-js';

// ============================================================================
// TYPES
// ============================================================================

export interface SyncOperation {
  id: string;
  type: 'CREATE' | 'UPDATE' | 'DELETE';
  table: string;
  data: Record<string, unknown>;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  dependencies?: string[]; // IDs of operations that must complete first
  metadata?: {
    userId?: string;
    deviceId?: string;
    version?: number;
    checksum?: string;
  };
}

export interface SyncResult {
  id: string;
  success: boolean;
  operation: SyncOperation;
  result?: unknown;
  error?: string;
  duration: number;
  timestamp: number;
}

export interface SyncBatch {
  id: string;
  operations: SyncOperation[];
  priority: number;
  estimatedDuration: number;
  dependencies: string[];
}

export interface WorkerMessage {
  type: 'SYNC_OPERATION' | 'SYNC_BATCH' | 'HEALTH_CHECK' | 'CONFIGURE' | 'CANCEL';
  payload: unknown;
}

export interface WorkerResponse {
  type: 'SYNC_RESULT' | 'SYNC_PROGRESS' | 'BATCH_COMPLETE' | 'ERROR' | 'HEALTH_RESPONSE';
  payload: unknown;
}

// ============================================================================
// SYNC PROCESSOR CLASS
// ============================================================================

class OfflineSyncProcessor {
  private supabase: ReturnType<typeof createClient> | null = null;
  private isConfigured = false;
  private activeOperations = new Map<string, SyncOperation>();
  private completedOperations = new Map<string, SyncResult>();
  private failedOperations = new Map<string, SyncResult>();
  private maxConcurrentOperations = 5;
  private conflictResolutionStrategy: 'CLIENT_WINS' | 'SERVER_WINS' | 'MERGE' = 'CLIENT_WINS';

  constructor() {
    // Supabase client will be initialized when configured
  }

  /**
   * Configure the sync processor with Supabase credentials
   */
  configure(config: { supabaseUrl: string; supabaseKey: string; options?: Record<string, unknown> }) {
    try {
      this.supabase = createClient(config.supabaseUrl, config.supabaseKey, {
        ...config.options,
        auth: {
          persistSession: false, // Worker doesn't need session persistence
        },
      });
      this.isConfigured = true;
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Configuration failed' 
      };
    }
  }

  /**
   * Process a single sync operation
   */
  async processSyncOperation(operation: SyncOperation): Promise<SyncResult> {
    const startTime = performance.now();
    
    if (!this.isConfigured) {
      throw new Error('Sync processor not configured');
    }

    try {
      this.activeOperations.set(operation.id, operation);
      
      // Send progress update
      this.sendProgress(operation.id, 'PROCESSING', 'Starting sync operation...');

      let result: unknown;
      
      switch (operation.type) {
        case 'CREATE':
          result = await this.handleCreate(operation);
          break;
        case 'UPDATE':
          result = await this.handleUpdate(operation);
          break;
        case 'DELETE':
          result = await this.handleDelete(operation);
          break;
        default:
          throw new Error(`Unknown operation type: ${operation.type}`);
      }

      const duration = performance.now() - startTime;
      const syncResult: SyncResult = {
        id: operation.id,
        success: true,
        operation,
        result,
        duration,
        timestamp: Date.now(),
      };

      this.activeOperations.delete(operation.id);
      this.completedOperations.set(operation.id, syncResult);
      
      this.sendProgress(operation.id, 'COMPLETE', 'Sync operation completed successfully');
      
      return syncResult;

    } catch (error) {
      const duration = performance.now() - startTime;
      const syncResult: SyncResult = {
        id: operation.id,
        success: false,
        operation,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration,
        timestamp: Date.now(),
      };

      this.activeOperations.delete(operation.id);
      
      // Retry logic
      if (operation.retryCount < operation.maxRetries) {
        operation.retryCount++;
        this.sendProgress(operation.id, 'RETRYING', `Retrying... (${operation.retryCount}/${operation.maxRetries})`);
        
        // Exponential backoff
        const delay = Math.min(1000 * Math.pow(2, operation.retryCount), 30000);
        setTimeout(() => {
          this.processSyncOperation(operation);
        }, delay);
        
        return syncResult;
      } else {
        this.failedOperations.set(operation.id, syncResult);
        this.sendProgress(operation.id, 'FAILED', `Sync failed: ${syncResult.error}`);
        return syncResult;
      }
    }
  }

  /**
   * Handle CREATE operations
   */
  private async handleCreate(operation: SyncOperation): Promise<unknown> {
    const { table, data } = operation;
    
    // Check for conflicts
    if (data.id) {
      const { data: existing } = await this.supabase
        .from(table)
        .select('*')
        .eq('id', data.id)
        .single();
      
      if (existing) {
        // Handle conflict based on strategy
        return this.resolveConflict(operation, existing);
      }
    }

    // Perform insert
    const { data: result, error } = await this.supabase
      .from(table)
      .insert(data)
      .select()
      .single();

    if (error) throw error;
    return result;
  }

  /**
   * Handle UPDATE operations
   */
  private async handleUpdate(operation: SyncOperation): Promise<unknown> {
    const { table, data } = operation;
    
    if (!data.id) {
      throw new Error('Update operation requires an ID');
    }

    // Get current server version
    const { data: serverData } = await this.supabase
      .from(table)
      .select('*')
      .eq('id', data.id)
      .single();

    if (!serverData) {
      throw new Error('Record not found on server');
    }

    // Check for conflicts
    if (this.hasConflict(operation, serverData)) {
      return this.resolveConflict(operation, serverData);
    }

    // Perform update
    const { data: result, error } = await this.supabase
      .from(table)
      .update(data)
      .eq('id', data.id)
      .select()
      .single();

    if (error) throw error;
    return result;
  }

  /**
   * Handle DELETE operations
   */
  private async handleDelete(operation: SyncOperation): Promise<unknown> {
    const { table, data } = operation;
    
    if (!data.id) {
      throw new Error('Delete operation requires an ID');
    }

    // Check if record exists
    const { data: existing } = await this.supabase
      .from(table)
      .select('id')
      .eq('id', data.id)
      .single();

    if (!existing) {
      // Record already deleted, consider it successful
      return { id: data.id, deleted: true };
    }

    // Perform delete
    const { error } = await this.supabase
      .from(table)
      .delete()
      .eq('id', data.id);

    if (error) throw error;
    return { id: data.id, deleted: true };
  }

  /**
   * Check for data conflicts
   */
  private hasConflict(operation: SyncOperation, serverData: Record<string, unknown>): boolean {
    const { metadata } = operation;
    
    if (!metadata?.version || !serverData.version) {
      return false; // No version tracking
    }

    return serverData.version > metadata.version;
  }

  /**
   * Resolve data conflicts
   */
  private async resolveConflict(operation: SyncOperation, serverData: Record<string, unknown>): Promise<unknown> {
    switch (this.conflictResolutionStrategy) {
      case 'CLIENT_WINS': {
        // Force update with client data
        const { data: clientResult, error: clientError } = await this.supabase
          .from(operation.table)
          .update(operation.data)
          .eq('id', operation.data.id)
          .select()
          .single();
        
        if (clientError) throw clientError;
        return clientResult;
      }

      case 'SERVER_WINS':
        // Return server data without updating
        return serverData;

      case 'MERGE': {
        // Merge client and server data
        const mergedData = this.mergeData(operation.data, serverData);
        const { data: mergeResult, error: mergeError } = await this.supabase
          .from(operation.table)
          .update(mergedData)
          .eq('id', operation.data.id)
          .select()
          .single();
        
        if (mergeError) throw mergeError;
        return mergeResult;
      }

      default:
        throw new Error(`Unknown conflict resolution strategy: ${this.conflictResolutionStrategy}`);
    }
  }

  /**
   * Merge client and server data
   */
  private mergeData(clientData: Record<string, unknown>, serverData: Record<string, unknown>): Record<string, unknown> {
    // Simple merge strategy - prefer client data for most fields
    // but keep server timestamps and version
    return {
      ...serverData,
      ...clientData,
      updated_at: new Date().toISOString(),
      version: (serverData.version || 0) + 1,
    };
  }

  /**
   * Process a batch of sync operations
   */
  async processSyncBatch(batch: SyncBatch): Promise<SyncResult[]> {
    const results: SyncResult[] = [];
    const { operations } = batch;

    // Sort operations by priority and dependencies
    const sortedOperations = this.sortOperationsByDependencies(operations);

    // Process operations with concurrency limit
    const chunks = this.chunkArray(sortedOperations, this.maxConcurrentOperations);
    
    for (const chunk of chunks) {
      const chunkPromises = chunk.map(op => this.processSyncOperation(op));
      const chunkResults = await Promise.allSettled(chunkPromises);
      
      chunkResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          // Create error result
          const operation = chunk[index];
          results.push({
            id: operation.id,
            success: false,
            operation,
            error: result.reason?.message || 'Unknown error',
            duration: 0,
            timestamp: Date.now(),
          });
        }
      });
    }

    return results;
  }

  /**
   * Sort operations by dependencies and priority
   */
  private sortOperationsByDependencies(operations: SyncOperation[]): SyncOperation[] {
    const sorted: SyncOperation[] = [];
    const remaining = [...operations];
    const completed = new Set<string>();

    while (remaining.length > 0) {
      const ready = remaining.filter(op => 
        !op.dependencies || op.dependencies.every(dep => completed.has(dep))
      );

      if (ready.length === 0) {
        // Circular dependency or missing dependency
        console.warn('Circular dependency detected, processing remaining operations');
        sorted.push(...remaining);
        break;
      }

      // Sort ready operations by priority
      ready.sort((a, b) => this.getPriorityValue(b.priority) - this.getPriorityValue(a.priority));
      
      ready.forEach(op => {
        sorted.push(op);
        completed.add(op.id);
        const index = remaining.indexOf(op);
        remaining.splice(index, 1);
      });
    }

    return sorted;
  }

  /**
   * Get numeric priority value
   */
  private getPriorityValue(priority: SyncOperation['priority']): number {
    const values = { CRITICAL: 4, HIGH: 3, MEDIUM: 2, LOW: 1 };
    return values[priority];
  }

  /**
   * Split array into chunks
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Send progress update to main thread
   */
  private sendProgress(operationId: string, stage: string, message: string) {
    self.postMessage({
      type: 'SYNC_PROGRESS',
      payload: {
        operationId,
        stage,
        message,
        timestamp: Date.now(),
      }
    });
  }

  /**
   * Get processor statistics
   */
  getStats() {
    return {
      active: this.activeOperations.size,
      completed: this.completedOperations.size,
      failed: this.failedOperations.size,
      isConfigured: this.isConfigured,
    };
  }
}

// ============================================================================
// WORKER MESSAGE HANDLING
// ============================================================================

const processor = new OfflineSyncProcessor();

self.onmessage = async (event: MessageEvent<WorkerMessage>) => {
  const { type, payload } = event.data;

  try {
    switch (type) {
      case 'CONFIGURE': {
        const configResult = processor.configure(payload);
        self.postMessage({
          type: 'HEALTH_RESPONSE',
          payload: configResult
        });
        break;
      }

      case 'SYNC_OPERATION': {
        const result = await processor.processSyncOperation(payload);
        self.postMessage({
          type: 'SYNC_RESULT',
          payload: result
        });
        break;
      }

      case 'SYNC_BATCH': {
        const batchResults = await processor.processSyncBatch(payload);
        self.postMessage({
          type: 'BATCH_COMPLETE',
          payload: {
            batchId: payload.id,
            results: batchResults,
            summary: {
              total: batchResults.length,
              successful: batchResults.filter(r => r.success).length,
              failed: batchResults.filter(r => !r.success).length,
            }
          }
        });
        break;
      }

      case 'HEALTH_CHECK':
        self.postMessage({
          type: 'HEALTH_RESPONSE',
          payload: {
            status: 'healthy',
            stats: processor.getStats(),
            timestamp: Date.now(),
          }
        });
        break;

      default:
        throw new Error(`Unknown message type: ${type}`);
    }
  } catch (error) {
    self.postMessage({
      type: 'ERROR',
      payload: {
        error: error instanceof Error ? error.message : 'Unknown error',
        originalMessage: event.data,
      }
    });
  }
};

// Error handling
self.onerror = (error) => {
  self.postMessage({
    type: 'ERROR',
    payload: {
      error: `Worker error: ${error.message}`,
      filename: error.filename,
      lineno: error.lineno,
    }
  });
};

self.onunhandledrejection = (event) => {
  self.postMessage({
    type: 'ERROR',
    payload: {
      error: `Unhandled promise rejection: ${event.reason}`,
    }
  });
};

// Export types for TypeScript
export type {
  SyncOperation,
  SyncResult,
  SyncBatch,
  WorkerMessage,
  WorkerResponse
};
