-- Migration: Remove User Invitation System
-- Description: Remove all invitation-related tables, functions, and types since accounts will be created directly
-- Date: 2025-01-14

-- Drop RLS policies for user_invitations table
DROP POLICY IF EXISTS "Admin and program officers can manage invitations" ON user_invitations;

-- Drop indexes related to user_invitations
DROP INDEX IF EXISTS idx_user_invitations_email;
DROP INDEX IF EXISTS idx_user_invitations_token;
DROP INDEX IF EXISTS idx_user_invitations_expires_at;

-- Drop functions that use user_invitations
DROP FUNCTION IF EXISTS create_user_invitation(VARCHAR, VARCHAR, user_role, UUID, VARCHAR);
DROP FUNCTION IF EXISTS bulk_create_invitations(J<PERSON>NB[]);

-- Drop the user_invitations table
DROP TABLE IF EXISTS user_invitations;

-- Remove invitation-related values from audit_action enum
-- Note: We can't directly remove enum values in PostgreSQL, so we'll create a new enum
-- and update the audit_logs table to use it

-- Create new audit_action enum without invitation-related values
CREATE TYPE audit_action_new AS ENUM (
    'user_created', 
    'user_updated', 
    'user_deleted', 
    'user_activated', 
    'user_deactivated',
    'role_changed',
    'bulk_creation_started',
    'bulk_creation_completed'
);

-- Update audit_logs table to use new enum (if it exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'audit_logs') THEN
        -- Update the column type
        ALTER TABLE audit_logs ALTER COLUMN action TYPE audit_action_new USING action::text::audit_action_new;
    END IF;
END $$;

-- Update staff_audit_log table to use new enum (if it exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'staff_audit_log') THEN
        -- Update the column type
        ALTER TABLE staff_audit_log ALTER COLUMN action TYPE audit_action_new USING action::text::audit_action_new;
    END IF;
END $$;

-- Drop the old enum
DROP TYPE IF EXISTS audit_action;

-- Rename the new enum to the original name
ALTER TYPE audit_action_new RENAME TO audit_action;

-- Drop invitation_status enum as it's no longer needed
DROP TYPE IF EXISTS invitation_status;

-- Update any remaining functions that might reference invitation functionality
-- Remove invitation-related parameters from existing functions if they exist

-- Clean up any remaining invitation-related database functions
DROP FUNCTION IF EXISTS send_invitation_email(TEXT, TEXT, TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS resend_invitation(UUID);
DROP FUNCTION IF EXISTS accept_invitation(TEXT, TEXT);

-- Update comments and documentation
COMMENT ON TYPE audit_action IS 'Audit actions for user management. Invitation-related actions removed as accounts are now created directly.';

-- Add comment for documentation
COMMENT ON MIGRATION '034_remove_invitation_system' IS 'Removed all invitation-related tables, functions, and types. User accounts are now created directly by admins and program officers without invitation flow.';
