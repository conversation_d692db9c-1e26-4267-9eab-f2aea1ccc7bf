/**
 * Attendance Tracking Hooks
 * Provides data fetching and management for comprehensive attendance tracking
 */

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useMemo } from 'react';

export interface AttendanceRecord {
  id: string;
  staff_id: string;
  staff_name: string;
  staff_email: string;
  staff_role: string;
  attendance_date: string;
  check_in_type: 'school' | 'office';
  location_id: string;
  location_name: string;
  location_address?: string;
  check_in_time: string;
  check_out_time?: string;
  total_duration_minutes?: number;
  status: 'active' | 'checked_out' | 'completed';
  distance_from_location?: number;
  location_verified: boolean;
  verification_method: string;
  notes?: string;
  created_at: string;
}

export interface AttendanceSummaryStats {
  total_records: number;
  total_staff: number;
  total_hours: number;
  school_checkins: number;
  office_checkins: number;
  completed_sessions: number;
  active_sessions: number;
  average_duration_minutes: number;
  total_distance_km: number;
}

export interface StaffFilterOption {
  id: string;
  full_name: string;
  email: string;
  role: string;
  total_checkins: number;
  last_checkin_date?: string;
}

export interface AttendanceFilters {
  startDate?: string;
  endDate?: string;
  staffId?: string;
  checkInType?: 'school' | 'office' | '';
  orderBy?: 'attendance_date' | 'staff_name' | 'check_in_time' | 'check_out_time' | 'total_duration_minutes' | 'location_name';
  orderDirection?: 'ASC' | 'DESC';
}

export interface PaginationOptions {
  page: number;
  pageSize: number;
}

/**
 * Hook to fetch attendance records with filtering and pagination
 */
export function useAttendanceRecords(
  filters: AttendanceFilters = {},
  pagination: PaginationOptions = { page: 1, pageSize: 10 }
) {
  const { profile } = useAuth();
  
  const queryKey = [
    'attendance-records',
    filters.startDate,
    filters.endDate,
    filters.staffId,
    filters.checkInType,
    filters.orderBy,
    filters.orderDirection,
    pagination.page,
    pagination.pageSize,
  ];

  return useQuery({
    queryKey,
    queryFn: async (): Promise<AttendanceRecord[]> => {
      const offset = (pagination.page - 1) * pagination.pageSize;
      
      const { data, error } = await supabase.rpc('get_staff_attendance_records', {
        p_start_date: filters.startDate || null,
        p_end_date: filters.endDate || null,
        p_staff_id: filters.staffId || null,
        p_check_in_type: filters.checkInType || null,
        p_limit: pagination.pageSize,
        p_offset: offset,
        p_order_by: filters.orderBy || 'attendance_date',
        p_order_direction: filters.orderDirection || 'DESC',
      });

      if (error) {
        console.error('Error fetching attendance records:', error);
        throw new Error(error.message);
      }

      return data || [];
    },
    enabled: !!profile && ['admin', 'program_officer'].includes(profile.role),
    staleTime: 30000, // 30 seconds
    cacheTime: 300000, // 5 minutes
  });
}

/**
 * Hook to fetch attendance summary statistics
 */
export function useAttendanceSummaryStats(filters: AttendanceFilters = {}) {
  const { profile } = useAuth();

  const queryKey = [
    'attendance-summary-stats',
    filters.startDate,
    filters.endDate,
    filters.staffId,
    filters.checkInType,
  ];

  return useQuery({
    queryKey,
    queryFn: async (): Promise<AttendanceSummaryStats> => {
      const { data, error } = await supabase.rpc('get_attendance_summary_stats', {
        p_start_date: filters.startDate || null,
        p_end_date: filters.endDate || null,
        p_staff_id: filters.staffId || null,
        p_check_in_type: filters.checkInType || null,
      });

      if (error) {
        console.error('Error fetching attendance summary stats:', error);
        throw new Error(error.message);
      }

      return data?.[0] || {
        total_records: 0,
        total_staff: 0,
        total_hours: 0,
        school_checkins: 0,
        office_checkins: 0,
        completed_sessions: 0,
        active_sessions: 0,
        average_duration_minutes: 0,
        total_distance_km: 0,
      };
    },
    enabled: !!profile && ['admin', 'program_officer'].includes(profile.role),
    staleTime: 60000, // 1 minute
    cacheTime: 300000, // 5 minutes
  });
}

/**
 * Hook to fetch staff list for filtering
 */
export function useStaffForAttendanceFilter() {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['staff-for-attendance-filter'],
    queryFn: async (): Promise<StaffFilterOption[]> => {
      const { data, error } = await supabase.rpc('get_staff_for_attendance_filter');

      if (error) {
        console.error('Error fetching staff for filter:', error);
        throw new Error(error.message);
      }

      return data || [];
    },
    enabled: !!profile && ['admin', 'program_officer'].includes(profile.role),
    staleTime: 300000, // 5 minutes
    cacheTime: 600000, // 10 minutes
  });
}

/**
 * Hook to get formatted duration display
 */
export function useFormattedDuration(minutes?: number): string {
  return useMemo(() => {
    if (!minutes || minutes <= 0) return '-';
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours === 0) {
      return `${remainingMinutes}m`;
    } else if (remainingMinutes === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${remainingMinutes}m`;
    }
  }, [minutes]);
}

/**
 * Hook to get formatted distance display
 */
export function useFormattedDistance(meters?: number): string {
  return useMemo(() => {
    if (!meters || meters <= 0) return '-';
    
    if (meters < 1000) {
      return `${Math.round(meters)}m`;
    } else {
      return `${(meters / 1000).toFixed(1)}km`;
    }
  }, [meters]);
}

/**
 * Hook to invalidate attendance queries (for refresh after updates)
 */
export function useInvalidateAttendanceQueries() {
  const queryClient = useQueryClient();

  return {
    invalidateAttendanceRecords: () => {
      queryClient.invalidateQueries({ queryKey: ['attendance-records'] });
    },
    invalidateAttendanceStats: () => {
      queryClient.invalidateQueries({ queryKey: ['attendance-summary-stats'] });
    },
    invalidateStaffFilter: () => {
      queryClient.invalidateQueries({ queryKey: ['staff-for-attendance-filter'] });
    },
    invalidateAll: () => {
      queryClient.invalidateQueries({ queryKey: ['attendance-records'] });
      queryClient.invalidateQueries({ queryKey: ['attendance-summary-stats'] });
      queryClient.invalidateQueries({ queryKey: ['staff-for-attendance-filter'] });
    },
  };
}

/**
 * Hook to get attendance record status badge props
 */
export function useAttendanceStatusBadge(status: string) {
  return useMemo(() => {
    switch (status) {
      case 'active':
        return {
          variant: 'default' as const,
          className: 'bg-blue-100 text-blue-800 border-blue-200',
          label: 'Active',
        };
      case 'checked_out':
      case 'completed':
        return {
          variant: 'default' as const,
          className: 'bg-green-100 text-green-800 border-green-200',
          label: 'Completed',
        };
      default:
        return {
          variant: 'secondary' as const,
          className: 'bg-gray-100 text-gray-800 border-gray-200',
          label: status,
        };
    }
  }, [status]);
}

/**
 * Hook to get check-in type badge props
 */
export function useCheckInTypeBadge(checkInType: string) {
  return useMemo(() => {
    switch (checkInType) {
      case 'school':
        return {
          variant: 'default' as const,
          className: 'bg-blue-100 text-blue-800 border-blue-200',
          label: 'School Visit',
        };
      case 'office':
        return {
          variant: 'default' as const,
          className: 'bg-purple-100 text-purple-800 border-purple-200',
          label: 'Office',
        };
      default:
        return {
          variant: 'secondary' as const,
          className: 'bg-gray-100 text-gray-800 border-gray-200',
          label: checkInType,
        };
    }
  }, [checkInType]);
}
