import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Home,
  BookOpen,
  School,
  FileText,
  CheckSquare,
  Menu,
  ChevronDown,
  ChevronRight,
  Users,
  MapPin,
  Package,
  BarChart3,
  TrendingUp,
  Award,
  GraduationCap,
  MessageSquare,
  Activity,
  Clock,
  UserCheck,
  Navigation as NavigationIcon,
  Bell,
  HelpCircle,
  Settings,
  Phone,
} from 'lucide-react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { useAccessControl } from '@/hooks/useAccessControl';

interface NavigationProps {
  currentUser: {
    id: string;
    name: string;
    role: 'admin' | 'program_officer' | 'field_staff';
  };
  currentView: string;
  onViewChange: (view: string) => void;
}

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  adminOnly?: boolean;
  subcategories: Array<{
    id: string;
    label: string;
    icon: React.ComponentType<{ className?: string }>;
  }>;
}

const Navigation = ({ currentUser, currentView, onViewChange }: NavigationProps) => {
  const [expanded, setExpanded] = useState(true);
  const [openSections, setOpenSections] = useState<string[]>(['tasks', 'attendance', 'schools', 'distributions', 'impact', 'reports']);
  const { roleChecker } = useAccessControl();

  const toggleSection = (sectionId: string) => {
    setOpenSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };



  const navItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
      subcategories: []
    },

    // Field Visits - Simplified for field staff, full features for admin/program officers
    {
      id: 'field-visits',
      label: roleChecker.isFieldStaff() ? 'My Field Visits' : 'Field Visits',
      icon: UserCheck,
      subcategories: []
    },
    {
      id: 'tasks',
      label: 'Tasks',
      icon: CheckSquare,
      subcategories: [] // No subcategories - unified task management for all roles
    },

    {
      id: 'schools',
      label: 'Schools',
      icon: School,
      subcategories: []
    },
    // Books - Hidden from field staff (admin and program officers only)
    ...(!roleChecker.isFieldStaff() ? [{
      id: 'books',
      label: 'Books',
      icon: BookOpen,
      subcategories: []
    }] : []),
    // Impact - Available to admin and program officers
    ...(roleChecker.isAdminOrProgramOfficer() ? [{
      id: 'impact',
      label: 'Impact',
      icon: Award,
      subcategories: []
    }] : []),
    // Staff Management - Admin and Program Officers, standalone route
    ...(roleChecker.isAdminOrProgramOfficer() ? [{
      id: 'staff-management',
      label: 'Staff Management',
      icon: Users,
      subcategories: [
        { id: 'staff-management-attendance', label: 'Attendance', icon: Clock }
      ]
    }] : []),
    {
      id: 'help',
      label: 'Help & Support',
      icon: HelpCircle,
      subcategories: [
        // Documentation - Admin only
        ...(roleChecker.isAdmin() ? [
          { id: 'help-docs', label: 'Documentation', icon: FileText }
        ] : [])
      ]
    },
    // Settings - Available to all users with role-based content (moved to last position)
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      subcategories: []
    },
  ];

  // Filter navigation items based on user role
  const filteredNavItems = navItems.filter(item => {
    // If item has adminOnly flag, only show to admins
    if (item.adminOnly && !roleChecker.isAdmin()) {
      return false;
    }
    return true;
  });

  return (
    <div className="bg-white h-full">
      {/* Sidebar Navigation */}
      <div className="h-full flex flex-col max-h-screen">
        {/* Navigation Items */}
        <div className="flex-1 p-3 scrollbar-visible min-h-0" style={{ maxHeight: 'calc(100vh - 120px)' }}>
          {filteredNavItems.map((item) => {
            const Icon = item.icon;
            const hasSubcategories = item.subcategories.length > 0;
            const isOpen = openSections.includes(item.id);
            
            return (
              <div key={item.id} className="mb-2">
                {hasSubcategories ? (
                  <Collapsible open={isOpen} onOpenChange={() => toggleSection(item.id)}>
                    <div className="flex">
                      <Button
                        variant="ghost"
                        className={`flex-1 justify-start ${
                          currentView === item.id
                            ? 'bg-ilead-green text-white hover:bg-ilead-dark-green'
                            : 'text-gray-600 hover:text-ilead-green hover:bg-gray-100'
                        }`}
                        onClick={() => onViewChange(item.id)}
                      >
                        <Icon className="mr-2 h-4 w-4" />
                        {item.label}
                      </Button>
                      <CollapsibleTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className={`px-2 ${
                            currentView.startsWith(item.id)
                              ? 'text-white hover:bg-ilead-dark-green'
                              : 'text-gray-600 hover:text-ilead-green hover:bg-gray-100'
                          }`}
                        >
                          {isOpen ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </Button>
                      </CollapsibleTrigger>
                    </div>
                    <CollapsibleContent className="ml-4 mt-1">
                      {item.subcategories.map((subcategory) => {
                        const SubIcon = subcategory.icon;
                        return (
                          <Button
                            key={subcategory.id}
                            variant="ghost"
                            className={`w-full justify-start mb-1 ${
                              currentView === subcategory.id
                                ? 'bg-ilead-light-green text-ilead-dark-green'
                                : 'text-gray-500 hover:text-ilead-green hover:bg-gray-50'
                            }`}
                            onClick={() => onViewChange(subcategory.id)}
                          >
                            <SubIcon className="mr-2 h-3 w-3" />
                            {subcategory.label}
                          </Button>
                        );
                      })}
                    </CollapsibleContent>
                  </Collapsible>
                ) : (
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      currentView === item.id
                        ? 'bg-ilead-green text-white hover:bg-ilead-dark-green'
                        : 'text-gray-600 hover:text-ilead-green hover:bg-gray-100'
                    }`}
                    onClick={() => onViewChange(item.id)}
                  >
                    <Icon className="mr-2 h-4 w-4" />
                    {item.label}
                  </Button>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Navigation;
