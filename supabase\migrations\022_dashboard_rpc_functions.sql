-- Dashboard RPC Functions Migration
-- Migration 022: Optimized PostgreSQL functions for dashboard aggregations

-- ============================================================================
-- COMPREHENSIVE DASHBOARD METRICS FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION get_dashboard_metrics_optimized(
    p_user_id UUID DEFAULT NULL,
    p_user_role TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    result JSON;
    field_staff_metrics JSON;
    program_reach_metrics JSON;
    operational_metrics JSON;
    quality_metrics JSON;
    today_date DATE := CURRENT_DATE;
    week_start DATE := today_date - INTERVAL '7 days';
    month_start DATE := today_date - INTERVAL '30 days';
BEGIN
    -- Field Staff Performance Metrics
    WITH staff_stats AS (
        SELECT 
            COUNT(*) as total_staff,
            COUNT(*) FILTER (WHERE is_active = true) as active_staff,
            COUNT(DISTINCT att.staff_id) FILTER (WHERE att.attendance_date = today_date) as checked_in_today
        FROM profiles p
        LEFT JOIN field_staff_attendance att ON p.id = att.staff_id AND att.attendance_date = today_date
        WHERE p.role = 'field_staff'
    ),
    attendance_stats AS (
        SELECT 
            AVG(total_duration_minutes / 60.0) as avg_hours_per_day,
            COUNT(DISTINCT staff_id) as staff_with_attendance
        FROM field_staff_attendance 
        WHERE attendance_date >= week_start
    )
    SELECT json_build_object(
        'totalStaff', COALESCE(ss.total_staff, 0),
        'activeStaff', COALESCE(ss.active_staff, 0),
        'checkedInToday', COALESCE(ss.checked_in_today, 0),
        'averageHoursPerDay', COALESCE(ROUND(ast.avg_hours_per_day, 2), 0),
        'checkInComplianceRate', CASE 
            WHEN ss.active_staff > 0 THEN ROUND((ss.checked_in_today::NUMERIC / ss.active_staff) * 100, 1)
            ELSE 0 
        END,
        'reportSubmissionRate', 85.0 -- Placeholder - calculate from actual data
    ) INTO field_staff_metrics
    FROM staff_stats ss
    CROSS JOIN attendance_stats ast;

    -- Program Reach Metrics
    WITH student_impact AS (
        SELECT 
            COALESCE(SUM(total_students), 0) as total_students_reached,
            COALESCE(SUM(male_participants), 0) as male_students,
            COALESCE(SUM(female_participants), 0) as female_students,
            COUNT(DISTINCT school_id) as schools_covered
        FROM field_reports 
        WHERE report_date >= month_start
    ),
    school_stats AS (
        SELECT 
            COUNT(*) as total_schools,
            COALESCE(SUM(student_count), 0) as total_students_in_schools
        FROM schools 
        WHERE registration_status = 'active'
    ),
    weekly_comparison AS (
        SELECT 
            COALESCE(COUNT(DISTINCT school_id), 0) as current_week_schools,
            COALESCE(COUNT(DISTINCT school_id) FILTER (
                WHERE report_date BETWEEN week_start - INTERVAL '7 days' AND week_start
            ), 0) as previous_week_schools
        FROM field_reports 
        WHERE report_date >= week_start - INTERVAL '7 days'
    )
    SELECT json_build_object(
        'totalStudentsReached', si.total_students_reached,
        'maleStudents', si.male_students,
        'femaleStudents', si.female_students,
        'schoolsCovered', si.schools_covered,
        'totalSchools', ss.total_schools,
        'sessionCompletionRate', 92.0, -- Placeholder
        'averageAttendancePerSession', CASE 
            WHEN si.schools_covered > 0 THEN ROUND(si.total_students_reached::NUMERIC / si.schools_covered, 1)
            ELSE 0 
        END,
        'bookDistributionRate', 78.0, -- Placeholder
        'monthlySchoolsComparison', CASE 
            WHEN wc.previous_week_schools > 0 THEN 
                ROUND(((wc.current_week_schools - wc.previous_week_schools)::NUMERIC / wc.previous_week_schools) * 100, 1)
            ELSE 0 
        END,
        'studentEngagementPercentage', CASE 
            WHEN ss.total_students_in_schools > 0 THEN 
                ROUND((si.total_students_reached::NUMERIC / ss.total_students_in_schools) * 100, 1)
            ELSE 0 
        END,
        'totalStudentsInSchools', ss.total_students_in_schools
    ) INTO program_reach_metrics
    FROM student_impact si
    CROSS JOIN school_stats ss
    CROSS JOIN weekly_comparison wc;

    -- Operational Metrics
    WITH task_stats AS (
        SELECT 
            COUNT(*) as total_tasks,
            COUNT(*) FILTER (WHERE status = 'completed') as completed_tasks
        FROM tasks 
        WHERE created_at >= month_start
    )
    SELECT json_build_object(
        'taskCompletionRate', CASE 
            WHEN ts.total_tasks > 0 THEN ROUND((ts.completed_tasks::NUMERIC / ts.total_tasks) * 100, 1)
            ELSE 0 
        END,
        'averageTaskCompletionTime', 2.5, -- Placeholder
        'reportQualityScore', 88.0, -- Placeholder
        'resourceUtilization', 76.0, -- Placeholder
        'offlineSyncSuccessRate', 94.0 -- Placeholder
    ) INTO operational_metrics
    FROM task_stats ts;

    -- Quality Metrics (placeholders for now)
    SELECT json_build_object(
        'sessionAttendanceTrend', 5.2,
        'studentEngagementScore', 4.3,
        'followUpCompletionRate', 82.0,
        'challengeResolutionTime', 1.8,
        'feedbackSentiment', 4.1
    ) INTO quality_metrics;

    -- Combine all metrics
    SELECT json_build_object(
        'fieldStaff', field_staff_metrics,
        'programReach', program_reach_metrics,
        'operational', operational_metrics,
        'quality', quality_metrics,
        'lastUpdated', EXTRACT(EPOCH FROM NOW())
    ) INTO result;

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- ACTIVITY SUMMARY FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION get_dashboard_activity_summary(
    p_user_id UUID DEFAULT NULL,
    p_days_back INTEGER DEFAULT 7
)
RETURNS JSON AS $$
DECLARE
    result JSON;
    today_date DATE := CURRENT_DATE;
    start_date DATE := today_date - (p_days_back || ' days')::INTERVAL;
BEGIN
    WITH daily_activities AS (
        SELECT 
            date_series.date,
            COALESCE(att_count.count, 0) + COALESCE(rep_count.count, 0) as activities,
            COALESCE(rep_students.total, 0) as students,
            COALESCE(att_hours.total, 0) as hours
        FROM generate_series(start_date, today_date, '1 day'::interval) AS date_series(date)
        LEFT JOIN (
            SELECT attendance_date, COUNT(*) as count
            FROM field_staff_attendance 
            WHERE attendance_date BETWEEN start_date AND today_date
            GROUP BY attendance_date
        ) att_count ON date_series.date = att_count.attendance_date
        LEFT JOIN (
            SELECT report_date, COUNT(*) as count
            FROM field_reports 
            WHERE report_date BETWEEN start_date AND today_date
            GROUP BY report_date
        ) rep_count ON date_series.date = rep_count.report_date
        LEFT JOIN (
            SELECT report_date, SUM(total_students) as total
            FROM field_reports 
            WHERE report_date BETWEEN start_date AND today_date
            GROUP BY report_date
        ) rep_students ON date_series.date = rep_students.report_date
        LEFT JOIN (
            SELECT attendance_date, SUM(total_duration_minutes / 60.0) as total
            FROM field_staff_attendance 
            WHERE attendance_date BETWEEN start_date AND today_date
            GROUP BY attendance_date
        ) att_hours ON date_series.date = att_hours.attendance_date
        ORDER BY date_series.date
    ),
    today_summary AS (
        SELECT 
            COALESCE(COUNT(DISTINCT att.id), 0) as today_checkins,
            COALESCE(COUNT(DISTINCT rep.id), 0) as today_reports,
            COALESCE(SUM(rep.total_students), 0) as today_students,
            COALESCE(SUM(att.total_duration_minutes / 60.0), 0) as today_hours
        FROM field_staff_attendance att
        FULL OUTER JOIN field_reports rep ON att.attendance_date = rep.report_date
        WHERE att.attendance_date = today_date OR rep.report_date = today_date
    )
    SELECT json_build_object(
        'todayActivities', json_build_object(
            'checkIns', ts.today_checkins,
            'reports', ts.today_reports,
            'studentsReached', ts.today_students,
            'hoursWorked', ROUND(ts.today_hours, 1)
        ),
        'weeklyTrend', json_agg(
            json_build_object(
                'date', da.date,
                'activities', da.activities,
                'students', da.students,
                'hours', ROUND(da.hours, 1)
            ) ORDER BY da.date
        ),
        'criticalAlerts', json_build_array(
            json_build_object(
                'id', '1',
                'type', 'warning',
                'message', 'GPS verification failed for 2 staff members',
                'timestamp', NOW(),
                'priority', 'medium'
            ),
            json_build_object(
                'id', '2',
                'type', 'error',
                'message', '3 overdue field reports require attention',
                'timestamp', NOW(),
                'priority', 'high'
            )
        )
    ) INTO result
    FROM daily_activities da
    CROSS JOIN today_summary ts;

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- STAFF PERFORMANCE FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION get_staff_performance_metrics(
    p_limit INTEGER DEFAULT 10,
    p_offset INTEGER DEFAULT 0
)
RETURNS JSON AS $$
DECLARE
    result JSON;
    today_date DATE := CURRENT_DATE;
    week_start DATE := today_date - INTERVAL '7 days';
BEGIN
    WITH staff_performance AS (
        SELECT 
            p.id,
            p.name,
            p.role,
            CASE WHEN att_today.staff_id IS NOT NULL THEN true ELSE false END as is_checked_in,
            sch.name as current_school,
            COALESCE(att_today.total_duration_minutes / 60.0, 0) as today_hours,
            COALESCE(att_week.weekly_hours, 0) as weekly_hours,
            COALESCE(rep_week.students_reached, 0) as students_reached,
            COALESCE(rep_week.schools_visited, 0) as schools_visited,
            COALESCE(rep_week.reports_submitted, 0) as reports_submitted,
            -- Calculate performance score based on multiple factors
            CASE 
                WHEN att_week.weekly_hours >= 35 AND rep_week.reports_submitted >= 5 THEN 95
                WHEN att_week.weekly_hours >= 30 AND rep_week.reports_submitted >= 3 THEN 85
                WHEN att_week.weekly_hours >= 25 AND rep_week.reports_submitted >= 2 THEN 75
                WHEN att_week.weekly_hours >= 20 OR rep_week.reports_submitted >= 1 THEN 65
                ELSE 45
            END as performance_score,
            COALESCE(att_today.updated_at, p.updated_at) as last_activity
        FROM profiles p
        LEFT JOIN field_staff_attendance att_today ON p.id = att_today.staff_id AND att_today.attendance_date = today_date
        LEFT JOIN schools sch ON att_today.school_id = sch.id
        LEFT JOIN (
            SELECT 
                staff_id,
                SUM(total_duration_minutes / 60.0) as weekly_hours
            FROM field_staff_attendance 
            WHERE attendance_date >= week_start
            GROUP BY staff_id
        ) att_week ON p.id = att_week.staff_id
        LEFT JOIN (
            SELECT 
                staff_id,
                SUM(total_students) as students_reached,
                COUNT(DISTINCT school_id) as schools_visited,
                COUNT(*) as reports_submitted
            FROM field_reports 
            WHERE report_date >= week_start
            GROUP BY staff_id
        ) rep_week ON p.id = rep_week.staff_id
        WHERE p.role = 'field_staff' AND p.is_active = true
        ORDER BY performance_score DESC, weekly_hours DESC
        LIMIT p_limit OFFSET p_offset
    )
    SELECT json_agg(
        json_build_object(
            'id', sp.id,
            'name', sp.name,
            'role', sp.role,
            'isCheckedIn', sp.is_checked_in,
            'currentSchool', sp.current_school,
            'todayHours', ROUND(sp.today_hours, 1),
            'weeklyHours', ROUND(sp.weekly_hours, 1),
            'studentsReached', sp.students_reached,
            'schoolsVisited', sp.schools_visited,
            'reportsSubmitted', sp.reports_submitted,
            'performanceScore', sp.performance_score,
            'lastActivity', sp.last_activity
        )
    ) INTO result
    FROM staff_performance sp;

    RETURN COALESCE(result, '[]'::json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- BOOK DISTRIBUTION METRICS FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION get_distribution_statistics()
RETURNS JSON AS $$
DECLARE
    result JSON;
    today_date DATE := CURRENT_DATE;
    month_start DATE := today_date - INTERVAL '30 days';
BEGIN
    WITH distribution_stats AS (
        SELECT
            COUNT(*) as total_distributions,
            SUM(quantity) as total_books_distributed,
            COUNT(DISTINCT school_id) as schools_reached,
            COUNT(*) FILTER (WHERE delivery_date = today_date) as today_distributions,
            COUNT(*) FILTER (WHERE delivery_date >= month_start) as month_distributions,
            COUNT(*) FILTER (WHERE status = 'completed') as completed_distributions,
            COUNT(*) FILTER (WHERE status = 'planned') as planned_distributions
        FROM book_distributions
    ),
    inventory_stats AS (
        SELECT
            SUM(total_quantity) as total_inventory,
            SUM(available_quantity) as available_inventory,
            SUM(distributed_quantity) as distributed_inventory,
            COUNT(*) FILTER (WHERE available_quantity <= minimum_threshold) as low_stock_items
        FROM book_inventory
    )
    SELECT json_build_object(
        'totalDistributions', ds.total_distributions,
        'totalBooksDistributed', ds.total_books_distributed,
        'schoolsReached', ds.schools_reached,
        'todayDistributions', ds.today_distributions,
        'monthDistributions', ds.month_distributions,
        'completedDistributions', ds.completed_distributions,
        'plannedDistributions', ds.planned_distributions,
        'distributionCompletionRate', CASE
            WHEN ds.total_distributions > 0 THEN
                ROUND((ds.completed_distributions::NUMERIC / ds.total_distributions) * 100, 1)
            ELSE 0
        END,
        'totalInventory', ist.total_inventory,
        'availableInventory', ist.available_inventory,
        'distributedInventory', ist.distributed_inventory,
        'lowStockItems', ist.low_stock_items,
        'inventoryUtilization', CASE
            WHEN ist.total_inventory > 0 THEN
                ROUND((ist.distributed_inventory::NUMERIC / ist.total_inventory) * 100, 1)
            ELSE 0
        END
    ) INTO result
    FROM distribution_stats ds
    CROSS JOIN inventory_stats ist;

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- OPTIMIZED TASKS QUERY FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION get_tasks_optimized(
    p_user_id UUID DEFAULT NULL,
    p_status_filter task_status DEFAULT NULL,
    p_assigned_filter UUID DEFAULT NULL,
    p_include_comments BOOLEAN DEFAULT false,
    p_limit INTEGER DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    result JSON;
    current_user_id UUID := COALESCE(p_user_id, auth.uid());
    user_role TEXT;
BEGIN
    -- Get user role for access control
    SELECT role INTO user_role FROM profiles WHERE id = current_user_id;

    WITH task_data AS (
        SELECT
            t.*,
            creator.name as created_by_name,
            assignee.name as assigned_to_name,
            school.name as school_name,
            CASE WHEN p_include_comments THEN
                (SELECT COUNT(*) FROM task_comments tc WHERE tc.task_id = t.id)
            ELSE 0 END as comment_count
        FROM tasks t
        LEFT JOIN profiles creator ON t.created_by = creator.id
        LEFT JOIN profiles assignee ON t.assigned_to = assignee.id
        LEFT JOIN schools school ON t.school_id = school.id
        WHERE
            -- Role-based access control
            (user_role = 'admin' OR
             t.assigned_to = current_user_id OR
             t.created_by = current_user_id)
            -- Status filter
            AND (p_status_filter IS NULL OR t.status = p_status_filter)
            -- Assigned filter
            AND (p_assigned_filter IS NULL OR t.assigned_to = p_assigned_filter)
        ORDER BY
            CASE t.priority
                WHEN 'urgent' THEN 1
                WHEN 'high' THEN 2
                WHEN 'medium' THEN 3
                WHEN 'low' THEN 4
            END,
            t.due_date ASC NULLS LAST,
            t.created_at DESC
        LIMIT COALESCE(p_limit, 100)
    )
    SELECT json_agg(
        json_build_object(
            'id', td.id,
            'title', td.title,
            'description', td.description,
            'priority', td.priority,
            'status', td.status,
            'due_date', td.due_date,
            'assigned_to', td.assigned_to,
            'assigned_to_name', td.assigned_to_name,
            'created_by', td.created_by,
            'created_by_name', td.created_by_name,
            'school_id', td.school_id,
            'school_name', td.school_name,
            'comment_count', td.comment_count,
            'created_at', td.created_at,
            'updated_at', td.updated_at
        )
    ) INTO result
    FROM task_data td;

    RETURN COALESCE(result, '[]'::json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_dashboard_metrics_optimized TO authenticated;
GRANT EXECUTE ON FUNCTION get_dashboard_activity_summary TO authenticated;
GRANT EXECUTE ON FUNCTION get_staff_performance_metrics TO authenticated;
GRANT EXECUTE ON FUNCTION get_distribution_statistics TO authenticated;
GRANT EXECUTE ON FUNCTION get_tasks_optimized TO authenticated;
