import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface CreateUserRequest {
  email: string;
  name: string;
  role: string;
  division_id?: string;
  phone?: string;
}

interface BulkCreateRequest {
  users: CreateUserRequest[];
}

interface BulkCreateResponse {
  success: boolean;
  results: Array<{
    email: string;
    success: boolean;
    user?: {
      id: string;
      email: string;
      name: string;
      role: string;
      tempPassword: string;
    };
    error?: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Initialize Supabase admin client
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the authorization header
    const authHeader = req.headers.get('authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header required' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Verify the user's JWT token using admin client
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authorization token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get the user's profile to check permissions
    const { data: userProfile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !userProfile) {
      return new Response(
        JSON.stringify({ error: 'Unable to verify user permissions' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body
    const requestData: BulkCreateRequest = await req.json()

    // Validate request
    if (!Array.isArray(requestData.users) || requestData.users.length === 0) {
      return new Response(
        JSON.stringify({ error: 'Users array is required and must not be empty' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Validate each user
    for (const userData of requestData.users) {
      if (!userData.email || !userData.name || !userData.role) {
        return new Response(
          JSON.stringify({ error: 'Each user must have email, name, and role' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }
    }

    // Process users in batches to avoid timeouts
    const results: BulkCreateResponse['results'] = []
    const batchSize = 5 // Process 5 users at a time
    
    for (let i = 0; i < requestData.users.length; i += batchSize) {
      const batch = requestData.users.slice(i, i + batchSize)
      
      // Process batch sequentially to avoid overwhelming the system
      for (const userData of batch) {
        try {
          // Validate permissions for this user
          const hasPermission = await validatePermissions(userProfile.role, userData.role)
          if (!hasPermission) {
            results.push({
              email: userData.email,
              success: false,
              error: 'Insufficient permissions to create user with this role'
            })
            continue
          }

          // Check for duplicates
          const exists = await checkUserExists(supabaseAdmin, userData.email)
          if (exists) {
            results.push({
              email: userData.email,
              success: false,
              error: `User with email ${userData.email} already exists`
            })
            continue
          }

          // Generate secure password
          const tempPassword = generateSecurePassword()

          // Create auth user
          const { data: authData, error: authUserError } = await supabaseAdmin.auth.admin.createUser({
            email: userData.email.toLowerCase(),
            password: tempPassword,
            email_confirm: true,
          })

          if (authUserError || !authData.user) {
            results.push({
              email: userData.email,
              success: false,
              error: `Failed to create auth user: ${authUserError?.message || 'Unknown error'}`
            })
            continue
          }

          // Create profile
          const { error: profileInsertError } = await supabaseAdmin
            .from('profiles')
            .insert({
              id: authData.user.id,
              email: userData.email.toLowerCase(),
              name: userData.name,
              role: userData.role,
              division_id: userData.division_id || null,
              phone: userData.phone || null,
              country: 'Uganda',
              is_active: true,
            })

          if (profileInsertError) {
            // Cleanup auth user if profile creation fails
            await supabaseAdmin.auth.admin.deleteUser(authData.user.id)
            results.push({
              email: userData.email,
              success: false,
              error: `Failed to create profile: ${profileInsertError.message}`
            })
            continue
          }

          results.push({
            email: userData.email,
            success: true,
            user: {
              id: authData.user.id,
              email: userData.email,
              name: userData.name,
              role: userData.role,
              tempPassword
            }
          })

        } catch (error) {
          results.push({
            email: userData.email,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error occurred'
          })
        }
      }
    }

    const successful = results.filter(r => r.success).length
    const failed = results.length - successful

    const response: BulkCreateResponse = {
      success: failed === 0,
      results,
      summary: {
        total: results.length,
        successful,
        failed
      }
    }

    return new Response(
      JSON.stringify(response),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in bulk-create-users function:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

// Validate user creation permissions
async function validatePermissions(creatorRole: string, targetRole: string): Promise<boolean> {
  // Admins can create any role
  if (creatorRole === 'admin') return true

  // Program officers can only create field-level roles
  if (creatorRole === 'program_officer') {
    const allowedRoles = ['staff', 'field_staff', 'partner', 'accountant', 'social_media_manager']
    return allowedRoles.includes(targetRole)
  }

  return false
}

// Generate secure password
function generateSecurePassword(): string {
  const length = 16
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const lowercase = 'abcdefghijklmnopqrstuvwxyz'
  const numbers = '**********'
  const symbols = '!@#$%^&*'
  const allChars = uppercase + lowercase + numbers + symbols
  
  let password = ''
  
  // Ensure at least one character from each category
  password += uppercase[Math.floor(Math.random() * uppercase.length)]
  password += lowercase[Math.floor(Math.random() * lowercase.length)]
  password += numbers[Math.floor(Math.random() * numbers.length)]
  password += symbols[Math.floor(Math.random() * symbols.length)]
  
  // Fill remaining length
  for (let i = 4; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)]
  }
  
  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('')
}

// Check if user already exists
async function checkUserExists(supabaseAdmin: any, email: string): Promise<boolean> {
  const { data } = await supabaseAdmin
    .from('profiles')
    .select('id')
    .eq('email', email.toLowerCase())
    .single()
    
  return !!data
}
