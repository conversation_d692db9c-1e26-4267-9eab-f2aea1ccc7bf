import { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';

export interface PaginationState {
  currentPage: number;
  pageSize: number;
}

export interface UsePaginationStateOptions {
  defaultPageSize?: number;
  defaultPage?: number;
  pageSizeOptions?: number[];
  resetOnDependencyChange?: boolean;
}

export interface UsePaginationStateReturn {
  currentPage: number;
  pageSize: number;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  resetToFirstPage: () => void;
  updatePagination: (updates: Partial<PaginationState>) => void;
}

/**
 * Custom hook for managing pagination state with URL synchronization
 * Maintains pagination state in URL parameters for bookmarkable links and browser navigation
 */
export const usePaginationState = (
  options: UsePaginationStateOptions = {}
): UsePaginationStateReturn => {
  const {
    defaultPageSize = 10,
    defaultPage = 1,
    pageSizeOptions = [10, 25, 50, 100],
    resetOnDependencyChange = true
  } = options;

  const [searchParams, setSearchParams] = useSearchParams();

  // Initialize state from URL parameters or defaults
  const getInitialPage = useCallback(() => {
    const pageParam = searchParams.get('page');
    const page = pageParam ? parseInt(pageParam, 10) : defaultPage;
    return page > 0 ? page : defaultPage;
  }, [searchParams, defaultPage]);

  const getInitialPageSize = useCallback(() => {
    const sizeParam = searchParams.get('pageSize');
    const size = sizeParam ? parseInt(sizeParam, 10) : defaultPageSize;
    return pageSizeOptions.includes(size) ? size : defaultPageSize;
  }, [searchParams, defaultPageSize, pageSizeOptions]);

  const [currentPage, setCurrentPageState] = useState(getInitialPage);
  const [pageSize, setPageSizeState] = useState(getInitialPageSize);

  // Update URL parameters when pagination state changes
  const updateUrlParams = useCallback((page: number, size: number) => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      
      // Only set page parameter if it's not the default
      if (page === defaultPage) {
        newParams.delete('page');
      } else {
        newParams.set('page', page.toString());
      }
      
      // Only set pageSize parameter if it's not the default
      if (size === defaultPageSize) {
        newParams.delete('pageSize');
      } else {
        newParams.set('pageSize', size.toString());
      }
      
      return newParams;
    });
  }, [setSearchParams, defaultPage, defaultPageSize]);

  // Sync state with URL parameters when they change externally
  useEffect(() => {
    const urlPage = getInitialPage();
    const urlPageSize = getInitialPageSize();
    
    if (urlPage !== currentPage) {
      setCurrentPageState(urlPage);
    }
    
    if (urlPageSize !== pageSize) {
      setPageSizeState(urlPageSize);
    }
  }, [searchParams, getInitialPage, getInitialPageSize, currentPage, pageSize]);

  // Set current page and update URL
  const setCurrentPage = useCallback((page: number) => {
    if (page < 1) return;
    
    setCurrentPageState(page);
    updateUrlParams(page, pageSize);
  }, [pageSize, updateUrlParams]);

  // Set page size and update URL (reset to page 1)
  const setPageSize = useCallback((size: number) => {
    if (!pageSizeOptions.includes(size)) return;
    
    setPageSizeState(size);
    setCurrentPageState(1);
    updateUrlParams(1, size);
  }, [pageSizeOptions, updateUrlParams]);

  // Reset to first page
  const resetToFirstPage = useCallback(() => {
    setCurrentPage(1);
  }, [setCurrentPage]);

  // Update multiple pagination properties at once
  const updatePagination = useCallback((updates: Partial<PaginationState>) => {
    const newPage = updates.currentPage ?? currentPage;
    const newPageSize = updates.pageSize ?? pageSize;
    
    if (updates.pageSize && !pageSizeOptions.includes(updates.pageSize)) {
      return;
    }
    
    setCurrentPageState(newPage);
    setPageSizeState(newPageSize);
    updateUrlParams(newPage, newPageSize);
  }, [currentPage, pageSize, pageSizeOptions, updateUrlParams]);

  return {
    currentPage,
    pageSize,
    setCurrentPage,
    setPageSize,
    resetToFirstPage,
    updatePagination
  };
};

export default usePaginationState;
