#!/usr/bin/env node

/**
 * Configuration Validation Script
 * Validates environment variables before build/deployment
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function loadEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return {};
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const env = {};
  
  content.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        env[key.trim()] = valueParts.join('=').trim();
      }
    }
  });
  
  return env;
}

function validateConfiguration() {
  log('\n🔍 Validating iLead Field Track Configuration...', 'blue');
  
  const errors = [];
  const warnings = [];
  
  // Load environment variables
  const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env';
  const envVars = { ...process.env, ...loadEnvFile(envFile) };
  
  log(`\n📁 Using environment file: ${envFile}`, 'blue');
  
  // Required variables
  const required = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ];
  
  // Important but optional variables
  const important = [
    'VITE_SUPABASE_SERVICE_ROLE_KEY',
    'VITE_APP_ENVIRONMENT'
  ];
  
  // Check required variables
  log('\n✅ Checking required variables:', 'green');
  required.forEach(key => {
    if (!envVars[key]) {
      errors.push(`Missing required environment variable: ${key}`);
      log(`  ❌ ${key}: Missing`, 'red');
    } else {
      log(`  ✅ ${key}: Set`, 'green');
    }
  });
  
  // Check important variables
  log('\n⚠️  Checking important variables:', 'yellow');
  important.forEach(key => {
    if (!envVars[key]) {
      warnings.push(`Missing important environment variable: ${key}`);
      log(`  ⚠️  ${key}: Missing`, 'yellow');
    } else {
      log(`  ✅ ${key}: Set`, 'green');
    }
  });
  
  // Validate Supabase URL format
  if (envVars.VITE_SUPABASE_URL) {
    if (!envVars.VITE_SUPABASE_URL.startsWith('https://')) {
      errors.push('VITE_SUPABASE_URL must be a valid HTTPS URL');
    }
    if (!envVars.VITE_SUPABASE_URL.includes('.supabase.co')) {
      warnings.push('VITE_SUPABASE_URL should be a Supabase URL');
    }
  }
  
  // Validate environment setting
  const environment = envVars.VITE_APP_ENVIRONMENT || 'development';
  if (!['development', 'production', 'staging'].includes(environment)) {
    errors.push('VITE_APP_ENVIRONMENT must be one of: development, production, staging');
  }
  
  // Production-specific checks
  if (environment === 'production') {
    log('\n🚀 Production environment detected - additional checks:', 'blue');
    
    if (!envVars.VITE_SUPABASE_SERVICE_ROLE_KEY) {
      errors.push('VITE_SUPABASE_SERVICE_ROLE_KEY is required for production (user management will not work)');
    }
    
    if (envVars.VITE_ENABLE_DEBUG_MODE === 'true') {
      warnings.push('Debug mode is enabled in production');
    }
    
    if (!envVars.VITE_ENABLE_ERROR_REPORTING || envVars.VITE_ENABLE_ERROR_REPORTING !== 'true') {
      warnings.push('Error reporting is not enabled in production');
    }
  }
  
  // Security checks
  log('\n🔒 Security checks:', 'blue');
  
  // Check if service role key is in the code (security risk)
  const clientFile = path.join(__dirname, '../src/integrations/supabase/client.ts');
  if (fs.existsSync(clientFile)) {
    const clientContent = fs.readFileSync(clientFile, 'utf8');
    if (clientContent.includes('eyJ') && clientContent.includes('service_role')) {
      errors.push('Service role key appears to be hardcoded in client.ts - this is a security risk!');
    }
  }
  
  // Summary
  log('\n📊 Validation Summary:', 'bold');
  log(`Environment: ${environment}`, 'blue');
  log(`Errors: ${errors.length}`, errors.length > 0 ? 'red' : 'green');
  log(`Warnings: ${warnings.length}`, warnings.length > 0 ? 'yellow' : 'green');
  
  if (errors.length > 0) {
    log('\n❌ Configuration Errors:', 'red');
    errors.forEach(error => log(`  • ${error}`, 'red'));
  }
  
  if (warnings.length > 0) {
    log('\n⚠️  Configuration Warnings:', 'yellow');
    warnings.forEach(warning => log(`  • ${warning}`, 'yellow'));
  }
  
  if (errors.length === 0 && warnings.length === 0) {
    log('\n🎉 Configuration is valid! Ready for deployment.', 'green');
  } else if (errors.length === 0) {
    log('\n✅ Configuration is valid with warnings. Deployment should work.', 'yellow');
  } else {
    log('\n💥 Configuration has errors. Please fix before deployment.', 'red');
    process.exit(1);
  }
}

// Run validation
validateConfiguration();
