// Debug script to test Edge Function directly
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function debugEdgeFunction() {
  console.log('🔍 Debugging Edge Function Response...\n');

  try {
    // First, let's try without authentication to see what error we get
    console.log('1. Testing without authentication (should get 401)...');
    
    try {
      const { data, error } = await supabase.functions.invoke('create-user', {
        body: { email: '<EMAIL>', name: 'Test', role: 'field_staff' }
      });
      console.log('Unexpected success:', data);
    } catch (error) {
      console.log('Expected error (no auth):', error.message);
      console.log('Error details:', error);
    }

    // Now let's try to authenticate and test
    console.log('\n2. Attempting authentication...');
    
    // Try to sign in with a test account
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>', // You may need to update this
      password: 'your-password' // You may need to update this
    });

    if (authError) {
      console.log('❌ Authentication failed:', authError.message);
      console.log('\n📝 To debug properly, you need to:');
      console.log('1. Update the email/password in this script');
      console.log('2. Or test manually in the browser');
      
      // Let's try to get more info about the function anyway
      console.log('\n3. Testing function URL directly...');
      
      try {
        const response = await fetch('https://bygrspebofyofymivmib.supabase.co/functions/v1/create-user', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email: '<EMAIL>', name: 'Test', role: 'field_staff' })
        });
        
        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));
        
        const responseText = await response.text();
        console.log('Response body:', responseText);
        
      } catch (fetchError) {
        console.log('Fetch error:', fetchError.message);
      }
      
      return;
    }

    console.log('✅ Authenticated successfully');

    // Now test with authentication
    console.log('\n3. Testing with authentication...');
    
    const testUser = {
      email: `debug-test-${Date.now()}@example.com`,
      name: 'Debug Test User',
      role: 'field_staff'
    };

    try {
      const { data, error } = await supabase.functions.invoke('create-user', {
        body: testUser
      });

      if (error) {
        console.log('❌ Function error:', error);
        console.log('Error message:', error.message);
        console.log('Error details:', JSON.stringify(error, null, 2));
      } else {
        console.log('✅ Function success:', data);
      }
    } catch (invokeError) {
      console.log('❌ Invoke error:', invokeError);
      console.log('Error message:', invokeError.message);
      console.log('Error stack:', invokeError.stack);
    }

  } catch (error) {
    console.error('❌ Debug script error:', error);
  }
}

// Run the debug
debugEdgeFunction().catch(console.error);
