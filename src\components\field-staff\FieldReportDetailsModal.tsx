import React from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  FileText,
  User,
  MapPin,
  Calendar,
  Clock,
  Users,
  Target,
  BookOpen,
  AlertTriangle,
  Trophy,
  Eye,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react';
import { useFieldReport } from '@/hooks/field-staff/useFieldReports';
import { format } from 'date-fns';
import { getLessonById, ALL_LESSON_TOPICS } from '@/constants/lessonTopics';
import PhotoGallery from '@/components/photos/PhotoGallery';

interface FieldReportDetailsModalProps {
  reportId: string;
  isOpen: boolean;
  onClose: () => void;
}

const FieldReportDetailsModal: React.FC<FieldReportDetailsModalProps> = ({
  reportId,
  isO<PERSON>,
  onClose,
}) => {
  const { data: report, isLoading, error, isInitialLoading } = useFieldReport(reportId);

  // Helper function to format lesson topics with category
  const formatLessonTopic = (topic: string) => {
    // First try to find by ID
    const lesson = getLessonById(topic);
    if (lesson) {
      return `${lesson.category}: ${lesson.title}`;
    }

    // If not found by ID, try to find by title
    const lessonByTitle = ALL_LESSON_TOPICS.find(l => l.title === topic);
    if (lessonByTitle) {
      return `${lessonByTitle.category}: ${lessonByTitle.title}`;
    }

    // If still not found, return as is (might be a custom topic)
    return topic;
  };

  const formatActivityType = (activityType: string) => {
    return activityType.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const getActivityTypeBadgeColor = (activityType: string) => {
    switch (activityType) {
      case 'round_table_session':
        return 'bg-blue-100 text-blue-800';
      case 'school_visit':
        return 'bg-green-100 text-green-800';
      case 'meeting':
        return 'bg-purple-100 text-purple-800';
      case 'assessment':
        return 'bg-yellow-100 text-yellow-800';
      case 'other':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Show loading state during initial load or when still loading
  if (isLoading || isInitialLoading || (!report && !error)) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading report details...</span>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Only show error after initial loading is complete and there's actually an error
  if (error) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <div className="text-center text-red-600 py-8">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Failed to load report details</p>
            <p className="text-sm text-gray-500 mt-2">{error?.message}</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Field Report Details
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header Information */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Badge className={getActivityTypeBadgeColor(report.activity_type)}>
                    {formatActivityType(report.activity_type)}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    {format(new Date(report.report_date), 'EEEE, MMMM dd, yyyy')}
                  </span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <User className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="font-medium">Staff Member</div>
                      <div className="text-sm text-gray-600">{report.staff_name}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <MapPin className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="font-medium">School</div>
                      <div className="text-sm text-gray-600">{report.school_name}</div>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="font-medium">Visit Duration</div>
                      <div className="text-sm text-gray-600">
                        {report.attendance_check_in_time && report.attendance_check_out_time ? (
                          <>
                            {format(new Date(report.attendance_check_in_time), 'HH:mm')} - {' '}
                            {format(new Date(report.attendance_check_out_time), 'HH:mm')}
                          </>
                        ) : (
                          'Time not available'
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    {report.follow_up_required ? (
                      <CheckCircle className="h-5 w-5 text-orange-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-green-500" />
                    )}
                    <div>
                      <div className="font-medium">Follow-up Required</div>
                      <div className="text-sm text-gray-600">
                        {report.follow_up_required ? 'Yes' : 'No'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Session Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Session Statistics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">
                    {report.round_table_sessions || report.total_round_tables_calculated || 0}
                  </div>
                  <div className="text-sm text-gray-600">Round Table Sessions</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    {report.total_students || report.total_students_calculated || 0}
                  </div>
                  <div className="text-sm text-gray-600">Total Students</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">
                    {report.students_per_session || 0}
                  </div>
                  <div className="text-sm text-gray-600">Students per Session</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Activities and Topics */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  Activities Conducted
                </CardTitle>
              </CardHeader>
              <CardContent>
                {report.activities_conducted && report.activities_conducted.length > 0 ? (
                  <ul className="space-y-2">
                    {report.activities_conducted.map((activity, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                        <span className="text-sm">{activity}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-sm text-gray-500">No activities recorded</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  Lesson/Topic Covered
                </CardTitle>
              </CardHeader>
              <CardContent>
                {report.topics_covered && report.topics_covered.length > 0 ? (
                  <ul className="space-y-3">
                    {report.topics_covered.map((topic, index) => {
                      const formattedTopic = formatLessonTopic(topic);
                      const [category, lesson] = formattedTopic.includes(':')
                        ? formattedTopic.split(': ', 2)
                        : ['', formattedTopic];

                      return (
                        <li key={index} className="flex items-start gap-3">
                          <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                          <div className="flex-1">
                            {category && (
                              <Badge variant="outline" className="text-xs mb-1 mr-2">
                                {category}
                              </Badge>
                            )}
                            <span className="text-sm">{lesson || topic}</span>
                          </div>
                        </li>
                      );
                    })}
                  </ul>
                ) : (
                  <p className="text-sm text-gray-500">No lessons/topics recorded</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Challenges and Wins */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  Challenges Encountered
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm whitespace-pre-wrap">
                  {report.challenges || 'No challenges reported'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trophy className="h-5 w-5 text-yellow-500" />
                  Wins Achieved
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm whitespace-pre-wrap">
                  {report.wins || 'No wins reported'}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Observations and Lessons */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  General Observations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm whitespace-pre-wrap">
                  {report.observations || 'No observations recorded'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  Lessons Learned
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm whitespace-pre-wrap">
                  {report.lessons_learned || 'No lessons recorded'}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Follow-up Actions */}
          {report.follow_up_required && report.follow_up_actions && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-orange-500" />
                  Follow-up Actions Required
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm whitespace-pre-wrap">
                  {report.follow_up_actions}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Photo Gallery */}
          {report.photos && report.photos.length > 0 && (
            <PhotoGallery
              photos={report.photos}
              title="Field Report Photos"
              description="Photos taken during this field visit"
              maxDisplayPhotos={6}
              showValidationStatus={true}
              allowDownload={true}
              gridCols={3}
              aspectRatio="square"
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FieldReportDetailsModal;
