
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { School, SchoolFilters, SchoolSort, SchoolFormData, SchoolStatistics } from '@/types/school';

export const useSchoolsEnhanced = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch schools with advanced filtering, sorting, and pagination
  const useSchoolsFiltered = (
    filters: SchoolFilters = {},
    sort: SchoolSort = { field: 'name', direction: 'asc' },
    pagination: { limit: number; offset: number } = { limit: 10, offset: 0 }
  ) => {
    return useQuery({
      queryKey: ['schools-filtered', filters, sort, pagination],
      queryFn: async () => {
        console.log('Fetching filtered schools with:', { filters, sort, pagination });

        const { data, error } = await supabase.rpc('get_schools_filtered', {
          p_search_term: filters.search || null,
          p_school_type: filters.school_type || null,
          p_registration_status: filters.registration_status || null,
          p_district: filters.district || null,
          p_sort_by: sort.field,
          p_sort_direction: sort.direction,
          p_limit: pagination.limit,
          p_offset: pagination.offset,
        });

        if (error) {
          console.error('Error fetching filtered schools:', error);
          throw error;
        }

        return (data || []) as School[];
      },
      staleTime: 2 * 60 * 1000, // 2 minutes
      retry: 2,
    });
  };

  // Fetch total count of schools for pagination
  const useSchoolsCount = (filters: SchoolFilters = {}) => {
    return useQuery({
      queryKey: ['schools-count', filters],
      queryFn: async () => {
        console.log('Fetching schools count with filters:', filters);

        const { data, error } = await supabase.rpc('get_schools_count', {
          p_search_term: filters.search || null,
          p_school_type: filters.school_type || null,
          p_registration_status: filters.registration_status || null,
          p_district: filters.district || null,
        });

        if (error) {
          console.error('Error fetching schools count:', error);
          throw error;
        }

        return (data as number) || 0;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 2,
    });
  };

  // Fetch school statistics
  const useSchoolStatistics = () => {
    return useQuery({
      queryKey: ['school-statistics'],
      queryFn: async () => {
        console.log('Fetching school statistics');
        
        const { data, error } = await supabase.rpc('get_school_statistics');

        if (error) {
          console.error('Error fetching school statistics:', error);
          throw error;
        }

        return data?.[0] as SchoolStatistics;
      },
    });
  };

  // Update school mutation
  const updateSchoolMutation = useMutation({
    mutationFn: async ({ schoolId, schoolData }: { schoolId: string; schoolData: Partial<SchoolFormData> }) => {
      console.log('Updating school:', schoolId, schoolData);
      
      const { data, error } = await supabase.rpc('update_school', {
        p_school_id: schoolId,
        p_name: schoolData.name || null,
        p_code: schoolData.code || null,
        p_school_type: schoolData.school_type || null,
        p_student_count: schoolData.student_count || null,
        p_teacher_count: schoolData.teacher_count || null,
        p_contact_phone: schoolData.contact_phone || null,
        p_email: schoolData.email || null,
        p_division_id: schoolData.division_id || null,
        p_classes_count: schoolData.classes_count || null,
        p_streams_per_class: schoolData.streams_per_class || null,
        p_head_teacher_name: schoolData.head_teacher_name || null,
        p_deputy_head_teacher_name: schoolData.deputy_head_teacher_name || null,
        p_year_established: schoolData.year_established || null,
        p_ownership_type: schoolData.ownership_type || null,
        p_location_description: schoolData.location_description || null,
        p_nearest_health_center: schoolData.nearest_health_center || null,
        p_distance_to_main_road: schoolData.distance_to_main_road || null,
        p_infrastructure_notes: schoolData.infrastructure_notes || null,
        p_registration_status: schoolData.registration_status || null,
      });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schools-filtered'] });
      queryClient.invalidateQueries({ queryKey: ['schools'] });
      queryClient.invalidateQueries({ queryKey: ['school-statistics'] });
      toast({
        title: "Success",
        description: "School updated successfully",
      });
    },
    onError: (error: Error) => {
      console.error('Error updating school:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update school",
        variant: "destructive",
      });
    },
  });

  // Delete school mutation
  const deleteSchoolMutation = useMutation({
    mutationFn: async (schoolId: string) => {
      console.log('Deleting school:', schoolId);

      const { data, error } = await supabase.rpc('delete_school', {
        p_school_id: schoolId,
      });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schools-filtered'] });
      queryClient.invalidateQueries({ queryKey: ['schools'] });
      queryClient.invalidateQueries({ queryKey: ['school-statistics'] });
      toast({
        title: "Success",
        description: "School deleted successfully",
      });
    },
    onError: (error: Error) => {
      console.error('Error deleting school:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete school",
        variant: "destructive",
      });
    },
  });

  // Bulk update school status mutation
  const bulkUpdateStatusMutation = useMutation({
    mutationFn: async ({ schoolIds, status }: { schoolIds: string[]; status: School['registration_status'] }) => {
      console.log('Bulk updating school status:', schoolIds, status);

      const { data, error } = await supabase.rpc('bulk_update_school_status', {
        p_school_ids: schoolIds,
        p_new_status: status,
      });

      if (error) throw error;
      return data;
    },
    onSuccess: (updatedCount) => {
      queryClient.invalidateQueries({ queryKey: ['schools-filtered'] });
      queryClient.invalidateQueries({ queryKey: ['schools'] });
      queryClient.invalidateQueries({ queryKey: ['school-statistics'] });
      toast({
        title: "Success",
        description: `${updatedCount} schools updated successfully`,
      });
    },
    onError: (error: Error) => {
      console.error('Error bulk updating schools:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update schools",
        variant: "destructive",
      });
    },
  });

  return {
    useSchoolsFiltered,
    useSchoolsCount,
    useSchoolStatistics,
    updateSchool: updateSchoolMutation.mutate,
    deleteSchool: deleteSchoolMutation.mutate,
    bulkUpdateStatus: bulkUpdateStatusMutation.mutate,
    isUpdating: updateSchoolMutation.isPending,
    isDeleting: deleteSchoolMutation.isPending,
    isBulkUpdating: bulkUpdateStatusMutation.isPending,
  };
};
