import React from 'react';
import { Users } from 'lucide-react';
import TaskCard from '../TaskCard';
import TaskPagination from '../TaskPagination';
import { useTaskFiltering } from '@/hooks/useTaskFiltering';
import { Database } from '@/integrations/supabase/types';

type Task = {
  id: string;
  title: string;
  description: string | null;
  priority: Database['public']['Enums']['task_priority'];
  status: Database['public']['Enums']['task_status'];
  due_date: string | null;
  assigned_to: string | null;
  assigned_to_name: string | null;
  created_by: string;
  created_by_name: string;
  school_id: string | null;
  school_name: string | null;
  created_at: string;
  updated_at: string;
  comment_count: number;
};

interface TaskGridProps {
  tasks: Task[];
  currentUserId?: string;
  onViewDetails: (taskId: string) => void;
  onUpdateStatus: (taskId: string, status: Database['public']['Enums']['task_status']) => void;
  onEdit?: (taskId: string) => void;
  onDelete?: (taskId: string) => void;
  canDelete?: boolean;
  searchTerm: string;
  statusFilter: string;
  priorityFilter: string;
  currentPage: number;
  onPageChange: (page: number) => void;
  itemsPerPage?: number;
}

const TaskGrid: React.FC<TaskGridProps> = ({
  tasks,
  currentUserId,
  onViewDetails,
  onUpdateStatus,
  onEdit,
  onDelete,
  canDelete = false,
  searchTerm,
  statusFilter,
  priorityFilter,
  currentPage,
  onPageChange,
  itemsPerPage = 9
}) => {
  const { paginatedTasks, totalPages, totalItems } = useTaskFiltering({
    tasks,
    searchTerm,
    statusFilter,
    priorityFilter,
    currentPage,
    itemsPerPage
  });

  if (totalItems === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <Users className="h-12 w-12 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {searchTerm || statusFilter !== 'all' || priorityFilter !== 'all' 
            ? 'No matching tasks found' 
            : 'No tasks yet'
          }
        </h3>
        <p className="text-gray-600">
          {searchTerm || statusFilter !== 'all' || priorityFilter !== 'all'
            ? 'Try adjusting your search or filter criteria'
            : 'Tasks will appear here when they are created'
          }
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {paginatedTasks.map((task) => (
          <TaskCard
            key={task.id}
            task={task}
            currentUserId={currentUserId}
            onViewDetails={onViewDetails}
            onUpdateStatus={onUpdateStatus}
            onEdit={onEdit}
            onDelete={onDelete}
            canDelete={canDelete}
          />
        ))}
      </div>
      
      <TaskPagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
      />
    </div>
  );
};

export default TaskGrid;
