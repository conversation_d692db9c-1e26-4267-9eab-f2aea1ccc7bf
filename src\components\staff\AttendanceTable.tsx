/**
 * Attendance Table Component
 * Displays comprehensive attendance records in a tabular format with sorting and status indicators
 */

import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  ArrowUpDown, 
  ArrowUp, 
  ArrowDown,
  Clock,
  MapPin,
  User,
  Calendar,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { format } from 'date-fns';
import {
  AttendanceRecord,
  AttendanceFilters,
} from '@/hooks/useAttendanceTracking';

interface AttendanceTableProps {
  records: AttendanceRecord[];
  isLoading?: boolean;
  filters: AttendanceFilters;
  onFiltersChange: (filters: AttendanceFilters) => void;
  className?: string;
}

interface SortableHeaderProps {
  column: string;
  label: string;
  currentSort?: string;
  currentDirection?: 'ASC' | 'DESC';
  onSort: (column: string) => void;
  icon?: React.ComponentType<{ className?: string }>;
}

const SortableHeader: React.FC<SortableHeaderProps> = ({
  column,
  label,
  currentSort,
  currentDirection,
  onSort,
  icon: Icon,
}) => {
  const isActive = currentSort === column;
  
  return (
    <Button
      variant="ghost"
      className="h-auto p-0 font-semibold text-left justify-start hover:bg-transparent"
      onClick={() => onSort(column)}
    >
      <div className="flex items-center gap-2">
        {Icon && <Icon className="h-4 w-4" />}
        <span>{label}</span>
        {isActive ? (
          currentDirection === 'ASC' ? (
            <ArrowUp className="h-4 w-4" />
          ) : (
            <ArrowDown className="h-4 w-4" />
          )
        ) : (
          <ArrowUpDown className="h-4 w-4 opacity-50" />
        )}
      </div>
    </Button>
  );
};

// Utility functions (moved from hooks to avoid rules-of-hooks violations)
const formatDuration = (minutes?: number): string => {
  if (!minutes || minutes <= 0) return '-';

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (hours === 0) {
    return `${remainingMinutes}m`;
  } else if (remainingMinutes === 0) {
    return `${hours}h`;
  } else {
    return `${hours}h ${remainingMinutes}m`;
  }
};

const formatDistance = (meters?: number): string => {
  if (!meters || meters <= 0) return '-';

  if (meters < 1000) {
    return `${Math.round(meters)}m`;
  } else {
    return `${(meters / 1000).toFixed(1)}km`;
  }
};

const getStatusBadgeProps = (status: string) => {
  switch (status) {
    case 'active':
      return {
        className: 'bg-blue-100 text-blue-800 border-blue-200',
        label: 'Active',
      };
    case 'checked_out':
    case 'completed':
      return {
        className: 'bg-green-100 text-green-800 border-green-200',
        label: 'Completed',
      };
    default:
      return {
        className: 'bg-gray-100 text-gray-800 border-gray-200',
        label: status,
      };
  }
};

const getCheckInTypeBadgeProps = (checkInType: string) => {
  switch (checkInType) {
    case 'school':
      return {
        className: 'bg-blue-100 text-blue-800 border-blue-200',
        label: 'School Visit',
      };
    case 'office':
      return {
        className: 'bg-purple-100 text-purple-800 border-purple-200',
        label: 'Office',
      };
    default:
      return {
        className: 'bg-gray-100 text-gray-800 border-gray-200',
        label: checkInType,
      };
  }
};

const AttendanceTable: React.FC<AttendanceTableProps> = ({
  records,
  isLoading = false,
  filters,
  onFiltersChange,
  className = '',
}) => {
  const handleSort = (column: string) => {
    const newDirection = 
      filters.orderBy === column && filters.orderDirection === 'DESC' 
        ? 'ASC' 
        : 'DESC';
    
    onFiltersChange({
      ...filters,
      orderBy: column as AttendanceFilters['orderBy'],
      orderDirection: newDirection,
    });
  };

  const formatTime = (timeString?: string) => {
    if (!timeString) return '-';
    try {
      return format(new Date(timeString), 'HH:mm');
    } catch {
      return '-';
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch {
      return dateString;
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-12">
          <div className="flex items-center gap-2 text-gray-500">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span>Loading attendance records...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (records.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center text-gray-500">
            <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">No attendance records found</h3>
            <p className="text-sm">Try adjusting your filters to see more results.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Attendance Records
        </CardTitle>
        <CardDescription>
          Showing {records.length} attendance record{records.length !== 1 ? 's' : ''}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>
                  <SortableHeader
                    column="staff_name"
                    label="Staff Member"
                    currentSort={filters.orderBy}
                    currentDirection={filters.orderDirection}
                    onSort={handleSort}
                    icon={User}
                  />
                </TableHead>
                <TableHead>
                  <SortableHeader
                    column="attendance_date"
                    label="Date"
                    currentSort={filters.orderBy}
                    currentDirection={filters.orderDirection}
                    onSort={handleSort}
                    icon={Calendar}
                  />
                </TableHead>
                <TableHead>Check-in Type</TableHead>
                <TableHead>
                  <SortableHeader
                    column="location_name"
                    label="Location"
                    currentSort={filters.orderBy}
                    currentDirection={filters.orderDirection}
                    onSort={handleSort}
                    icon={MapPin}
                  />
                </TableHead>
                <TableHead>
                  <SortableHeader
                    column="check_in_time"
                    label="Check-in"
                    currentSort={filters.orderBy}
                    currentDirection={filters.orderDirection}
                    onSort={handleSort}
                    icon={Clock}
                  />
                </TableHead>
                <TableHead>
                  <SortableHeader
                    column="check_out_time"
                    label="Check-out"
                    currentSort={filters.orderBy}
                    currentDirection={filters.orderDirection}
                    onSort={handleSort}
                  />
                </TableHead>
                <TableHead>
                  <SortableHeader
                    column="total_duration_minutes"
                    label="Duration"
                    currentSort={filters.orderBy}
                    currentDirection={filters.orderDirection}
                    onSort={handleSort}
                  />
                </TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Distance</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {records.map((record) => {
                const duration = formatDuration(record.total_duration_minutes);
                const distance = formatDistance(record.distance_from_location);
                const statusBadge = getStatusBadgeProps(record.status);
                const typeBadge = getCheckInTypeBadgeProps(record.check_in_type);

                return (
                  <TableRow key={record.id} className="hover:bg-gray-50">
                    <TableCell>
                      <div>
                        <div className="font-medium">{record.staff_name}</div>
                        <div className="text-sm text-gray-500">{record.staff_role}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{formatDate(record.attendance_date)}</div>
                    </TableCell>
                    <TableCell>
                      <Badge className={typeBadge.className}>
                        {typeBadge.label}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{record.location_name}</div>
                        {record.location_address && (
                          <div className="text-sm text-gray-500 truncate max-w-48">
                            {record.location_address}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <span className="font-mono text-sm">{formatTime(record.check_in_time)}</span>
                        {record.location_verified ? (
                          <CheckCircle className="h-3 w-3 text-green-500" title="Location verified" />
                        ) : (
                          <AlertCircle className="h-3 w-3 text-orange-500" title="Location not verified" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="font-mono text-sm">{formatTime(record.check_out_time)}</span>
                    </TableCell>
                    <TableCell>
                      <span className="font-medium">{duration}</span>
                    </TableCell>
                    <TableCell>
                      <Badge className={statusBadge.className}>
                        {statusBadge.label}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-gray-600">{distance}</span>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default AttendanceTable;
