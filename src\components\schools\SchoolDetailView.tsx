
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { School, Users, MapPin, Phone, Mail, User, Calendar, Building, Edit, UserCheck, Globe, Shield } from 'lucide-react';
import { School as SchoolType } from '@/types/school';
import { Database } from '@/integrations/supabase/types';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

type FieldStaffMember = {
  id: string;
  name: string;
  phone: string | null;
  division_name: string;
};

type ChampionTeacher = {
  id: string;
  school_id: string;
  name: string;
  phone: string | null;
  email: string | null;
  contact_type: 'champion' | 'assistant_champion';
  is_active: boolean;
  created_at: string;
};
// Type definitions
type Profile = Database['public']['Functions']['get_user_profile']['Returns'][0];

interface SchoolDetailViewProps {
  school: SchoolType;
  currentUser: Profile | null;
  onEdit?: () => void;
}


const SchoolDetailView = ({ school, currentUser, onEdit }: SchoolDetailViewProps) => {
  const canEdit = currentUser?.role === 'admin' || currentUser?.role === 'program_officer';
  const { data: championTeachers = [] } = useQuery({
    queryKey: ['school-champion-teachers', school.id],
    queryFn: async (): Promise<ChampionTeacher[]> => {
      const { data, error } = await supabase
        .from('school_champion_teachers')
        .select('*')
        .eq('school_id', school.id)
        .eq('is_active', true)
        .order('created_at');
      if (error) return [];
      return (data as ChampionTeacher[]) || [];
    },
    enabled: !!school?.id,
  });

  const { data: fieldStaffMembers = [] } = useQuery({
    queryKey: ['field-staff-members'],
    queryFn: async (): Promise<FieldStaffMember[]> => {
      const { data, error } = await supabase.rpc('get_field_staff_members');
      if (error) return [];
      return data || [];
    },
    staleTime: 5 * 60 * 1000,
  });
  const assignedStaff = fieldStaffMembers.find(s => s.id === (school as SchoolType & { field_staff_id?: string }).field_staff_id);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-center space-x-3">
          <School className="h-8 w-8 text-purple-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{school.name}</h1>
            {school.code && <p className="text-gray-600">{school.code}</p>}
          </div>
        </div>
        {canEdit && (
          <Button onClick={onEdit} variant="outline">
            <Edit className="h-4 w-4 mr-2" />
            Edit School
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              School Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Status and Type Badges */}
            <div className="flex flex-wrap gap-2">
              <Badge variant={
                school.school_type === 'primary' ? 'default' :
                school.school_type === 'secondary' ? 'secondary' :
                'outline'
              }>
                {school.school_type?.charAt(0).toUpperCase() + school.school_type?.slice(1)} School
              </Badge>
              {school.registration_status && (
                <Badge variant={school.registration_status === 'active' ? 'default' : 'destructive'}>
                  {school.registration_status === 'active' ? 'Active' : 'Inactive'}
                </Badge>
              )}
              {school.ownership_type && (
                <Badge variant="outline">{school.ownership_type}</Badge>
              )}
              {school.school_category && (
                <Badge variant="outline">{school.school_category}</Badge>
              )}
            </div>

            {/* Basic Details */}
            <div className="space-y-3">
              {school.year_established && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Established:</span>
                  <span className="font-medium">{school.year_established}</span>
                </div>
              )}
              {school.date_joined_ilead && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Joined iLead:</span>
                  <span className="font-medium">{new Date(school.date_joined_ilead).toLocaleDateString()}</span>
                </div>
              )}
              {assignedStaff && (
                <div className="flex items-center gap-2">
                  <UserCheck className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Field Staff:</span>
                  <span className="font-medium">{assignedStaff.name}</span>
                  {assignedStaff.phone && (
                    <span className="text-sm text-gray-500">({assignedStaff.phone})</span>
                  )}
                </div>
              )}
              {school.is_partner_managed && school.partner_name && (
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Partner:</span>
                  <span className="font-medium">{school.partner_name}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Academic Structure */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Academic Structure
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900">{school.student_count ?? 'N/A'}</div>
                <div className="text-sm text-gray-600">Students</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                <User className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900">{school.teacher_count ?? 'N/A'}</div>
                <div className="text-sm text-gray-600">Teachers</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                <Building className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900">{school.classes_count || 'N/A'}</div>
                <div className="text-sm text-gray-600">Classes</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg border border-orange-200">
                <School className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900">{school.streams_per_class || 'N/A'}</div>
                <div className="text-sm text-gray-600">Streams/Class</div>
              </div>
            </div>
            {championTeachers.length > 0 && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Shield className="h-4 w-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700">Champion Teachers</span>
                </div>
                <div className="text-lg font-semibold text-gray-900">
                  {championTeachers.filter(ct => ct.contact_type === 'champion').length} Champions, {championTeachers.filter(ct => ct.contact_type === 'assistant_champion').length} Assistants
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">{/* Location & Contact */}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Location & Contact
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Location */}
            <div className="space-y-2">
              <div className="flex items-start gap-2">
                <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                <div>
                  <div className="font-medium">{[school.district, school.sub_county].filter(Boolean).join(', ') || 'Location not specified'}</div>
                  {school.location_description && (
                    <div className="text-sm text-gray-600 mt-1">{school.location_description}</div>
                  )}
                  {school.location_coordinates && (
                    <div className="text-xs text-gray-500 mt-1">
                      Coordinates: {school.location_coordinates}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="space-y-2">
              {school.contact_phone && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span>{school.contact_phone}</span>
                </div>
              )}
              {school.email && (
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span>{school.email}</span>
                </div>
              )}
              {school.head_teacher_email && (
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Head Teacher:</span>
                  <span>{school.head_teacher_email}</span>
                </div>
              )}
              {school.deputy_head_teacher_email && (
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Deputy Head:</span>
                  <span>{school.deputy_head_teacher_email}</span>
                </div>
              )}
            </div>

            {/* Accessibility Info */}
            {(school.nearest_health_center || school.distance_to_main_road) && (
              <div className="pt-2 border-t">
                <h4 className="font-medium text-gray-900 mb-2">Accessibility</h4>
                <div className="space-y-1 text-sm">
                  {school.nearest_health_center && (
                    <div>
                      <span className="text-gray-600">Health Center:</span>
                      <span className="ml-2">{school.nearest_health_center}</span>
                    </div>
                  )}
                  {school.distance_to_main_road && (
                    <div>
                      <span className="text-gray-600">Distance to Road:</span>
                      <span className="ml-2">{school.distance_to_main_road}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* School Leadership */}
        {(school.head_teacher_name || school.deputy_head_teacher_name) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                School Leadership
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {school.head_teacher_name && (
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Head Teacher:</span>
                  <span className="font-medium">{school.head_teacher_name}</span>
                </div>
              )}
              {school.deputy_head_teacher_name && (
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Deputy Head:</span>
                  <span className="font-medium">{school.deputy_head_teacher_name}</span>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Infrastructure */}
        {school.infrastructure_notes && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Infrastructure
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700">{school.infrastructure_notes}</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Champion Teachers - Full Width */}
      {championTeachers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Champion Teachers
            </CardTitle>
            <CardDescription>
              iLead program champions and assistant champions at this school
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Champions */}
              {championTeachers.filter(ct => ct.contact_type === 'champion').length > 0 && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Champion Teachers</h4>
                  <div className="space-y-3">
                    {championTeachers
                      .filter(ct => ct.contact_type === 'champion')
                      .map((ct, idx) => (
                        <div key={ct.id || idx} className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                          <div className="font-medium text-gray-900">{ct.name}</div>
                          <div className="space-y-1 mt-1">
                            {ct.phone && (
                              <div className="flex items-center gap-1 text-sm text-gray-600">
                                <Phone className="h-3 w-3" />
                                {ct.phone}
                              </div>
                            )}
                            {ct.email && (
                              <div className="flex items-center gap-1 text-sm text-gray-600">
                                <Mail className="h-3 w-3" />
                                {ct.email}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              )}

              {/* Assistant Champions */}
              {championTeachers.filter(ct => ct.contact_type === 'assistant_champion').length > 0 && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Assistant Champion Teachers</h4>
                  <div className="space-y-3">
                    {championTeachers
                      .filter(ct => ct.contact_type === 'assistant_champion')
                      .map((ct, idx) => (
                        <div key={ct.id || idx} className="p-3 bg-green-50 rounded-lg border border-green-200">
                          <div className="font-medium text-gray-900">{ct.name}</div>
                          <div className="space-y-1 mt-1">
                            {ct.phone && (
                              <div className="flex items-center gap-1 text-sm text-gray-600">
                                <Phone className="h-3 w-3" />
                                {ct.phone}
                              </div>
                            )}
                            {ct.email && (
                              <div className="flex items-center gap-1 text-sm text-gray-600">
                                <Mail className="h-3 w-3" />
                                {ct.email}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SchoolDetailView;
