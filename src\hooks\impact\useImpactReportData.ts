import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface ImpactReportFilters {
  schoolId?: string | null;
  dateRange: { start: Date; end: Date };
}

export interface ImpactKeyMetric {
  label: string;
  value: number | string;
  change?: string; // e.g., '+12.3%'
}

export interface ImpactReportSummary {
  summary: string;
  keyMetrics: ImpactKeyMetric[];
  sections: string[];
}

export interface ImpactReportData {
  summary: ImpactReportSummary;
  fieldReports: Array<{
    id: string | null;
    school_id: string | null;
    school_name: string | null;
    staff_id: string | null;
    staff_name: string | null;
    report_date: string | null;
    activity_type: string | null;
    total_students: number | null;
  }>;
  assessments: Array<{
    id: string;
    school_id: string | null;
    assessment_date: string;
    created_by: string | null;
  }>;
}

export const useImpactReportData = (filters: ImpactReportFilters) => {
  const start = filters.dateRange.start.toISOString().slice(0, 10);
  const end = filters.dateRange.end.toISOString().slice(0, 10);

  return useQuery<ImpactReportData>({
    queryKey: ['impact-report', filters.schoolId, start, end],
    queryFn: async () => {
      // Field reports from enhanced view
      let frQuery = supabase
        .from('enhanced_field_reports')
        .select('*')
        .gte('report_date', start)
        .lte('report_date', end);

      if (filters.schoolId) {
        frQuery = frQuery.eq('school_id', filters.schoolId);
      }

      const { data: fieldReports, error: frError } = await frQuery;
      if (frError) throw frError;

      // Assessments from ilead_assessments
      let asmtQuery = supabase.from('ilead_assessments').select('*')
        .gte('assessment_date', start)
        .lte('assessment_date', end);
      if (filters.schoolId) {
        asmtQuery = asmtQuery.eq('school_id', filters.schoolId);
      }
      const { data: assessments, error: asmtError } = await asmtQuery;
      if (asmtError) throw asmtError;

      // Derive basic key metrics (examples)
      const uniqueSchools = new Set((fieldReports || []).map(r => r.school_id)).size;
      const studentsReached = (fieldReports || []).reduce((sum, r) => sum + (r.total_students || 0), 0);
      const activitiesCount = (fieldReports || []).length;
      const assessmentsCount = (assessments || []).length;

      const summary: ImpactReportSummary = {
        summary: 'This report summarizes iLEAD activities and outcomes for the selected period using live data.',
        keyMetrics: [
          { label: 'Students Impacted', value: studentsReached.toLocaleString() },
          { label: 'Schools Served', value: uniqueSchools },
          { label: 'Activities Conducted', value: activitiesCount },
          { label: 'Assessments Recorded', value: assessmentsCount },
        ],
        sections: [
          'Executive Summary',
          'Activity Summary',
          'Assessments Overview',
          'Recommendations'
        ],
      };

      return {
        summary,
        fieldReports: fieldReports || [],
        assessments: assessments || [],
      };
    },
    staleTime: 60 * 1000,
  });
};

