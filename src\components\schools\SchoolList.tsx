
import React, { useState } from 'react';
import { List } from 'lucide-react';
import SchoolManagement from '../SchoolManagement';
import { useAuth } from '@/hooks/useAuth';
import { useAccessControl } from '@/hooks/useAccessControl';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import { actionPatterns, createResponsiveActions } from '@/utils/ui/actionButtons';
import AddSchoolModal from './AddSchoolModal';

const SchoolList = () => {
  const { profile } = useAuth();
  const { roleChecker } = useAccessControl();
  const [showAddModal, setShowAddModal] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate refresh
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  const handleDownload = () => {
    // Implement download functionality
    console.log('Downloading schools data...');
  };

  const handleFilter = () => {
    // Implement filter functionality
    console.log('Opening filters...');
  };

  // Create standardized actions based on user role
  const actions = createResponsiveActions(
    actionPatterns.list({
      onCreate: roleChecker.canManageSchools() ? () => setShowAddModal(true) : undefined,
      onRefresh: handleRefresh,
      onFilter: handleFilter,
      onDownload: handleDownload
    })
  );

  // Override loading state for refresh action
  const actionsWithState = actions.map(action =>
    action.label === 'Refresh'
      ? { ...action, loading: isRefreshing, disabled: isRefreshing }
      : action
  );

  return (
    <PageLayout>
      <ContentCard noPadding>
        <SchoolManagement currentUser={profile} />
      </ContentCard>

      {/* Add School Modal */}
      {showAddModal && (
        <AddSchoolModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
        />
      )}
    </PageLayout>
  );
};

export default SchoolList;
