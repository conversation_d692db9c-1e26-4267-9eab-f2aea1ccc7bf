import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { BookOpen, Package } from 'lucide-react';
import { PageLayout, PageHeader } from '@/components/layout';
import BookManagement from './BookManagement';
import ConsolidatedDistributionManagement from './distributions/ConsolidatedDistributionManagement';
import { BookManagementAccess } from '@/components/common/AccessControl';

const Books = () => {
  const [activeTab, setActiveTab] = useState('management');

  return (
    <BookManagementAccess>
      <PageLayout>
        <PageHeader
          title="Books"
          description="Manage books, inventory, and distributions"
          icon={BookOpen}
        />

        <div className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="management" className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Book Management
              </TabsTrigger>
              <TabsTrigger value="distributions" className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                Distributions
              </TabsTrigger>
            </TabsList>

            <TabsContent value="management" className="space-y-6">
              <BookManagement />
            </TabsContent>

            <TabsContent value="distributions" className="space-y-6">
              <ConsolidatedDistributionManagement />
            </TabsContent>
          </Tabs>
        </div>
      </PageLayout>
    </BookManagementAccess>
  );
};

export default Books;
