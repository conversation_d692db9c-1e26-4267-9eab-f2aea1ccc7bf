-- Add schools count function for pagination
-- This migration creates a function to efficiently count schools with filters

-- Function to get total count of schools with filters (for pagination)
CREATE OR REPLACE FUNCTION get_schools_count(
    p_search_term TEXT DEFAULT NULL,
    p_school_type school_type DEFAULT NULL,
    p_registration_status registration_status DEFAULT NULL,
    p_district TEXT DEFAULT NULL
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_count INTEGER;
BEGIN
    SELECT COUNT(*)
    INTO v_count
    FROM schools s
    LEFT JOIN divisions d ON s.division_id = d.id
    WHERE 
        (p_search_term IS NULL OR 
         s.name ILIKE '%' || p_search_term || '%' OR 
         s.code ILIKE '%' || p_search_term || '%')
    AND (p_school_type IS NULL OR s.school_type = p_school_type)
    AND (p_registration_status IS NULL OR s.registration_status = p_registration_status)
    AND (p_district IS NULL OR d.name ILIKE '%' || p_district || '%');
    
    RETURN v_count;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_schools_count(TEXT, school_type, registration_status, TEXT) TO authenticated;

-- Update the get_schools_filtered function to include total_count in results
-- This allows us to get both data and count in a single query when needed
CREATE OR REPLACE FUNCTION get_schools_filtered_with_count(
    p_search_term TEXT DEFAULT NULL,
    p_school_type school_type DEFAULT NULL,
    p_registration_status registration_status DEFAULT NULL,
    p_district TEXT DEFAULT NULL,
    p_sort_by TEXT DEFAULT 'name',
    p_sort_direction TEXT DEFAULT 'asc',
    p_limit INTEGER DEFAULT 10,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    name VARCHAR(255),
    code VARCHAR(50),
    school_type school_type,
    student_count INTEGER,
    teacher_count INTEGER,
    contact_phone VARCHAR(50),
    email VARCHAR(255),
    district VARCHAR(255),
    sub_county VARCHAR(255),
    registration_status registration_status,
    classes_count INTEGER,
    streams_per_class INTEGER,
    head_teacher_name VARCHAR(255),
    deputy_head_teacher_name VARCHAR(255),
    year_established INTEGER,
    date_joined_ilead DATE,
    champion_teacher_count INTEGER,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    total_count BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_total_count BIGINT;
    v_sort_column TEXT;
BEGIN
    -- Get total count first
    SELECT COUNT(*)
    INTO v_total_count
    FROM schools s
    LEFT JOIN divisions d ON s.division_id = d.id
    WHERE 
        (p_search_term IS NULL OR 
         s.name ILIKE '%' || p_search_term || '%' OR 
         s.code ILIKE '%' || p_search_term || '%')
    AND (p_school_type IS NULL OR s.school_type = p_school_type)
    AND (p_registration_status IS NULL OR s.registration_status = p_registration_status)
    AND (p_district IS NULL OR d.name ILIKE '%' || p_district || '%');

    -- Validate and set sort column
    v_sort_column := CASE 
        WHEN p_sort_by = 'name' THEN 's.name'
        WHEN p_sort_by = 'school_type' THEN 's.school_type'
        WHEN p_sort_by = 'student_count' THEN 's.student_count'
        WHEN p_sort_by = 'district' THEN 'd.name'
        WHEN p_sort_by = 'registration_status' THEN 's.registration_status'
        WHEN p_sort_by = 'created_at' THEN 's.created_at'
        ELSE 's.name'
    END;

    -- Return paginated results with total count
    RETURN QUERY EXECUTE format('
        SELECT 
            s.id,
            s.name,
            s.code,
            s.school_type,
            s.student_count,
            s.teacher_count,
            s.contact_phone,
            s.email,
            d.name as district,
            s.sub_county,
            s.registration_status,
            s.classes_count,
            s.streams_per_class,
            s.head_teacher_name,
            s.deputy_head_teacher_name,
            s.year_established,
            s.date_joined_ilead,
            s.champion_teacher_count,
            s.created_at,
            s.updated_at,
            %L::BIGINT as total_count
        FROM schools s
        LEFT JOIN divisions d ON s.division_id = d.id
        WHERE 
            ($1 IS NULL OR 
             s.name ILIKE ''%%'' || $1 || ''%%'' OR 
             s.code ILIKE ''%%'' || $1 || ''%%'')
        AND ($2 IS NULL OR s.school_type = $2)
        AND ($3 IS NULL OR s.registration_status = $3)
        AND ($4 IS NULL OR d.name ILIKE ''%%'' || $4 || ''%%'')
        ORDER BY %s %s
        LIMIT $5 OFFSET $6',
        v_total_count,
        v_sort_column,
        CASE WHEN p_sort_direction = 'desc' THEN 'DESC' ELSE 'ASC' END
    ) USING p_search_term, p_school_type, p_registration_status, p_district, p_limit, p_offset;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_schools_filtered_with_count(TEXT, school_type, registration_status, TEXT, TEXT, TEXT, INTEGER, INTEGER) TO authenticated;
