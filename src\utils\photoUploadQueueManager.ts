/**
 * Advanced Photo Upload Queue Manager
 * Handles priority-based queuing, retry mechanisms, and background processing
 */

import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { photoStorageCleanup } from './photoStorageCleanup';
import type { PhotoProcessingResult } from '@/workers/photoProcessingWorker';

// ============================================================================
// TYPES
// ============================================================================

export interface PhotoUploadTask {
  id: string;
  file: File;
  processedBlob?: Blob;
  fileName: string;
  bucket: string;
  filePath: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  metadata: {
    fieldReportId?: string;
    distributionId?: string;
    userId: string;
    timestamp: number;
    originalSize: number;
    compressedSize?: number;
  };
  retryCount: number;
  maxRetries: number;
  status: 'PENDING' | 'PROCESSING' | 'UPLOADING' | 'COMPLETED' | 'FAILED';
  uploadProgress: number;
  error?: string;
  createdAt: number;
  updatedAt: number;
}

export interface QueueStats {
  totalTasks: number;
  pendingTasks: number;
  processingTasks: number;
  uploadingTasks: number;
  completedTasks: number;
  failedTasks: number;
  averageUploadTime: number;
  totalDataUploaded: number;
  compressionSavings: number;
}

export interface UploadProgressEvent {
  taskId: string;
  progress: number;
  stage: 'QUEUED' | 'PROCESSING' | 'UPLOADING' | 'COMPLETE' | 'ERROR';
  message: string;
  bytesUploaded?: number;
  totalBytes?: number;
}

// ============================================================================
// PRIORITY QUEUE IMPLEMENTATION
// ============================================================================

class PriorityQueue<T> {
  private items: Array<{ item: T; priority: number }> = [];

  enqueue(item: T, priority: number): void {
    const queueElement = { item, priority };
    let added = false;

    for (let i = 0; i < this.items.length; i++) {
      if (queueElement.priority > this.items[i].priority) {
        this.items.splice(i, 0, queueElement);
        added = true;
        break;
      }
    }

    if (!added) {
      this.items.push(queueElement);
    }
  }

  dequeue(): T | undefined {
    return this.items.shift()?.item;
  }

  peek(): T | undefined {
    return this.items[0]?.item;
  }

  size(): number {
    return this.items.length;
  }

  isEmpty(): boolean {
    return this.items.length === 0;
  }

  clear(): void {
    this.items = [];
  }

  toArray(): T[] {
    return this.items.map(item => item.item);
  }
}

// ============================================================================
// PHOTO UPLOAD QUEUE MANAGER
// ============================================================================

export class PhotoUploadQueueManager {
  private queue: PriorityQueue<PhotoUploadTask>;
  private activeTasks: Map<string, PhotoUploadTask>;
  private completedTasks: Map<string, PhotoUploadTask>;
  private failedTasks: Map<string, PhotoUploadTask>;
  private worker: Worker | null = null;
  private isProcessing = false;
  private maxConcurrentUploads = 3;
  private progressCallbacks: Map<string, (event: UploadProgressEvent) => void> = new Map();
  private stats: QueueStats;

  constructor() {
    this.queue = new PriorityQueue<PhotoUploadTask>();
    this.activeTasks = new Map();
    this.completedTasks = new Map();
    this.failedTasks = new Map();
    this.stats = this.initializeStats();
    this.initializeWorker();
  }

  /**
   * Initialize Web Worker for photo processing
   */
  private initializeWorker(): void {
    try {
      this.worker = new Worker(
        new URL('../workers/photoProcessingWorker.ts', import.meta.url),
        { type: 'module' }
      );

      this.worker.onmessage = (event) => {
        this.handleWorkerMessage(event.data);
      };

      this.worker.onerror = (error) => {
        console.error('Photo processing worker error:', error);
        toast.error('Photo processing service unavailable');
      };

      // Health check
      this.worker.postMessage({ type: 'HEALTH_CHECK' });

    } catch (error) {
      console.warn('Web Worker not available, falling back to main thread processing');
      this.worker = null;
    }
  }

  /**
   * Handle messages from Web Worker
   */
  private handleWorkerMessage(data: { type: string; payload: unknown }): void {
    const { type, payload } = data;

    switch (type) {
      case 'RESULT':
        this.handleProcessingResult(payload as PhotoProcessingResult);
        break;
      case 'PROGRESS':
        this.handleProcessingProgress(payload);
        break;
      case 'ERROR':
        this.handleProcessingError(payload);
        break;
      case 'HEALTH_RESPONSE':
        console.log('Photo processing worker is healthy:', payload);
        break;
    }
  }

  /**
   * Add photo to upload queue
   */
  async addToQueue(
    file: File,
    options: {
      bucket?: string;
      priority?: PhotoUploadTask['priority'];
      fieldReportId?: string;
      distributionId?: string;
      userId: string;
      maxRetries?: number;
    }
  ): Promise<string> {
    // Check storage limits before adding to queue
    try {
      const canUpload = await this.checkStoragePermission(file.size);
      if (!canUpload) {
        throw new Error('Upload blocked due to storage limits');
      }
    } catch (error) {
      console.error('Storage check failed:', error);
      toast.error('Upload failed: Storage limit exceeded');
      throw error;
    }

    const taskId = `photo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timestamp = Date.now();

    // Determine bucket and file path
    const bucket = options.bucket || this.determineBucket(options);
    const fileName = this.generateFileName(file, options);
    const filePath = `${bucket}/${fileName}`;

    const task: PhotoUploadTask = {
      id: taskId,
      file,
      fileName,
      bucket,
      filePath,
      priority: options.priority || 'MEDIUM',
      metadata: {
        fieldReportId: options.fieldReportId,
        distributionId: options.distributionId,
        userId: options.userId,
        timestamp,
        originalSize: file.size,
      },
      retryCount: 0,
      maxRetries: options.maxRetries || 3,
      status: 'PENDING',
      uploadProgress: 0,
      createdAt: timestamp,
      updatedAt: timestamp,
    };

    // Add to queue with priority
    const priorityValue = this.getPriorityValue(task.priority);
    this.queue.enqueue(task, priorityValue);

    this.updateStats();
    this.notifyProgress(taskId, {
      taskId,
      progress: 0,
      stage: 'QUEUED',
      message: 'Added to upload queue',
    });

    // Start processing if not already running
    if (!this.isProcessing) {
      this.processQueue();
    }

    return taskId;
  }

  /**
   * Check if upload is allowed based on storage limits
   */
  private async checkStoragePermission(fileSize: number): Promise<boolean> {
    try {
      const shouldBlock = await photoStorageCleanup.shouldBlockNewUploads();
      if (shouldBlock) {
        return false;
      }

      // Check if adding this file would exceed storage limits
      const usage = await photoStorageCleanup.getStorageUsage();
      const maxStorageBytes = 500 * 1024 * 1024; // 500MB limit
      const wouldExceedLimit = (usage.totalSizeBytes + fileSize) > maxStorageBytes;

      if (wouldExceedLimit) {
        // Trigger automatic cleanup if approaching limit
        photoStorageCleanup.checkAndCleanup();

        // Still allow upload but warn user
        toast.warning('Storage approaching limit. Automatic cleanup initiated.');
      }

      return true;
    } catch (error) {
      console.error('Failed to check storage permission:', error);
      return true; // Allow upload if check fails
    }
  }

  /**
   * Process the upload queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.queue.isEmpty()) {
      return;
    }

    this.isProcessing = true;

    while (!this.queue.isEmpty() && this.activeTasks.size < this.maxConcurrentUploads) {
      const task = this.queue.dequeue();
      if (task) {
        this.activeTasks.set(task.id, task);
        this.processTask(task);
      }
    }

    // Check if we should continue processing
    if (this.activeTasks.size === 0) {
      this.isProcessing = false;
    }
  }

  /**
   * Process individual upload task
   */
  private async processTask(task: PhotoUploadTask): Promise<void> {
    try {
      task.status = 'PROCESSING';
      task.updatedAt = Date.now();

      this.notifyProgress(task.id, {
        taskId: task.id,
        progress: 10,
        stage: 'PROCESSING',
        message: 'Processing image...',
      });

      // Process image if worker is available
      if (this.worker) {
        this.worker.postMessage({
          type: 'PROCESS_SINGLE',
          payload: {
            id: task.id,
            file: task.file,
            options: {
              maxWidth: 1920,
              maxHeight: 1080,
              quality: 0.8,
              format: 'jpeg',
            },
            metadata: task.metadata,
          }
        });
      } else {
        // Fallback to direct upload without processing
        await this.uploadTask(task);
      }

    } catch (error) {
      await this.handleTaskError(task, error);
    }
  }

  /**
   * Handle processing result from worker
   */
  private async handleProcessingResult(result: PhotoProcessingResult): Promise<void> {
    const task = this.activeTasks.get(result.id);
    if (!task) return;

    if (result.success && result.processedBlob) {
      task.processedBlob = result.processedBlob;
      task.metadata.compressedSize = result.compressedSize;
      
      this.notifyProgress(task.id, {
        taskId: task.id,
        progress: 50,
        stage: 'PROCESSING',
        message: `Compressed by ${result.compressionRatio.toFixed(1)}%`,
      });

      await this.uploadTask(task);
    } else {
      await this.handleTaskError(task, new Error(result.error || 'Processing failed'));
    }
  }

  /**
   * Upload processed task
   */
  private async uploadTask(task: PhotoUploadTask): Promise<void> {
    try {
      task.status = 'UPLOADING';
      task.updatedAt = Date.now();

      const fileToUpload = task.processedBlob || task.file;
      const startTime = Date.now();

      this.notifyProgress(task.id, {
        taskId: task.id,
        progress: 60,
        stage: 'UPLOADING',
        message: 'Uploading to storage...',
        bytesUploaded: 0,
        totalBytes: fileToUpload.size,
      });

      // Upload to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from(task.bucket)
        .upload(task.filePath, fileToUpload, {
          onUploadProgress: (progress) => {
            const uploadProgress = 60 + (progress.loaded / progress.total) * 40;
            task.uploadProgress = uploadProgress;
            
            this.notifyProgress(task.id, {
              taskId: task.id,
              progress: uploadProgress,
              stage: 'UPLOADING',
              message: 'Uploading...',
              bytesUploaded: progress.loaded,
              totalBytes: progress.total,
            });
          }
        });

      if (uploadError) {
        throw uploadError;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(task.bucket)
        .getPublicUrl(task.filePath);

      // Mark as completed
      task.status = 'COMPLETED';
      task.uploadProgress = 100;
      task.updatedAt = Date.now();

      const uploadTime = Date.now() - startTime;
      
      this.notifyProgress(task.id, {
        taskId: task.id,
        progress: 100,
        stage: 'COMPLETE',
        message: 'Upload completed successfully',
      });

      // Move to completed tasks
      this.activeTasks.delete(task.id);
      this.completedTasks.set(task.id, task);

      // Update stats
      this.updateStats();
      this.updateUploadTimeStats(uploadTime);

      // Register photo metadata for storage management
      await this.registerPhotoMetadata(task, uploadResult.path);

      toast.success(`Photo uploaded successfully (${(fileToUpload.size / 1024 / 1024).toFixed(1)}MB)`);

    } catch (error) {
      await this.handleTaskError(task, error);
    }

    // Continue processing queue
    this.processQueue();
  }

  /**
   * Register photo metadata for storage management
   */
  private async registerPhotoMetadata(task: PhotoUploadTask, filePath: string): Promise<void> {
    try {
      const { error } = await supabase.from('photo_metadata').insert({
        file_path: filePath,
        bucket_name: task.bucket,
        original_filename: task.fileName,
        file_size_bytes: task.file.size,
        compressed_size_bytes: task.metadata.compressedSize,
        mime_type: task.file.type,
        storage_tier: 'recent',
        field_report_id: task.metadata.fieldReportId,
        distribution_id: task.metadata.distributionId,
        uploaded_by: task.metadata.userId,
        metadata: {
          originalSize: task.metadata.originalSize,
          compressionRatio: task.metadata.compressedSize ?
            ((task.file.size - task.metadata.compressedSize) / task.file.size * 100) : null,
          uploadDuration: Date.now() - task.createdAt,
        },
      });

      if (error) {
        console.error('Failed to register photo metadata:', error);
      }
    } catch (error) {
      console.error('Failed to register photo metadata:', error);
    }
  }

  /**
   * Handle task error with retry logic
   */
  private async handleTaskError(task: PhotoUploadTask, error: unknown): Promise<void> {
    task.retryCount++;
    task.error = error instanceof Error ? error.message : 'Unknown error';
    task.updatedAt = Date.now();

    if (task.retryCount < task.maxRetries) {
      // Retry with exponential backoff
      const delay = Math.min(1000 * Math.pow(2, task.retryCount), 30000);
      
      this.notifyProgress(task.id, {
        taskId: task.id,
        progress: 0,
        stage: 'ERROR',
        message: `Retrying in ${delay / 1000}s... (${task.retryCount}/${task.maxRetries})`,
      });

      setTimeout(() => {
        task.status = 'PENDING';
        const priorityValue = this.getPriorityValue(task.priority);
        this.queue.enqueue(task, priorityValue);
        this.processQueue();
      }, delay);

    } else {
      // Max retries reached
      task.status = 'FAILED';
      
      this.notifyProgress(task.id, {
        taskId: task.id,
        progress: 0,
        stage: 'ERROR',
        message: `Upload failed: ${task.error}`,
      });

      this.activeTasks.delete(task.id);
      this.failedTasks.set(task.id, task);
      
      toast.error(`Photo upload failed: ${task.error}`);
    }

    this.updateStats();
    this.processQueue();
  }

  /**
   * Utility methods
   */
  private determineBucket(options: { fieldReportId?: string; distributionId?: string }): string {
    if (options.fieldReportId) return 'field-report-photos';
    if (options.distributionId) return 'distribution-photos';
    return 'general-files';
  }

  private generateFileName(file: File, options: { fieldReportId?: string; distributionId?: string }): string {
    const timestamp = Date.now();
    const prefix = options.fieldReportId || options.distributionId || 'photo';
    const extension = file.name.split('.').pop() || 'jpg';
    return `${prefix}_${timestamp}_${Math.random().toString(36).substr(2, 9)}.${extension}`;
  }

  private getPriorityValue(priority: PhotoUploadTask['priority']): number {
    const values = { CRITICAL: 4, HIGH: 3, MEDIUM: 2, LOW: 1 };
    return values[priority];
  }

  private notifyProgress(taskId: string, event: UploadProgressEvent): void {
    const callback = this.progressCallbacks.get(taskId);
    if (callback) {
      callback(event);
    }
  }

  private initializeStats(): QueueStats {
    return {
      totalTasks: 0,
      pendingTasks: 0,
      processingTasks: 0,
      uploadingTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      averageUploadTime: 0,
      totalDataUploaded: 0,
      compressionSavings: 0,
    };
  }

  private updateStats(): void {
    this.stats = {
      totalTasks: this.queue.size() + this.activeTasks.size + this.completedTasks.size + this.failedTasks.size,
      pendingTasks: this.queue.size(),
      processingTasks: Array.from(this.activeTasks.values()).filter(t => t.status === 'PROCESSING').length,
      uploadingTasks: Array.from(this.activeTasks.values()).filter(t => t.status === 'UPLOADING').length,
      completedTasks: this.completedTasks.size,
      failedTasks: this.failedTasks.size,
      averageUploadTime: this.stats.averageUploadTime, // Updated separately
      totalDataUploaded: Array.from(this.completedTasks.values()).reduce((sum, task) => 
        sum + (task.metadata.compressedSize || task.metadata.originalSize), 0),
      compressionSavings: this.calculateCompressionSavings(),
    };
  }

  private calculateCompressionSavings(): number {
    const completed = Array.from(this.completedTasks.values());
    const totalOriginal = completed.reduce((sum, task) => sum + task.metadata.originalSize, 0);
    const totalCompressed = completed.reduce((sum, task) => 
      sum + (task.metadata.compressedSize || task.metadata.originalSize), 0);
    
    return totalOriginal > 0 ? ((totalOriginal - totalCompressed) / totalOriginal) * 100 : 0;
  }

  private updateUploadTimeStats(uploadTime: number): void {
    const completed = this.completedTasks.size;
    this.stats.averageUploadTime = ((this.stats.averageUploadTime * (completed - 1)) + uploadTime) / completed;
  }

  /**
   * Public API methods
   */
  onProgress(taskId: string, callback: (event: UploadProgressEvent) => void): void {
    this.progressCallbacks.set(taskId, callback);
  }

  offProgress(taskId: string): void {
    this.progressCallbacks.delete(taskId);
  }

  getStats(): QueueStats {
    this.updateStats();
    return { ...this.stats };
  }

  getTaskStatus(taskId: string): PhotoUploadTask | null {
    return this.activeTasks.get(taskId) || 
           this.completedTasks.get(taskId) || 
           this.failedTasks.get(taskId) || 
           null;
  }

  cancelTask(taskId: string): boolean {
    const task = this.activeTasks.get(taskId);
    if (task && task.status === 'PENDING') {
      this.activeTasks.delete(taskId);
      this.failedTasks.set(taskId, { ...task, status: 'FAILED', error: 'Cancelled by user' });
      return true;
    }
    return false;
  }

  clearCompleted(): void {
    this.completedTasks.clear();
    this.updateStats();
  }

  retryFailed(): void {
    const failed = Array.from(this.failedTasks.values());
    this.failedTasks.clear();
    
    failed.forEach(task => {
      task.retryCount = 0;
      task.status = 'PENDING';
      task.error = undefined;
      const priorityValue = this.getPriorityValue(task.priority);
      this.queue.enqueue(task, priorityValue);
    });
    
    this.processQueue();
  }

  destroy(): void {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
    this.queue.clear();
    this.activeTasks.clear();
    this.progressCallbacks.clear();
  }
}

// Export singleton instance
export const photoUploadQueue = new PhotoUploadQueueManager();
