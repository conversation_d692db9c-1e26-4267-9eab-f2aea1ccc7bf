import React from 'react';
import { useAccessControl } from '@/hooks/useAccessControl';
import StudentOutcomesModule from './StudentOutcomesModule';

/**
 * Wrapper component for Student Outcomes that handles role-based access
 * and passes the correct canViewAllData prop based on user role
 */
const StudentOutcomes: React.FC = () => {
  const { roleChecker } = useAccessControl();

  // Both admin and program officers should be able to view all data
  const canViewAllData = roleChecker.isAdminOrProgramOfficer();

  return (
    <StudentOutcomesModule
      schoolId={null}
      dateRange={{ 
        start: new Date(new Date().getFullYear(), 0, 1), 
        end: new Date() 
      }}
      canViewAllData={canViewAllData}
    />
  );
};

export default StudentOutcomes;
