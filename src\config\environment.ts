/**
 * Environment Configuration
 * Centralized configuration for all environment variables
 */

// Environment type
export type Environment = 'development' | 'production' | 'staging';

// Configuration interface
export interface AppConfig {
  // App Info
  appName: string;
  appVersion: string;
  environment: Environment;
  
  // Supabase
  supabaseUrl: string;
  supabaseAnonKey: string;
  supabaseServiceRoleKey?: string;
  
  // Features
  enableDebugMode: boolean;
  enableAnalytics: boolean;
  enableErrorReporting: boolean;
  
  // API
  apiTimeout: number;
  maxFileSize: number;
  maxPhotoSize: number;
  
  // GPS
  gpsTimeout: number;
  gpsMaxAge: number;
  gpsHighAccuracy: boolean;
  
  // Sync
  syncInterval: number;
  maxOfflineStorage: number;
  retryAttempts: number;
}

// Helper function to get environment variable with validation
function getEnvVar(key: string, required: boolean = true, defaultValue?: string): string {
  const value = import.meta.env[key] || defaultValue;
  
  if (required && !value) {
    throw new Error(`Missing required environment variable: ${key}`);
  }
  
  return value || '';
}

// Helper function to get boolean environment variable
function getBooleanEnvVar(key: string, defaultValue: boolean = false): boolean {
  const value = import.meta.env[key];
  if (value === undefined) return defaultValue;
  return value === 'true' || value === '1';
}

// Helper function to get number environment variable
function getNumberEnvVar(key: string, defaultValue: number): number {
  const value = import.meta.env[key];
  if (value === undefined) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

// Create configuration object
export const config: AppConfig = {
  // App Info
  appName: getEnvVar('VITE_APP_NAME', false, 'iLead Field Track'),
  appVersion: getEnvVar('VITE_APP_VERSION', false, '1.0.0'),
  environment: (getEnvVar('VITE_APP_ENVIRONMENT', false, 'development') as Environment),
  
  // Supabase - Required
  supabaseUrl: getEnvVar('VITE_SUPABASE_URL'),
  supabaseAnonKey: getEnvVar('VITE_SUPABASE_ANON_KEY'),
  supabaseServiceRoleKey: getEnvVar('VITE_SUPABASE_SERVICE_ROLE_KEY', false),
  
  // Features
  enableDebugMode: getBooleanEnvVar('VITE_ENABLE_DEBUG_MODE', true),
  enableAnalytics: getBooleanEnvVar('VITE_ENABLE_ANALYTICS', false),
  enableErrorReporting: getBooleanEnvVar('VITE_ENABLE_ERROR_REPORTING', false),
  
  // API
  apiTimeout: getNumberEnvVar('VITE_API_TIMEOUT', 30000),
  maxFileSize: getNumberEnvVar('VITE_MAX_FILE_SIZE', 10 * 1024 * 1024), // 10MB
  maxPhotoSize: getNumberEnvVar('VITE_MAX_PHOTO_SIZE', 5 * 1024 * 1024), // 5MB
  
  // GPS
  gpsTimeout: getNumberEnvVar('VITE_GPS_TIMEOUT', 15000),
  gpsMaxAge: getNumberEnvVar('VITE_GPS_MAX_AGE', 300000),
  gpsHighAccuracy: getBooleanEnvVar('VITE_GPS_HIGH_ACCURACY', true),
  
  // Sync
  syncInterval: getNumberEnvVar('VITE_SYNC_INTERVAL', 300000), // 5 minutes
  maxOfflineStorage: getNumberEnvVar('VITE_MAX_OFFLINE_STORAGE', 100 * 1024 * 1024), // 100MB
  retryAttempts: getNumberEnvVar('VITE_RETRY_ATTEMPTS', 3),
};

// Validation function
export function validateConfig(): void {
  const errors: string[] = [];
  
  // Validate required Supabase configuration
  if (!config.supabaseUrl) {
    errors.push('VITE_SUPABASE_URL is required');
  }
  
  if (!config.supabaseAnonKey) {
    errors.push('VITE_SUPABASE_ANON_KEY is required');
  }
  
  // Validate URL format
  if (config.supabaseUrl && !config.supabaseUrl.startsWith('https://')) {
    errors.push('VITE_SUPABASE_URL must be a valid HTTPS URL');
  }
  
  // Validate environment
  if (!['development', 'production', 'staging'].includes(config.environment)) {
    errors.push('VITE_APP_ENVIRONMENT must be one of: development, production, staging');
  }
  
  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
  }
}

// Development helpers
export const isDevelopment = config.environment === 'development';
export const isProduction = config.environment === 'production';
export const isStaging = config.environment === 'staging';

// Log configuration in development
if (isDevelopment && config.enableDebugMode) {
  console.log('🔧 App Configuration:', {
    ...config,
    // Hide sensitive keys in logs
    supabaseAnonKey: config.supabaseAnonKey ? '***' + config.supabaseAnonKey.slice(-4) : 'Not set',
    supabaseServiceRoleKey: config.supabaseServiceRoleKey ? '***' + config.supabaseServiceRoleKey.slice(-4) : 'Not set',
  });
}

// Validate configuration on import
validateConfig();
