# =============================================================================
# iLead Field Track - Environment Configuration
# =============================================================================

# Supabase Configuration
# Get these values from your Supabase project settings > API
VITE_SUPABASE_URL=https://bygrspebofyofymivmib.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ5Z3JzcGVib2Z5b2Z5bWl2bWliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwMzIxODgsImV4cCI6MjA2NDYwODE4OH0.xxfeix-6F42NmVWaQHE19nnDCxZmiMDs1_fyLb0-lgE

# Service Role Key (Required for admin operations like user creation)
# WARNING: Keep this secret and never commit the actual key to version control
# For production, use your deployment platform's environment variable system
VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ5Z3JzcGVib2Z5b2Z5bWl2bWliIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTAzMjE4OCwiZXhwIjoyMDY0NjA4MTg4fQ.d9HbH3fF-tBa8v7HBAR5XnFOghjorx_bAVQh0ZaeDf4

# Application Configuration
VITE_APP_NAME=iLead Field Track
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# Feature Flags
VITE_ENABLE_DEBUG_MODE=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=false

# API Configuration
VITE_API_TIMEOUT=30000
VITE_MAX_FILE_SIZE=10485760
VITE_MAX_PHOTO_SIZE=5242880

# GPS and Location Settings
VITE_GPS_TIMEOUT=15000
VITE_GPS_MAX_AGE=300000
VITE_GPS_HIGH_ACCURACY=true

# Offline Sync Configuration
VITE_SYNC_INTERVAL=300000
VITE_MAX_OFFLINE_STORAGE=104857600
VITE_RETRY_ATTEMPTS=3
