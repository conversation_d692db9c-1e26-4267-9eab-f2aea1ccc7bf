// API routes for user management
import express from 'express';
import { requireAuth } from '../middleware/auth';
import { createUser, bulkCreateUsers } from '../admin/users';
import type { CreateUserRequest, BulkCreateRequest } from '../admin/users';

const router = express.Router();

// POST /api/users - Create single user
router.post('/', requireAuth(['admin', 'program_officer']), async (req, res) => {
  try {
    const userRequest: CreateUserRequest = {
      ...req.body,
      created_by: req.auth.user.id
    };

    // Validate required fields
    if (!userRequest.email || !userRequest.name || !userRequest.role) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: email, name, role'
      });
    }

    const result = await createUser(userRequest);
    
    if (result.success) {
      res.status(201).json(result);
    } else {
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('User creation error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// POST /api/users/bulk - Create multiple users
router.post('/bulk', requireAuth(['admin', 'program_officer']), async (req, res) => {
  try {
    const bulkRequest: BulkCreateRequest = {
      users: req.body.users || [],
      created_by: req.auth.user.id
    };

    // Validate request
    if (!Array.isArray(bulkRequest.users) || bulkRequest.users.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Users array is required and must not be empty'
      });
    }

    // Validate each user
    for (const user of bulkRequest.users) {
      if (!user.email || !user.name || !user.role) {
        return res.status(400).json({
          success: false,
          error: 'Each user must have email, name, and role'
        });
      }
    }

    const result = await bulkCreateUsers(bulkRequest);
    
    res.status(200).json(result);

  } catch (error) {
    console.error('Bulk user creation error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;
