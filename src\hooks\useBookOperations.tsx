import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { BookCondition, BookLanguage } from '@/types/book';

interface BookFormData {
  title: string;
  language: BookLanguage;
  description?: string;
  total_quantity: number;
  condition: BookCondition;
  minimum_threshold: number;
  notes?: string;
}

interface BookWithInventory {
  id: string;
  title: string;
  author: string;
  isbn?: string;
  publication_year?: number;
  language: BookLanguage;
  publisher?: string;
  description?: string;
  total_quantity: number;
  available_quantity: number;
  distributed_quantity: number;
  damaged_quantity: number;
  lost_quantity: number;
  minimum_threshold: number;
  condition: BookCondition;
  storage_location?: string;
  cost_per_unit?: number;
  inventory_notes?: string;
  created_at: string;
  updated_at: string;
}

export const useBookOperations = () => {
  const { profile, loading } = useAuth();
  const queryClient = useQueryClient();

  // Check if user can manage books (admin, program officers, and accountants)
  // Return null during loading to prevent premature access denial
  const canManageBooks = loading ? null : (profile?.role === 'admin' || profile?.role === 'program_officer' || profile?.role === 'accountant');

  // Fetch books with inventory
  const { data: books = [], isLoading: isLoadingBooks, error: booksError } = useQuery({
    queryKey: ['books'],
    queryFn: async (): Promise<BookWithInventory[]> => {
      const { data, error } = await supabase.rpc('get_books_with_inventory');
      
      if (error) {
        console.error('Error fetching books:', error);
        throw error;
      }
      
      return data || [];
    },
    enabled: !!profile,
  });

  // Add book mutation
  const addBookMutation = useMutation({
    mutationFn: async (bookData: BookFormData): Promise<string> => {
      if (!canManageBooks) {
        throw new Error('Access denied. Only admins and program officers can add books.');
      }

      const { data, error } = await supabase.rpc('add_book_with_inventory', {
        p_title: bookData.title,
        p_author: 'iLead Program', // Default author for all iLead books
        p_category: 'other', // Default category since we don't collect this
        p_grade_level: 'p1', // Default grade level since we don't collect this
        p_isbn: null, // No longer collected
        p_publication_year: null, // No longer collected
        p_language: bookData.language,
        p_publisher: null, // No longer collected
        p_description: bookData.description || null,
        p_total_quantity: bookData.total_quantity,
        p_condition: bookData.condition,
        p_storage_location: null, // No longer collected
        p_cost_per_unit: null, // No longer collected
        p_minimum_threshold: bookData.minimum_threshold,
        p_notes: bookData.notes || null,
      });

      if (error) {
        console.error('Error adding book:', error);
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['books'] });
    },
  });

  // Update book mutation
  const updateBookMutation = useMutation({
    mutationFn: async ({ bookId, bookData }: { bookId: string; bookData: Partial<BookFormData> }): Promise<void> => {
      if (!canManageBooks) {
        throw new Error('Access denied. Only admins can update books.');
      }

      const { error } = await supabase
        .from('books')
        .update({
          title: bookData.title,
          author: bookData.author,
          isbn: bookData.isbn,
          publication_year: bookData.publication_year,
          category: bookData.category,
          grade_level: bookData.grade_level,
          language: bookData.language,
          publisher: bookData.publisher,
          description: bookData.description,
        })
        .eq('id', bookId);

      if (error) {
        console.error('Error updating book:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['books'] });
    },
  });

  // Delete book mutation
  const deleteBookMutation = useMutation({
    mutationFn: async (bookId: string): Promise<void> => {
      if (!canManageBooks) {
        throw new Error('Access denied. Only admins can delete books.');
      }

      const { error } = await supabase
        .from('books')
        .delete()
        .eq('id', bookId);

      if (error) {
        console.error('Error deleting book:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['books'] });
    },
  });

  // Get low stock books
  const { data: lowStockBooks = [], isLoading: isLoadingLowStock } = useQuery({
    queryKey: ['books', 'low-stock'],
    queryFn: async (): Promise<BookWithInventory[]> => {
      const { data, error } = await supabase
        .rpc('get_books_with_inventory');
      
      if (error) {
        console.error('Error fetching low stock books:', error);
        throw error;
      }
      
      // Filter books where available_quantity <= minimum_threshold
      return (data || []).filter((book: BookWithInventory) => 
        book.available_quantity <= book.minimum_threshold
      );
    },
    enabled: !!profile,
  });

  // Function to refresh book data
  const refreshBooks = () => {
    queryClient.invalidateQueries({ queryKey: ['books'] });
  };

  return {
    // Data
    books,
    lowStockBooks,

    // Loading states
    isLoadingBooks,
    isLoadingLowStock,
    isAddingBook: addBookMutation.isPending,
    isUpdatingBook: updateBookMutation.isPending,
    isDeletingBook: deleteBookMutation.isPending,

    // Error states
    booksError,

    // Permissions
    canManageBooks,

    // Actions
    addBook: addBookMutation.mutateAsync,
    updateBook: updateBookMutation.mutateAsync,
    deleteBook: deleteBookMutation.mutateAsync,
    refreshBooks,
  };
};

export type { BookFormData, BookWithInventory };
