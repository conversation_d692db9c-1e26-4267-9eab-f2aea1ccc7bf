import React, { useState } from 'react';
import { 
  ChevronDown,
  ChevronRight,
  Menu,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useAccessControl } from '@/hooks/useAccessControl';
import { 
  navigationConfig, 
  getNavigationForRole, 
  legacyRouteRedirects,
  type NavigationItem 
} from '@/config/navigation';

interface NavigationProps {
  currentUser: { role: string; id: string } | null;
  currentView: string;
  onViewChange: (view: string) => void;
}

const NavigationSimplified: React.FC<NavigationProps> = ({ 
  currentUser, 
  currentView, 
  onViewChange 
}) => {
  const [expanded, setExpanded] = useState(true);
  const [openSections, setOpenSections] = useState<string[]>(['impact', 'help']);
  const { currentUser: accessUser } = useAccessControl();

  const toggleSection = (sectionId: string) => {
    setOpenSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  // Get navigation items for current user role
  const navItems = accessUser ? getNavigationForRole(accessUser.role) : [];

  // Handle navigation with legacy route support
  const handleNavigation = (route: string) => {
    // Check for legacy redirects
    const finalRoute = legacyRouteRedirects[route] || route;
    onViewChange(finalRoute);
  };

  // Check if current view matches item (including legacy routes)
  const isCurrentView = (route: string) => {
    if (currentView === route) return true;
    
    // Check if current view is a legacy route that redirects to this route
    const redirectedRoute = legacyRouteRedirects[currentView];
    return redirectedRoute === route;
  };

  const renderNavigationItem = (item: NavigationItem) => {
    const Icon = item.icon;
    const hasChildren = item.children && item.children.length > 0;
    const isOpen = openSections.includes(item.id);
    const isCurrent = isCurrentView(item.route);

    if (hasChildren) {
      return (
        <div key={item.id} className="mb-1">
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-between text-left font-normal h-10 px-3",
              isCurrent && "bg-purple-50 text-purple-700 border-r-2 border-purple-600"
            )}
            onClick={() => toggleSection(item.id)}
          >
            <div className="flex items-center">
              <Icon className="h-4 w-4 mr-3" />
              <span className="text-sm">{item.label}</span>
            </div>
            {isOpen ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
          
          {isOpen && (
            <div className="ml-4 mt-1 space-y-1">
              {item.children?.map((child) => {
                const ChildIcon = child.icon;
                const isChildCurrent = isCurrentView(child.route);
                
                return (
                  <Button
                    key={child.id}
                    variant="ghost"
                    className={cn(
                      "w-full justify-start text-left font-normal h-9 px-3",
                      isChildCurrent && "bg-purple-50 text-purple-700 border-r-2 border-purple-600"
                    )}
                    onClick={() => handleNavigation(child.route)}
                  >
                    <ChildIcon className="h-4 w-4 mr-3" />
                    <span className="text-sm">{child.label}</span>
                  </Button>
                );
              })}
            </div>
          )}
        </div>
      );
    }

    return (
      <Button
        key={item.id}
        variant="ghost"
        className={cn(
          "w-full justify-start text-left font-normal h-10 px-3 mb-1",
          isCurrent && "bg-purple-50 text-purple-700 border-r-2 border-purple-600"
        )}
        onClick={() => handleNavigation(item.route)}
      >
        <Icon className="h-4 w-4 mr-3" />
        <span className="text-sm">{item.label}</span>
      </Button>
    );
  };

  return (
    <div className="bg-white h-full">
      {/* Sidebar Navigation */}
      <div className="h-full flex flex-col max-h-screen">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              iLead Field Track
            </h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setExpanded(!expanded)}
              className="lg:hidden"
            >
              {expanded ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {/* Navigation Items */}
        <div className="flex-1 p-3 overflow-y-auto">
          <nav className="space-y-1">
            {navItems.map(renderNavigationItem)}
          </nav>
        </div>

        {/* User Info */}
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-medium">
                {currentUser?.name?.charAt(0) || 'U'}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {currentUser?.name || 'User'}
              </p>
              <p className="text-xs text-gray-500 capitalize">
                {currentUser?.role?.replace('_', ' ') || 'Role'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NavigationSimplified;
