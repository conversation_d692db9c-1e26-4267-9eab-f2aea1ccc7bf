/**
 * Photo Gallery Component
 * Displays photos in a grid with validation and modal viewing
 */

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Camera, 
  AlertCircle, 
  Loader2, 
  Eye, 
  Download,
  RefreshCw,
  ImageIcon
} from 'lucide-react';
import { useSimplePhotoValidation } from '@/hooks/usePhotoValidation';
import PhotoModal from './PhotoModal';
import { toast } from 'sonner';

// ============================================================================
// TYPES
// ============================================================================

export interface PhotoGalleryProps {
  photos: string[];
  title?: string;
  description?: string;
  maxDisplayPhotos?: number;
  showValidationStatus?: boolean;
  allowDownload?: boolean;
  gridCols?: 2 | 3 | 4;
  aspectRatio?: 'square' | 'video' | 'auto';
  className?: string;
}

// ============================================================================
// PHOTO GALLERY COMPONENT
// ============================================================================

export const PhotoGallery: React.FC<PhotoGalleryProps> = ({
  photos,
  title = "Photos",
  description,
  maxDisplayPhotos,
  showValidationStatus = true,
  allowDownload = true,
  gridCols = 3,
  aspectRatio = 'square',
  className = '',
}) => {
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState<number | null>(null);
  const [showAllPhotos, setShowAllPhotos] = useState(false);

  // Validate photos
  const {
    validPhotos,
    brokenPhotos,
    isLoading,
    isPhotoValid,
    hasValidPhotos,
    hasBrokenPhotos,
    validationRate,
  } = useSimplePhotoValidation(photos);

  // Determine which photos to display
  const displayPhotos = showAllPhotos || !maxDisplayPhotos 
    ? photos 
    : photos.slice(0, maxDisplayPhotos);
  
  const hasMorePhotos = maxDisplayPhotos && photos.length > maxDisplayPhotos && !showAllPhotos;

  // Grid class mapping
  const gridClasses = {
    2: 'grid-cols-2',
    3: 'grid-cols-2 md:grid-cols-3',
    4: 'grid-cols-2 md:grid-cols-4',
  };

  // Aspect ratio classes
  const aspectClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    auto: 'aspect-auto',
  };

  // Download all photos
  const downloadAllPhotos = async () => {
    if (!hasValidPhotos) {
      toast.error('No valid photos to download');
      return;
    }

    try {
      toast.info('Starting download of all photos...');
      
      for (let i = 0; i < validPhotos.length; i++) {
        const photo = validPhotos[i];
        const response = await fetch(photo);
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `photo_${i + 1}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        window.URL.revokeObjectURL(url);
        
        // Small delay between downloads
        if (i < validPhotos.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
      
      toast.success(`Downloaded ${validPhotos.length} photos`);
    } catch (error) {
      console.error('Download failed:', error);
      toast.error('Failed to download photos');
    }
  };

  // Handle photo click
  const handlePhotoClick = (index: number) => {
    const actualIndex = photos.indexOf(displayPhotos[index]);
    setSelectedPhotoIndex(actualIndex);
  };

  // If no photos, show empty state
  if (photos.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No photos available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Camera className="h-5 w-5" />
              <CardTitle>{title}</CardTitle>
              <Badge variant="secondary">
                {photos.length} photo{photos.length !== 1 ? 's' : ''}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              {showValidationStatus && (
                <>
                  {isLoading && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Loader2 className="h-3 w-3 animate-spin" />
                      Checking...
                    </Badge>
                  )}
                  
                  {!isLoading && hasValidPhotos && (
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      {validPhotos.length} valid
                    </Badge>
                  )}
                  
                  {!isLoading && hasBrokenPhotos && (
                    <Badge variant="destructive" className="flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {brokenPhotos.length} broken
                    </Badge>
                  )}
                </>
              )}
              
              {allowDownload && hasValidPhotos && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={downloadAllPhotos}
                  className="flex items-center gap-1"
                >
                  <Download className="h-3 w-3" />
                  Download All
                </Button>
              )}
            </div>
          </div>
          
          {description && (
            <p className="text-sm text-gray-600">{description}</p>
          )}
          
          {showValidationStatus && !isLoading && validationRate < 100 && (
            <div className="text-sm text-amber-600 flex items-center gap-1">
              <AlertCircle className="h-4 w-4" />
              {Math.round(validationRate)}% of photos are accessible
            </div>
          )}
        </CardHeader>
        
        <CardContent>
          <div className={`grid ${gridClasses[gridCols]} gap-4`}>
            {displayPhotos.map((photo, index) => {
              const isValid = isPhotoValid(photo);
              const isValidating = isLoading;
              
              return (
                <div
                  key={index}
                  className={`${aspectClasses[aspectRatio]} bg-gray-100 rounded-lg overflow-hidden cursor-pointer hover:opacity-80 transition-opacity relative group`}
                  onClick={() => handlePhotoClick(index)}
                >
                  {isValidating ? (
                    <div className="w-full h-full flex items-center justify-center">
                      <Loader2 className="h-6 w-6 text-gray-400 animate-spin" />
                    </div>
                  ) : isValid ? (
                    <>
                      <img
                        src={photo}
                        alt={`Photo ${index + 1}`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                          e.currentTarget.parentElement?.classList.add('bg-red-50');
                        }}
                      />
                      
                      {/* Hover overlay */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                        <Eye className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                      </div>
                    </>
                  ) : (
                    <div className="w-full h-full flex flex-col items-center justify-center bg-red-50 text-red-600">
                      <AlertCircle className="h-6 w-6 mb-2" />
                      <span className="text-xs text-center px-2">
                        Image unavailable
                      </span>
                    </div>
                  )}
                  
                  {/* Photo number badge */}
                  <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                    {index + 1}
                  </div>
                  
                  {/* Validation status indicator */}
                  {showValidationStatus && !isValidating && (
                    <div className={`absolute top-2 right-2 w-3 h-3 rounded-full ${
                      isValid ? 'bg-green-500' : 'bg-red-500'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
          
          {/* Show more button */}
          {hasMorePhotos && (
            <div className="mt-4 text-center">
              <Button
                variant="outline"
                onClick={() => setShowAllPhotos(true)}
                className="flex items-center gap-2"
              >
                <Eye className="h-4 w-4" />
                Show {photos.length - maxDisplayPhotos!} more photo{photos.length - maxDisplayPhotos! !== 1 ? 's' : ''}
              </Button>
            </div>
          )}
          
          {/* Validation summary */}
          {showValidationStatus && !isLoading && (hasBrokenPhotos || validationRate < 100) && (
            <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5" />
                <div className="text-sm">
                  <p className="text-amber-800 font-medium">Photo Accessibility Issues</p>
                  <p className="text-amber-700 mt-1">
                    {brokenPhotos.length} of {photos.length} photos could not be loaded. 
                    This may be due to deleted files or network issues.
                  </p>
                  {brokenPhotos.length > 0 && (
                    <details className="mt-2">
                      <summary className="cursor-pointer text-amber-600 hover:text-amber-800">
                        View broken photo URLs
                      </summary>
                      <div className="mt-2 space-y-1">
                        {brokenPhotos.slice(0, 3).map((url, index) => (
                          <p key={index} className="text-xs text-amber-600 break-all">
                            {url}
                          </p>
                        ))}
                        {brokenPhotos.length > 3 && (
                          <p className="text-xs text-amber-600">
                            ... and {brokenPhotos.length - 3} more
                          </p>
                        )}
                      </div>
                    </details>
                  )}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Photo Modal */}
      <PhotoModal
        photos={photos}
        initialIndex={selectedPhotoIndex || 0}
        isOpen={selectedPhotoIndex !== null}
        onClose={() => setSelectedPhotoIndex(null)}
        title={`${title} - Photo Gallery`}
        allowDownload={allowDownload}
      />
    </>
  );
};

export default PhotoGallery;
