/**
 * Advanced Query Cache Management
 * Provides intelligent caching strategies with automatic invalidation
 */

import { QueryClient } from '@tanstack/react-query';

// ============================================================================
// CACHE CONFIGURATION
// ============================================================================

export const CACHE_TIMES = {
  // Static data (rarely changes)
  STATIC: {
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  },
  
  // Semi-static data (changes occasionally)
  SEMI_STATIC: {
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  },
  
  // Dynamic data (changes frequently)
  DYNAMIC: {
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  },
  
  // Real-time data (changes constantly)
  REALTIME: {
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
  },
  
  // User-specific data
  USER_SPECIFIC: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
  },
} as const;

// ============================================================================
// CACHE KEYS
// ============================================================================

export const CACHE_KEYS = {
  // Schools (semi-static)
  schools: ['schools'] as const,
  schoolsPaginated: (page: number, filters: Record<string, unknown>) => 
    ['schools-paginated', page, filters] as const,
  
  // Staff (semi-static)
  staff: ['staff-members'] as const,
  staffPaginated: (page: number, filters: Record<string, unknown>) => 
    ['staff-paginated', page, filters] as const,
  
  // Tasks (dynamic)
  tasks: ['tasks'] as const,
  myTasks: (userId: string) => ['my-tasks', userId] as const,
  managedTasks: (userId: string) => ['managed-tasks', userId] as const,
  
  // Field reports (dynamic)
  fieldReports: ['field-reports'] as const,
  userFieldReports: (userId: string) => ['field-reports', userId] as const,
  
  // Attendance (real-time)
  attendance: ['field-staff-attendance'] as const,
  userAttendance: (userId: string, date: string) => ['attendance', userId, date] as const,
  
  // Dashboard (dynamic)
  dashboard: ['dashboard-metrics'] as const,
  dashboardOptimized: ['dashboard-optimized'] as const,
  
  // Activities (real-time)
  activities: ['activities'] as const,
  userActivities: (userId: string) => ['user-activities', userId] as const,
  
  // Notifications (real-time)
  notifications: (userId: string) => ['notifications', userId] as const,
  unreadCount: (userId: string) => ['unread-notification-count', userId] as const,
  
  // Storage (semi-static)
  storage: ['storage-usage'] as const,
  storageWarning: ['storage-warning'] as const,
} as const;

// ============================================================================
// CACHE INVALIDATION PATTERNS
// ============================================================================

export const INVALIDATION_PATTERNS = {
  // When a school is created/updated/deleted
  schools: [
    CACHE_KEYS.schools,
    ['schools-paginated'],
    CACHE_KEYS.dashboard,
    CACHE_KEYS.activities,
  ],
  
  // When staff is created/updated/deleted
  staff: [
    CACHE_KEYS.staff,
    ['staff-paginated'],
    CACHE_KEYS.dashboard,
    CACHE_KEYS.activities,
  ],
  
  // When a task is created/updated/deleted
  tasks: [
    CACHE_KEYS.tasks,
    ['my-tasks'],
    ['managed-tasks'],
    CACHE_KEYS.dashboard,
    CACHE_KEYS.activities,
  ],
  
  // When a field report is created/updated/deleted
  fieldReports: [
    CACHE_KEYS.fieldReports,
    ['field-reports'],
    CACHE_KEYS.dashboard,
    CACHE_KEYS.activities,
  ],
  
  // When attendance is recorded
  attendance: [
    CACHE_KEYS.attendance,
    ['attendance'],
    CACHE_KEYS.dashboard,
    CACHE_KEYS.activities,
  ],
  
  // When a notification is created/read
  notifications: [
    ['notifications'],
    ['unread-notification-count'],
    CACHE_KEYS.activities,
  ],
} as const;

// ============================================================================
// CACHE MANAGER CLASS
// ============================================================================

export class QueryCacheManager {
  private queryClient: QueryClient;
  private invalidationQueue: Set<string> = new Set();
  private batchInvalidationTimer: NodeJS.Timeout | null = null;

  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
  }

  /**
   * Get cache configuration for different data types
   */
  getCacheConfig(type: keyof typeof CACHE_TIMES) {
    return CACHE_TIMES[type];
  }

  /**
   * Invalidate cache with batching to prevent excessive refetches
   */
  invalidateQueries(patterns: readonly (readonly string[] | string[])[], immediate = false) {
    patterns.forEach(pattern => {
      const key = Array.isArray(pattern) ? pattern.join(':') : pattern;
      this.invalidationQueue.add(key);
    });

    if (immediate) {
      this.flushInvalidationQueue();
    } else {
      this.scheduleInvalidation();
    }
  }

  /**
   * Schedule batched invalidation
   */
  private scheduleInvalidation() {
    if (this.batchInvalidationTimer) {
      clearTimeout(this.batchInvalidationTimer);
    }

    this.batchInvalidationTimer = setTimeout(() => {
      this.flushInvalidationQueue();
    }, 100); // 100ms batch window
  }

  /**
   * Execute all queued invalidations
   */
  private flushInvalidationQueue() {
    const patterns = Array.from(this.invalidationQueue);
    this.invalidationQueue.clear();

    patterns.forEach(pattern => {
      const queryKey = pattern.split(':');
      this.queryClient.invalidateQueries({ queryKey });
    });

    if (this.batchInvalidationTimer) {
      clearTimeout(this.batchInvalidationTimer);
      this.batchInvalidationTimer = null;
    }
  }

  /**
   * Prefetch related data
   */
  async prefetchRelatedData(type: string, data: unknown) {
    switch (type) {
      case 'school': {
        // Prefetch staff for this school
        const schoolData = data as { id: string };
        await this.queryClient.prefetchQuery({
          queryKey: ['school-staff', schoolData.id],
          staleTime: CACHE_TIMES.SEMI_STATIC.staleTime,
        });
        break;
      }

      case 'task': {
        // Prefetch task assignee profile
        const task = data as { assigned_to?: string };
        if (task.assigned_to) {
          await this.queryClient.prefetchQuery({
            queryKey: ['profile', task.assigned_to],
            staleTime: CACHE_TIMES.USER_SPECIFIC.staleTime,
          });
        }
        break;
      }

      case 'field_report': {
        // Prefetch school and staff data
        const report = data as { school_id?: string; staff_id?: string };
        if (report.school_id) {
          await this.queryClient.prefetchQuery({
            queryKey: ['school', report.school_id],
            staleTime: CACHE_TIMES.SEMI_STATIC.staleTime,
          });
        }
        break;
      }
    }
  }

  /**
   * Smart cache warming for frequently accessed data
   */
  async warmCache(userId: string, userRole: string) {
    const prefetchPromises: Promise<unknown>[] = [];

    // Always prefetch user-specific data
    prefetchPromises.push(
      this.queryClient.prefetchQuery({
        queryKey: CACHE_KEYS.myTasks(userId),
        staleTime: CACHE_TIMES.DYNAMIC.staleTime,
      }),
      this.queryClient.prefetchQuery({
        queryKey: CACHE_KEYS.notifications(userId),
        staleTime: CACHE_TIMES.REALTIME.staleTime,
      }),
      this.queryClient.prefetchQuery({
        queryKey: CACHE_KEYS.unreadCount(userId),
        staleTime: CACHE_TIMES.REALTIME.staleTime,
      })
    );

    // Role-specific prefetching
    if (userRole === 'admin' || userRole === 'program_officer') {
      prefetchPromises.push(
        this.queryClient.prefetchQuery({
          queryKey: CACHE_KEYS.dashboard,
          staleTime: CACHE_TIMES.DYNAMIC.staleTime,
        }),
        this.queryClient.prefetchQuery({
          queryKey: CACHE_KEYS.schools,
          staleTime: CACHE_TIMES.SEMI_STATIC.staleTime,
        }),
        this.queryClient.prefetchQuery({
          queryKey: CACHE_KEYS.staff,
          staleTime: CACHE_TIMES.SEMI_STATIC.staleTime,
        })
      );
    }

    if (userRole === 'field_staff') {
      const today = new Date().toISOString().split('T')[0];
      prefetchPromises.push(
        this.queryClient.prefetchQuery({
          queryKey: CACHE_KEYS.userAttendance(userId, today),
          staleTime: CACHE_TIMES.REALTIME.staleTime,
        }),
        this.queryClient.prefetchQuery({
          queryKey: CACHE_KEYS.userFieldReports(userId),
          staleTime: CACHE_TIMES.DYNAMIC.staleTime,
        })
      );
    }

    await Promise.allSettled(prefetchPromises);
    console.log('🔥 Cache warmed for user:', userId, 'role:', userRole);
  }

  /**
   * Clean up stale cache entries
   */
  cleanupStaleCache() {
    // Remove queries that haven't been used in the last hour
    this.queryClient.getQueryCache().getAll().forEach(query => {
      const lastUpdated = query.state.dataUpdatedAt;
      const oneHourAgo = Date.now() - 60 * 60 * 1000;
      
      if (lastUpdated < oneHourAgo && query.getObserversCount() === 0) {
        this.queryClient.removeQueries({ queryKey: query.queryKey });
      }
    });
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    const cache = this.queryClient.getQueryCache();
    const queries = cache.getAll();
    
    const stats = {
      totalQueries: queries.length,
      activeQueries: queries.filter(q => q.getObserversCount() > 0).length,
      staleQueries: queries.filter(q => q.isStale()).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
      loadingQueries: queries.filter(q => q.state.status === 'pending').length,
      cacheSize: this.estimateCacheSize(queries),
    };
    
    return stats;
  }

  /**
   * Estimate cache size (rough calculation)
   */
  private estimateCacheSize(queries: unknown[]): string {
    const estimatedBytes = queries.length * 1024; // Rough estimate: 1KB per query
    
    if (estimatedBytes < 1024) return `${estimatedBytes}B`;
    if (estimatedBytes < 1024 * 1024) return `${(estimatedBytes / 1024).toFixed(1)}KB`;
    return `${(estimatedBytes / 1024 / 1024).toFixed(1)}MB`;
  }

  /**
   * Force refresh all active queries
   */
  async refreshAllActiveQueries() {
    const activeQueries = this.queryClient.getQueryCache()
      .getAll()
      .filter(query => query.getObserversCount() > 0);

    const refreshPromises = activeQueries.map(query =>
      this.queryClient.refetchQueries({ queryKey: query.queryKey })
    );

    await Promise.allSettled(refreshPromises);
    console.log('🔄 Refreshed all active queries:', activeQueries.length);
  }
}

// ============================================================================
// CACHE UTILITIES
// ============================================================================

/**
 * Create optimized query options with appropriate caching
 */
export function createOptimizedQueryOptions<T>(
  type: keyof typeof CACHE_TIMES,
  options: {
    queryKey: readonly unknown[];
    queryFn: () => Promise<T>;
    enabled?: boolean;
    retry?: number;
    refetchOnWindowFocus?: boolean;
  }
) {
  const cacheConfig = CACHE_TIMES[type];
  
  return {
    ...options,
    staleTime: cacheConfig.staleTime,
    gcTime: cacheConfig.gcTime,
    retry: options.retry ?? 2,
    refetchOnWindowFocus: options.refetchOnWindowFocus ?? false,
    refetchOnReconnect: true,
    refetchIntervalInBackground: false,
  };
}

/**
 * Create cache-aware mutation options
 */
export function createOptimizedMutationOptions<T, V>(
  invalidationPattern: readonly (readonly string[] | string[])[],
  cacheManager: QueryCacheManager,
  options: {
    mutationFn: (variables: V) => Promise<T>;
    onSuccess?: (data: T, variables: V) => void;
    onError?: (error: Error, variables: V) => void;
  }
) {
  return {
    ...options,
    onSuccess: (data: T, variables: V) => {
      // Invalidate related cache entries
      cacheManager.invalidateQueries(invalidationPattern);
      
      // Call custom onSuccess if provided
      options.onSuccess?.(data, variables);
    },
    onError: (error: Error, variables: V) => {
      console.error('Mutation error:', error);
      options.onError?.(error, variables);
    },
  };
}

// Export singleton instance
export const queryCacheManager = new QueryCacheManager(new QueryClient());
