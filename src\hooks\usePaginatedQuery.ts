/**
 * Paginated Query Hook
 * Provides standardized pagination for all data fetching operations
 */

import { useState, useCallback, useMemo } from 'react';
import { useInfiniteQuery, useQuery, UseInfiniteQueryOptions, UseQueryOptions } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

// ============================================================================
// TYPES
// ============================================================================

export interface PaginationConfig {
  pageSize: number;
  initialPage?: number;
  maxPages?: number;
  prefetchNextPage?: boolean;
}

export interface PaginatedResult<T> {
  data: T[];
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  currentPage: number;
  totalPages: number;
  pageSize: number;
}

export interface PaginatedQueryOptions<T> {
  table: string;
  select?: string;
  filters?: Record<string, unknown>;
  orderBy?: { column: string; ascending?: boolean }[];
  searchColumn?: string;
  searchTerm?: string;
  pagination: PaginationConfig;
  queryKey: string[];
  enabled?: boolean;
  staleTime?: number;
  gcTime?: number;
}

export interface InfinitePaginatedQueryOptions<T> extends Omit<PaginatedQueryOptions<T>, 'pagination'> {
  pageSize: number;
  maxPages?: number;
}

// ============================================================================
// PAGINATED QUERY HOOK
// ============================================================================

export const usePaginatedQuery = <T = unknown>(options: PaginatedQueryOptions<T>) => {
  const [currentPage, setCurrentPage] = useState(options.pagination.initialPage || 1);
  const [searchTerm, setSearchTerm] = useState('');

  const queryFn = useCallback(async (): Promise<PaginatedResult<T>> => {
    const { pageSize } = options.pagination;
    const offset = (currentPage - 1) * pageSize;

    // Build base query
    let query = supabase
      .from(options.table)
      .select(options.select || '*', { count: 'exact' });

    // Apply filters
    if (options.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            query = query.in(key, value);
          } else if (typeof value === 'string' && value.includes('*')) {
            query = query.like(key, value.replace(/\*/g, '%'));
          } else {
            query = query.eq(key, value);
          }
        }
      });
    }

    // Apply search
    if (searchTerm && options.searchColumn) {
      query = query.ilike(options.searchColumn, `%${searchTerm}%`);
    }

    // Apply ordering
    if (options.orderBy) {
      options.orderBy.forEach(({ column, ascending = true }) => {
        query = query.order(column, { ascending });
      });
    }

    // Apply pagination
    query = query.range(offset, offset + pageSize - 1);

    const { data, error, count } = await query;

    if (error) {
      console.error(`Error fetching paginated data from ${options.table}:`, error);
      throw error;
    }

    const totalCount = count || 0;
    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      data: (data as T[]) || [],
      totalCount,
      hasNextPage: currentPage < totalPages,
      hasPreviousPage: currentPage > 1,
      currentPage,
      totalPages,
      pageSize,
    };
  }, [
    options.table,
    options.select,
    options.filters,
    options.orderBy,
    options.searchColumn,
    options.pagination,
    currentPage,
    searchTerm,
  ]);

  const query = useQuery({
    queryKey: [...options.queryKey, currentPage, searchTerm, options.filters],
    queryFn,
    enabled: options.enabled,
    staleTime: options.staleTime || 5 * 60 * 1000, // 5 minutes
    gcTime: options.gcTime || 10 * 60 * 1000, // 10 minutes
  });

  // Navigation functions
  const goToPage = useCallback((page: number) => {
    if (page >= 1 && (!query.data || page <= query.data.totalPages)) {
      setCurrentPage(page);
    }
  }, [query.data]);

  const nextPage = useCallback(() => {
    if (query.data?.hasNextPage) {
      setCurrentPage(prev => prev + 1);
    }
  }, [query.data?.hasNextPage]);

  const previousPage = useCallback(() => {
    if (query.data?.hasPreviousPage) {
      setCurrentPage(prev => prev - 1);
    }
  }, [query.data?.hasPreviousPage]);

  const firstPage = useCallback(() => {
    setCurrentPage(1);
  }, []);

  const lastPage = useCallback(() => {
    if (query.data?.totalPages) {
      setCurrentPage(query.data.totalPages);
    }
  }, [query.data?.totalPages]);

  // Search function
  const search = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset to first page when searching
  }, []);

  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setCurrentPage(1);
  }, []);

  return {
    // Data
    data: query.data?.data || [],
    totalCount: query.data?.totalCount || 0,
    hasNextPage: query.data?.hasNextPage || false,
    hasPreviousPage: query.data?.hasPreviousPage || false,
    currentPage: query.data?.currentPage || 1,
    totalPages: query.data?.totalPages || 0,
    pageSize: query.data?.pageSize || options.pagination.pageSize,
    
    // Loading states
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    error: query.error,
    
    // Navigation
    goToPage,
    nextPage,
    previousPage,
    firstPage,
    lastPage,
    
    // Search
    search,
    clearSearch,
    searchTerm,
    
    // Utilities
    refetch: query.refetch,
    isRefetching: query.isRefetching,
  };
};

// ============================================================================
// INFINITE PAGINATED QUERY HOOK
// ============================================================================

export const useInfinitePaginatedQuery = <T = unknown>(options: InfinitePaginatedQueryOptions<T>) => {
  const [searchTerm, setSearchTerm] = useState('');

  const queryFn = useCallback(async ({ pageParam = 0 }): Promise<PaginatedResult<T>> => {
    const offset = pageParam * options.pageSize;

    // Build base query
    let query = supabase
      .from(options.table)
      .select(options.select || '*', { count: 'exact' });

    // Apply filters
    if (options.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            query = query.in(key, value);
          } else if (typeof value === 'string' && value.includes('*')) {
            query = query.like(key, value.replace(/\*/g, '%'));
          } else {
            query = query.eq(key, value);
          }
        }
      });
    }

    // Apply search
    if (searchTerm && options.searchColumn) {
      query = query.ilike(options.searchColumn, `%${searchTerm}%`);
    }

    // Apply ordering
    if (options.orderBy) {
      options.orderBy.forEach(({ column, ascending = true }) => {
        query = query.order(column, { ascending });
      });
    }

    // Apply pagination
    query = query.range(offset, offset + options.pageSize - 1);

    const { data, error, count } = await query;

    if (error) {
      console.error(`Error fetching infinite paginated data from ${options.table}:`, error);
      throw error;
    }

    const totalCount = count || 0;
    const currentPage = pageParam + 1;
    const totalPages = Math.ceil(totalCount / options.pageSize);

    return {
      data: (data as T[]) || [],
      totalCount,
      hasNextPage: currentPage < totalPages,
      hasPreviousPage: currentPage > 1,
      currentPage,
      totalPages,
      pageSize: options.pageSize,
    };
  }, [
    options.table,
    options.select,
    options.filters,
    options.orderBy,
    options.searchColumn,
    options.pageSize,
    searchTerm,
  ]);

  const query = useInfiniteQuery({
    queryKey: [...options.queryKey, searchTerm, options.filters],
    queryFn,
    getNextPageParam: (lastPage, allPages) => {
      if (lastPage.hasNextPage && (!options.maxPages || allPages.length < options.maxPages)) {
        return allPages.length; // Next page index
      }
      return undefined;
    },
    enabled: options.enabled,
    staleTime: options.staleTime || 5 * 60 * 1000, // 5 minutes
    gcTime: options.gcTime || 10 * 60 * 1000, // 10 minutes
  });

  // Flatten all pages data
  const allData = useMemo(() => {
    return query.data?.pages.flatMap(page => page.data) || [];
  }, [query.data?.pages]);

  // Search function
  const search = useCallback((term: string) => {
    setSearchTerm(term);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchTerm('');
  }, []);

  return {
    // Data
    data: allData,
    pages: query.data?.pages || [],
    totalCount: query.data?.pages[0]?.totalCount || 0,
    hasNextPage: query.hasNextPage,
    
    // Loading states
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    isFetchingNextPage: query.isFetchingNextPage,
    error: query.error,
    
    // Actions
    fetchNextPage: query.fetchNextPage,
    
    // Search
    search,
    clearSearch,
    searchTerm,
    
    // Utilities
    refetch: query.refetch,
    isRefetching: query.isRefetching,
  };
};
