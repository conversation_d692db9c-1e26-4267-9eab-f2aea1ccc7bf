# Comprehensive Admin Dashboard

## Overview

The Comprehensive Admin Dashboard provides a bird's eye view of all field activities in a minimalistic yet comprehensive manner. It's designed for administrators and program officers to understand program performance at a glance.

## Features

### 🎯 Executive Summary Cards
- **Field Staff Activity**: Real-time staff check-in status and compliance rates
- **Schools Reached**: Coverage percentage and total schools served
- **Students Impacted**: Monthly student reach metrics
-

### 📊 Real-Time Activity Monitor
- **Today's Activities**: Live count of check-ins, sessions, reports, and visits
- **Live Staff Status**: Current location and activity status of field staff
- **Critical Alerts**: System alerts requiring immediate attention

### 📈 Performance Trends
- **Weekly Activity Heatmap**: Visual calendar showing activity intensity
- **Metrics Trend Charts**: Line charts for activities, students reached, and hours worked
- **Performance Radar**: Multi-dimensional performance visualization
- **Activity Distribution**: Pie chart breakdown of activity types

### 🎯 Impact Indicators
- **Progress Tracking**: Visual progress bars for key targets
- **Quality Metrics**: Attendance rates, report quality, response times
- **Top Performers**: Ranking of best-performing field staff

## Components

### Core Components
- `ComprehensiveAdminDashboard.tsx` - Main dashboard container
- `ExecutiveSummaryCards.tsx` - Top-level KPI cards
- `RealTimeActivityMonitor.tsx` - Live activity tracking
- `PerformanceTrends.tsx` - Charts and trend analysis
- `ImpactIndicators.tsx` - Progress and quality metrics

### Data Hooks
- `useDashboardMetrics()` - Aggregated performance metrics
- `useActivitySummary()` - Real-time activity data
- `useStaffPerformance()` - Staff performance rankings

## Usage

### For Administrators
```tsx
import { ComprehensiveAdminDashboard } from '@/components/dashboard';

// The dashboard automatically shows for admin and program_officer roles
<ComprehensiveAdminDashboard onViewChange={handleViewChange} />
```

### Access Control
- **Admin**: Full access to all dashboard features
- **Program Officer**: Full access to all dashboard features  
- **Field Staff**: Redirected to standard dashboard
- **Other Roles**: Access denied with appropriate message

## Data Sources

The dashboard aggregates data from:
- `field_staff_attendance` - Check-in/out tracking
- `daily_timesheets` - Work hours and productivity
- `field_reports` - Activity reports and outcomes
- `tasks` - Task completion rates
- `schools` - School coverage metrics
- `profiles` - Staff information and status

## Real-Time Features

### Auto-Refresh
- **Metrics**: Every 5 minutes
- **Activity Summary**: Every 1 minute
- **Staff Performance**: Every 5 minutes

### Live Indicators
- Online/offline status
- Last update timestamp
- Active staff count
- Critical alert badges

## Customization

### Color Coding
- **Green**: Targets met, good performance
- **Yellow**: Attention needed, approaching deadlines
- **Red**: Critical issues, immediate action required
- **Blue**: Information, neutral status
- **Purple**: Special programs, leadership activities

### Interactive Features
- Click metric cards to drill down to detailed views
- Refresh button for manual data updates
- Export functionality for stakeholder reports
- Quick action buttons for common tasks

## Performance Considerations

- Uses React Query for efficient data caching
- Implements stale-while-revalidate pattern
- Optimized re-render cycles with proper memoization
- Lazy loading for chart components

## Future Enhancements

### Phase 2 Features
- Geographic mapping with school locations
- Advanced filtering and date range selection
- Predictive analytics and trend forecasting
- Custom dashboard layouts

### Phase 3 Features
- Automated insights and recommendations
- Anomaly detection algorithms
- Integration with external reporting tools
- Mobile-optimized responsive design

## Troubleshooting

### Common Issues
1. **Data not loading**: Check network connection and Supabase status
2. **Slow performance**: Verify query optimization and caching
3. **Missing permissions**: Ensure user has admin/program_officer role
4. **Chart rendering issues**: Check recharts dependency and data format

### Debug Mode
Enable debug logging by setting `localStorage.debug = 'dashboard:*'` in browser console.
