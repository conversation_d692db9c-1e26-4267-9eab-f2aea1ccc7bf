# Role Migration Guide

## Overview

This guide provides step-by-step instructions for migrating to the new user role system in the iLead Uganda application.

## Pre-Migration Checklist

### 1. Backup Current Data
```sql
-- Backup current user roles
CREATE TABLE user_roles_backup AS 
SELECT id, name, role, created_at 
FROM profiles;
```

### 2. Review Current Users
```sql
-- Check current role distribution
SELECT role, COUNT(*) as user_count 
FROM profiles 
GROUP BY role 
ORDER BY user_count DESC;
```

### 3. Identify Special Access Needs
- Review which users need book management access
- Identify external partners vs internal staff
- Determine social media management responsibilities

## Migration Steps

### Step 1: Run Database Migrations

Execute the migrations in order:

```bash
# 1. Add new roles to the database
supabase migration up 031_add_new_user_roles.sql

# 2. Update RLS policies
supabase migration up 032_update_rls_policies_for_new_roles.sql

# 3. Update server validation functions
supabase migration up 033_update_server_validation_for_new_roles.sql
```

### Step 2: Update User Roles

#### Automatic Updates
- New users will automatically get 'staff' role
- Existing users keep their current roles
- No immediate action required

#### Manual Role Updates (if needed)

```sql
-- Update specific users to accountant role
UPDATE profiles 
SET role = 'accountant' 
WHERE id IN ('user-id-1', 'user-id-2');

-- Update external collaborators to partner role
UPDATE profiles 
SET role = 'partner' 
WHERE id IN ('partner-id-1', 'partner-id-2');

-- Update social media staff
UPDATE profiles 
SET role = 'social_media_manager' 
WHERE id IN ('social-media-id-1');
```

### Step 3: Verify Access Control

#### Test Role Permissions
1. **Admin Access**: Verify full system access
2. **Program Officer Access**: Check elevated permissions
3. **Accountant Access**: Test book management features
4. **Field-Level Roles**: Verify data isolation

#### Validation Queries
```sql
-- Verify role assignments
SELECT role, COUNT(*) as count 
FROM profiles 
GROUP BY role;

-- Check for any null roles
SELECT * FROM profiles WHERE role IS NULL;
```

## Role Assignment Guidelines

### When to Use Each Role

#### Staff (Default)
- General internal employees
- Field workers without special access needs
- Default for new user creation

#### Field Staff (Legacy)
- Existing users who should maintain current access
- Specific field operations staff
- Backward compatibility scenarios

#### Partner
- External organizations
- Consultants and contractors
- Temporary collaborators
- NGO partners

#### Accountant
- Financial management staff
- Inventory managers
- Users who need book management access
- Budget and resource coordinators

#### Social Media Manager
- Communications team
- Marketing staff
- Public relations personnel
- Content creators

### Access Level Summary

| Role | Data Access | Book Management | User Management | System Admin |
|------|-------------|-----------------|-----------------|--------------|
| Admin | All | ✓ | ✓ | ✓ |
| Program Officer | All | ✓ | Limited | ✗ |
| Accountant | Own | ✓ | ✗ | ✗ |
| Field Staff | Own | ✗ | ✗ | ✗ |
| Staff | Own | ✗ | ✗ | ✗ |
| Partner | Own | ✗ | ✗ | ✗ |
| Social Media Manager | Own | ✗ | ✗ | ✗ |

## Post-Migration Tasks

### 1. User Communication
- Notify users of role changes
- Provide training on new features (for accountants)
- Update user documentation

### 2. Monitor System Performance
- Check for any access issues
- Monitor error logs for permission problems
- Verify RLS policies are working correctly

### 3. Update Procedures
- Revise user onboarding process
- Update role assignment procedures
- Document new access patterns

## Troubleshooting

### Common Issues

#### Users Cannot Access Features
```sql
-- Check user role
SELECT id, name, role FROM profiles WHERE email = '<EMAIL>';

-- Verify RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public';
```

#### Book Management Access Issues
```sql
-- Verify accountant role assignment
SELECT * FROM profiles WHERE role = 'accountant';

-- Check book management permissions
SELECT * FROM books WHERE created_by = 'user-id';
```

#### Navigation Problems
- Clear browser cache
- Check for JavaScript errors
- Verify role-based navigation configuration

### Rollback Procedure (Emergency)

If issues arise, you can rollback roles:

```sql
-- Restore from backup
UPDATE profiles 
SET role = backup.role 
FROM user_roles_backup backup 
WHERE profiles.id = backup.id;
```

## Validation Checklist

After migration, verify:

- [ ] All users can log in successfully
- [ ] Role-based navigation works correctly
- [ ] Data access is properly restricted
- [ ] Book management works for accountants
- [ ] Admin functions are accessible
- [ ] Field reports can be created
- [ ] Task management functions properly
- [ ] No permission errors in logs

## Support and Resources

### Documentation
- [User Roles System Documentation](./USER_ROLES_SYSTEM.md)
- [RBAC Implementation Guide](../src/utils/rbac.ts)
- [Access Control Components](../src/components/common/AccessControl.tsx)

### Contact Information
- Technical Support: [<EMAIL>]
- System Administrator: [<EMAIL>]
- Development Team: [<EMAIL>]

## Monitoring and Maintenance

### Regular Checks
- Monthly role distribution review
- Quarterly access audit
- Annual permission review

### Performance Monitoring
- Monitor RLS policy performance
- Check for slow queries
- Review access patterns

### Security Auditing
- Regular permission audits
- Access log reviews
- Role assignment validation
