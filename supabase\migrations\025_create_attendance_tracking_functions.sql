-- Attendance Tracking System Functions
-- Migration 025: Database functions for comprehensive attendance tracking and reporting

-- Function to get comprehensive attendance data with filtering and pagination
CREATE OR REPLACE FUNCTION get_staff_attendance_records(
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL,
    p_staff_id UUID DEFAULT NULL,
    p_check_in_type VARCHAR(20) DEFAULT NULL,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0,
    p_order_by VARCHAR(50) DEFAULT 'attendance_date',
    p_order_direction VARCHAR(4) DEFAULT 'DESC'
)
RETURNS TABLE (
    id UUID,
    staff_id UUID,
    staff_name VARCHAR(255),
    staff_email VARCHAR(255),
    staff_role VARCHAR(50),
    attendance_date DATE,
    check_in_type VARCHAR(20),
    location_id UUID,
    location_name VARCHAR(255),
    location_address TEXT,
    check_in_time TIMESTAMP WITH TIME ZONE,
    check_out_time TIMESTAMP WITH TIME ZONE,
    total_duration_minutes INTEGER,
    status VARCHAR(20),
    distance_from_location DECIMAL(8,2),
    location_verified BOOLEAN,
    verification_method VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    query_text TEXT;
    order_clause TEXT;
BEGIN
    -- Check if user has permission (admin or program_officer only)
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Access denied. Only admins and program officers can view attendance records.';
    END IF;

    -- Validate order direction
    IF p_order_direction NOT IN ('ASC', 'DESC') THEN
        p_order_direction := 'DESC';
    END IF;

    -- Validate order by column
    IF p_order_by NOT IN ('attendance_date', 'staff_name', 'check_in_time', 'check_out_time', 'total_duration_minutes', 'location_name') THEN
        p_order_by := 'attendance_date';
    END IF;

    -- Build order clause
    order_clause := format('ORDER BY %I %s', p_order_by, p_order_direction);

    -- Build the main query
    query_text := '
        SELECT 
            fsa.id,
            fsa.staff_id,
            p.full_name as staff_name,
            p.email as staff_email,
            p.role as staff_role,
            fsa.attendance_date,
            fsa.check_in_type::VARCHAR(20),
            COALESCE(fsa.school_id, fsa.office_id) as location_id,
            COALESCE(s.name, ol.name) as location_name,
            COALESCE(s.location_description, ol.address) as location_address,
            fsa.check_in_time,
            fsa.check_out_time,
            fsa.total_duration_minutes,
            fsa.status,
            fsa.distance_from_school as distance_from_location,
            fsa.location_verified,
            fsa.verification_method,
            fsa.notes,
            fsa.created_at
        FROM field_staff_attendance fsa
        INNER JOIN profiles p ON fsa.staff_id = p.id
        LEFT JOIN schools s ON fsa.school_id = s.id AND fsa.check_in_type = ''school''
        LEFT JOIN office_locations ol ON fsa.office_id = ol.id AND fsa.check_in_type = ''office''
        WHERE 1=1';

    -- Add date range filter
    IF p_start_date IS NOT NULL THEN
        query_text := query_text || ' AND fsa.attendance_date >= $1';
    END IF;

    IF p_end_date IS NOT NULL THEN
        query_text := query_text || ' AND fsa.attendance_date <= $2';
    END IF;

    -- Add staff filter
    IF p_staff_id IS NOT NULL THEN
        query_text := query_text || ' AND fsa.staff_id = $3';
    END IF;

    -- Add check-in type filter
    IF p_check_in_type IS NOT NULL AND p_check_in_type IN ('school', 'office') THEN
        query_text := query_text || ' AND fsa.check_in_type = $4';
    END IF;

    -- Add ordering and pagination
    query_text := query_text || ' ' || order_clause || ' LIMIT $5 OFFSET $6';

    -- Execute the dynamic query
    RETURN QUERY EXECUTE query_text 
    USING p_start_date, p_end_date, p_staff_id, p_check_in_type, p_limit, p_offset;
END;
$$;

-- Function to get attendance summary statistics
CREATE OR REPLACE FUNCTION get_attendance_summary_stats(
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL,
    p_staff_id UUID DEFAULT NULL,
    p_check_in_type VARCHAR(20) DEFAULT NULL
)
RETURNS TABLE (
    total_records INTEGER,
    total_staff INTEGER,
    total_hours DECIMAL(10,2),
    school_checkins INTEGER,
    office_checkins INTEGER,
    completed_sessions INTEGER,
    active_sessions INTEGER,
    average_duration_minutes DECIMAL(10,2),
    total_distance_km DECIMAL(10,2)
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user has permission (admin or program_officer only)
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Access denied. Only admins and program officers can view attendance statistics.';
    END IF;

    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_records,
        COUNT(DISTINCT fsa.staff_id)::INTEGER as total_staff,
        ROUND(COALESCE(SUM(fsa.total_duration_minutes), 0) / 60.0, 2) as total_hours,
        COUNT(CASE WHEN fsa.check_in_type = 'school' THEN 1 END)::INTEGER as school_checkins,
        COUNT(CASE WHEN fsa.check_in_type = 'office' THEN 1 END)::INTEGER as office_checkins,
        COUNT(CASE WHEN fsa.status = 'completed' THEN 1 END)::INTEGER as completed_sessions,
        COUNT(CASE WHEN fsa.status = 'active' THEN 1 END)::INTEGER as active_sessions,
        ROUND(AVG(fsa.total_duration_minutes), 2) as average_duration_minutes,
        ROUND(COALESCE(SUM(fsa.distance_from_school), 0) / 1000.0, 2) as total_distance_km
    FROM field_staff_attendance fsa
    WHERE 1=1
        AND (p_start_date IS NULL OR fsa.attendance_date >= p_start_date)
        AND (p_end_date IS NULL OR fsa.attendance_date <= p_end_date)
        AND (p_staff_id IS NULL OR fsa.staff_id = p_staff_id)
        AND (p_check_in_type IS NULL OR fsa.check_in_type = p_check_in_type);
END;
$$;

-- Function to get staff list for filtering dropdown
CREATE OR REPLACE FUNCTION get_staff_for_attendance_filter()
RETURNS TABLE (
    id UUID,
    full_name VARCHAR(255),
    email VARCHAR(255),
    role VARCHAR(50),
    total_checkins INTEGER,
    last_checkin_date DATE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user has permission (admin or program_officer only)
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Access denied. Only admins and program officers can view staff data.';
    END IF;

    RETURN QUERY
    SELECT 
        p.id,
        p.full_name,
        p.email,
        p.role,
        COUNT(fsa.id)::INTEGER as total_checkins,
        MAX(fsa.attendance_date) as last_checkin_date
    FROM profiles p
    LEFT JOIN field_staff_attendance fsa ON p.id = fsa.staff_id
    WHERE p.role IN ('admin', 'program_officer', 'field_staff')
    GROUP BY p.id, p.full_name, p.email, p.role
    ORDER BY p.full_name;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_staff_attendance_records TO authenticated;
GRANT EXECUTE ON FUNCTION get_attendance_summary_stats TO authenticated;
GRANT EXECUTE ON FUNCTION get_staff_for_attendance_filter TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_field_staff_attendance_date_staff ON field_staff_attendance(attendance_date, staff_id);
CREATE INDEX IF NOT EXISTS idx_field_staff_attendance_type_date ON field_staff_attendance(check_in_type, attendance_date);
CREATE INDEX IF NOT EXISTS idx_field_staff_attendance_status ON field_staff_attendance(status);

-- Add helpful comments
COMMENT ON FUNCTION get_staff_attendance_records IS 'Retrieves comprehensive attendance records with filtering, pagination, and location details';
COMMENT ON FUNCTION get_attendance_summary_stats IS 'Provides summary statistics for attendance data within specified filters';
COMMENT ON FUNCTION get_staff_for_attendance_filter IS 'Returns staff list with attendance counts for filter dropdown';
