/**
 * Attendance Export Utilities
 * Handles Excel export functionality for attendance data
 */

import * as XLSX from 'xlsx';
import { format } from 'date-fns';
import { AttendanceRecord, AttendanceFilters, AttendanceSummaryStats } from '@/hooks/useAttendanceTracking';

export interface ExportOptions {
  includeStats?: boolean;
  filename?: string;
  sheetName?: string;
}

/**
 * Format duration in minutes to human-readable format
 */
function formatDuration(minutes?: number): string {
  if (!minutes || minutes <= 0) return '-';
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (hours === 0) {
    return `${remainingMinutes}m`;
  } else if (remainingMinutes === 0) {
    return `${hours}h`;
  } else {
    return `${hours}h ${remainingMinutes}m`;
  }
}

/**
 * Format distance in meters to human-readable format
 */
function formatDistance(meters?: number): string {
  if (!meters || meters <= 0) return '-';
  
  if (meters < 1000) {
    return `${Math.round(meters)}m`;
  } else {
    return `${(meters / 1000).toFixed(1)}km`;
  }
}

/**
 * Format time string to HH:MM format
 */
function formatTime(timeString?: string): string {
  if (!timeString) return '-';
  try {
    return format(new Date(timeString), 'HH:mm');
  } catch {
    return '-';
  }
}

/**
 * Format date string to readable format
 */
function formatDate(dateString: string): string {
  try {
    return format(new Date(dateString), 'MMM dd, yyyy');
  } catch {
    return dateString;
  }
}

/**
 * Generate filename for export based on filters and timestamp
 */
function generateFilename(filters: AttendanceFilters, customFilename?: string): string {
  if (customFilename) {
    return customFilename.endsWith('.xlsx') ? customFilename : `${customFilename}.xlsx`;
  }

  const timestamp = format(new Date(), 'yyyy-MM-dd_HH-mm');
  let filename = `attendance_export_${timestamp}`;

  // Add filter information to filename
  const filterParts = [];
  
  if (filters.startDate && filters.endDate) {
    filterParts.push(`${filters.startDate}_to_${filters.endDate}`);
  } else if (filters.startDate) {
    filterParts.push(`from_${filters.startDate}`);
  } else if (filters.endDate) {
    filterParts.push(`until_${filters.endDate}`);
  }

  if (filters.checkInType) {
    filterParts.push(filters.checkInType);
  }

  if (filterParts.length > 0) {
    filename += `_${filterParts.join('_')}`;
  }

  return `${filename}.xlsx`;
}

/**
 * Convert attendance records to Excel-friendly format
 */
function prepareAttendanceData(records: AttendanceRecord[]) {
  return records.map((record) => ({
    'Staff Name': record.staff_name,
    'Email': record.staff_email,
    'Role': record.staff_role,
    'Date': formatDate(record.attendance_date),
    'Check-in Type': record.check_in_type === 'school' ? 'School Visit' : 'Office',
    'Location': record.location_name,
    'Location Address': record.location_address || '-',
    'Check-in Time': formatTime(record.check_in_time),
    'Check-out Time': formatTime(record.check_out_time),
    'Duration': formatDuration(record.total_duration_minutes),
    'Status': record.status === 'active' ? 'Active' : 'Completed',
    'Distance from Location': formatDistance(record.distance_from_location),
    'Location Verified': record.location_verified ? 'Yes' : 'No',
    'Verification Method': record.verification_method,
    'Notes': record.notes || '-',
    'Created At': format(new Date(record.created_at), 'MMM dd, yyyy HH:mm'),
  }));
}

/**
 * Prepare summary statistics for Excel export
 */
function prepareSummaryData(stats: AttendanceSummaryStats, filters: AttendanceFilters) {
  const filterDescription = [];
  if (filters.startDate || filters.endDate) {
    if (filters.startDate && filters.endDate) {
      filterDescription.push(`Date Range: ${filters.startDate} to ${filters.endDate}`);
    } else if (filters.startDate) {
      filterDescription.push(`From: ${filters.startDate}`);
    } else {
      filterDescription.push(`Until: ${filters.endDate}`);
    }
  }
  if (filters.checkInType) {
    filterDescription.push(`Type: ${filters.checkInType === 'school' ? 'School Visits' : 'Office Check-ins'}`);
  }

  return [
    { 'Metric': 'Export Generated', 'Value': format(new Date(), 'MMM dd, yyyy HH:mm') },
    { 'Metric': 'Filters Applied', 'Value': filterDescription.length > 0 ? filterDescription.join(', ') : 'None' },
    { 'Metric': '', 'Value': '' }, // Empty row
    { 'Metric': 'Total Records', 'Value': stats.total_records.toLocaleString() },
    { 'Metric': 'Total Staff Members', 'Value': stats.total_staff.toString() },
    { 'Metric': 'Total Hours Tracked', 'Value': `${stats.total_hours.toFixed(1)}h` },
    { 'Metric': 'School Check-ins', 'Value': stats.school_checkins.toLocaleString() },
    { 'Metric': 'Office Check-ins', 'Value': stats.office_checkins.toLocaleString() },
    { 'Metric': 'Completed Sessions', 'Value': stats.completed_sessions.toLocaleString() },
    { 'Metric': 'Active Sessions', 'Value': stats.active_sessions.toLocaleString() },
    { 'Metric': 'Average Duration', 'Value': formatDuration(stats.average_duration_minutes) },
    { 'Metric': 'Total Distance', 'Value': `${stats.total_distance_km.toFixed(1)} km` },
  ];
}

/**
 * Export attendance records to Excel file
 */
export async function exportAttendanceToExcel(
  records: AttendanceRecord[],
  filters: AttendanceFilters,
  stats?: AttendanceSummaryStats,
  options: ExportOptions = {}
): Promise<void> {
  try {
    const {
      includeStats = true,
      filename,
      sheetName = 'Attendance Records',
    } = options;

    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Prepare attendance data
    const attendanceData = prepareAttendanceData(records);
    const attendanceWorksheet = XLSX.utils.json_to_sheet(attendanceData);

    // Add attendance sheet
    XLSX.utils.book_append_sheet(workbook, attendanceWorksheet, sheetName);

    // Add summary statistics sheet if requested and available
    if (includeStats && stats) {
      const summaryData = prepareSummaryData(stats, filters);
      const summaryWorksheet = XLSX.utils.json_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'Summary Statistics');
    }

    // Auto-size columns for better readability
    const attendanceSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[attendanceSheetName];
    
    // Set column widths
    const columnWidths = [
      { wch: 20 }, // Staff Name
      { wch: 25 }, // Email
      { wch: 15 }, // Role
      { wch: 12 }, // Date
      { wch: 15 }, // Check-in Type
      { wch: 25 }, // Location
      { wch: 30 }, // Location Address
      { wch: 12 }, // Check-in Time
      { wch: 12 }, // Check-out Time
      { wch: 10 }, // Duration
      { wch: 12 }, // Status
      { wch: 15 }, // Distance
      { wch: 12 }, // Verified
      { wch: 15 }, // Verification Method
      { wch: 30 }, // Notes
      { wch: 18 }, // Created At
    ];
    
    worksheet['!cols'] = columnWidths;

    // Generate filename
    const exportFilename = generateFilename(filters, filename);

    // Write and download the file
    XLSX.writeFile(workbook, exportFilename);

    console.log(`Attendance data exported to ${exportFilename}`);
  } catch (error) {
    console.error('Error exporting attendance data:', error);
    throw new Error('Failed to export attendance data. Please try again.');
  }
}

/**
 * Export only summary statistics to Excel
 */
export async function exportSummaryStatsToExcel(
  stats: AttendanceSummaryStats,
  filters: AttendanceFilters,
  options: ExportOptions = {}
): Promise<void> {
  try {
    const {
      filename,
      sheetName = 'Summary Statistics',
    } = options;

    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Prepare summary data
    const summaryData = prepareSummaryData(stats, filters);
    const summaryWorksheet = XLSX.utils.json_to_sheet(summaryData);

    // Add summary sheet
    XLSX.utils.book_append_sheet(workbook, summaryWorksheet, sheetName);

    // Set column widths
    const worksheet = workbook.Sheets[sheetName];
    worksheet['!cols'] = [
      { wch: 25 }, // Metric
      { wch: 20 }, // Value
    ];

    // Generate filename
    const exportFilename = generateFilename(filters, filename || 'attendance_summary');

    // Write and download the file
    XLSX.writeFile(workbook, exportFilename);

    console.log(`Summary statistics exported to ${exportFilename}`);
  } catch (error) {
    console.error('Error exporting summary statistics:', error);
    throw new Error('Failed to export summary statistics. Please try again.');
  }
}

/**
 * Check if XLSX library is available
 */
export function isExportAvailable(): boolean {
  return typeof XLSX !== 'undefined';
}
