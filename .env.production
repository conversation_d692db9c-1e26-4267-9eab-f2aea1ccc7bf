# =============================================================================
# iLead Field Track - Production Environment Configuration
# =============================================================================

# Supabase Configuration
# These should be set via your deployment platform's environment variables
VITE_SUPABASE_URL=https://bygrspebofyofymivmib.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ5Z3JzcGVib2Z5b2Z5bWl2bWliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwMzIxODgsImV4cCI6MjA2NDYwODE4OH0.xxfeix-6F42NmVWaQHE19nnDCxZmiMDs1_fyLb0-lgE

# Service Role Key - SET THIS VIA DEPLOYMENT PLATFORM
# DO NOT commit the actual service role key to version control
VITE_SUPABASE_SERVICE_ROLE_KEY=

# Application Configuration
VITE_APP_NAME=iLead Field Track
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=production

# Feature Flags - Production Settings
VITE_ENABLE_DEBUG_MODE=false
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true

# API Configuration - Production Optimized
VITE_API_TIMEOUT=30000
VITE_MAX_FILE_SIZE=10485760
VITE_MAX_PHOTO_SIZE=5242880

# GPS and Location Settings
VITE_GPS_TIMEOUT=15000
VITE_GPS_MAX_AGE=300000
VITE_GPS_HIGH_ACCURACY=true

# Offline Sync Configuration
VITE_SYNC_INTERVAL=300000
VITE_MAX_OFFLINE_STORAGE=104857600
VITE_RETRY_ATTEMPTS=3
