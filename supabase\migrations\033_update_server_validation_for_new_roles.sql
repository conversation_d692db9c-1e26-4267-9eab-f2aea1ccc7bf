-- Migration: Update server-side validation functions for new user roles
-- Description: Update all server-side validation functions to include the new roles (staff, partner, accountant, social_media_manager)
-- Date: 2025-01-14

-- Update the validate_endpoint_access function to include new roles
CREATE OR REPLACE FUNCTION validate_endpoint_access(
    p_endpoint_name TEXT,
    p_target_user_id UUID DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    current_user_role user_role;
    required_roles user_role[];
    allow_self_access BOOLEAN := false;
    require_ownership BOOLEAN := false;
    is_owner BOOLEAN := false;
    has_required_role BOOLEAN := false;
    result JSONB;
BEGIN
    -- Get current user info
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'User not authenticated',
            'code', 'UNAUTHENTICATED'
        );
    END IF;
    
    -- Get user role
    SELECT role INTO current_user_role
    FROM profiles
    WHERE id = current_user_id;
    
    IF current_user_role IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'User profile not found',
            'code', 'PROFILE_NOT_FOUND'
        );
    END IF;
    
    -- Define endpoint access rules with new roles included
    CASE p_endpoint_name
        -- Dashboard endpoints
        WHEN 'dashboard_metrics' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager']::user_role[];
            
        -- Field reports endpoints
        WHEN 'field_reports' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager']::user_role[];
            allow_self_access := true;
            
        WHEN 'field_reports_create' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager']::user_role[];
            require_ownership := (current_user_role IN ('field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'));
            
        WHEN 'field_reports_update' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager']::user_role[];
            require_ownership := (current_user_role IN ('field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'));
            
        WHEN 'field_reports_delete' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        -- Field staff attendance endpoints
        WHEN 'field_staff_attendance' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager']::user_role[];
            allow_self_access := true;
            
        WHEN 'field_staff_attendance_create' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager']::user_role[];
            require_ownership := (current_user_role IN ('field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'));
            
        -- Tasks endpoints
        WHEN 'tasks' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager']::user_role[];
            allow_self_access := true;
            
        WHEN 'tasks_create' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        WHEN 'tasks_update' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        WHEN 'tasks_delete' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        -- Schools endpoints
        WHEN 'schools' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager']::user_role[];
            
        WHEN 'schools_create' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        WHEN 'schools_update' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        WHEN 'schools_delete' THEN
            required_roles := ARRAY['admin']::user_role[];
            
        -- Books endpoints (accountants get special access)
        WHEN 'books' THEN
            required_roles := ARRAY['admin', 'program_officer', 'accountant']::user_role[];
            
        WHEN 'books_create' THEN
            required_roles := ARRAY['admin', 'program_officer', 'accountant']::user_role[];
            
        WHEN 'books_update' THEN
            required_roles := ARRAY['admin', 'program_officer', 'accountant']::user_role[];
            
        WHEN 'books_delete' THEN
            required_roles := ARRAY['admin', 'program_officer', 'accountant']::user_role[];
            
        -- Book distributions endpoints (accountants get special access)
        WHEN 'book_distributions' THEN
            required_roles := ARRAY['admin', 'program_officer', 'accountant']::user_role[];
            
        WHEN 'book_distributions_create' THEN
            required_roles := ARRAY['admin', 'program_officer', 'accountant']::user_role[];
            
        -- Staff management endpoints
        WHEN 'staff_management' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        WHEN 'staff_create' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        WHEN 'staff_update' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        WHEN 'staff_delete' THEN
            required_roles := ARRAY['admin']::user_role[];
            
        -- Impact and analytics endpoints
        WHEN 'impact_analytics' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        -- Profile endpoints
        WHEN 'profile_view' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager']::user_role[];
            allow_self_access := true;
            
        WHEN 'profile_update' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager']::user_role[];
            require_ownership := (current_user_role IN ('field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'));
            
        ELSE
            RETURN jsonb_build_object(
                'success', false,
                'error', 'Unknown endpoint: ' || p_endpoint_name,
                'code', 'UNKNOWN_ENDPOINT'
            );
    END CASE;
    
    -- Check if user has required role
    has_required_role := current_user_role = ANY(required_roles);
    
    IF NOT has_required_role THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Insufficient permissions',
            'code', 'INSUFFICIENT_PERMISSIONS',
            'details', jsonb_build_object(
                'user_role', current_user_role,
                'required_roles', required_roles
            )
        );
    END IF;
    
    -- Check ownership if required
    IF require_ownership AND p_target_user_id IS NOT NULL THEN
        is_owner := (current_user_id = p_target_user_id);
        
        IF NOT is_owner AND current_user_role NOT IN ('admin', 'program_officer') THEN
            RETURN jsonb_build_object(
                'success', false,
                'error', 'Access denied: ownership required',
                'code', 'OWNERSHIP_REQUIRED'
            );
        END IF;
    END IF;
    
    -- Success
    RETURN jsonb_build_object(
        'success', true,
        'user_role', current_user_role,
        'is_owner', COALESCE(is_owner, false),
        'has_elevated_access', current_user_role IN ('admin', 'program_officer')
    );
END;
$$;

-- Create function to validate user creation permissions
CREATE OR REPLACE FUNCTION validate_user_creation_permissions(
    p_current_user_role user_role,
    p_target_role user_role
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Admins can create any role
    IF p_current_user_role = 'admin' THEN
        RETURN jsonb_build_object('success', true, 'message', 'Admin can create any role');
    END IF;

    -- Program officers can only create field-level roles
    IF p_current_user_role = 'program_officer' THEN
        IF p_target_role IN ('staff', 'field_staff', 'partner', 'accountant', 'social_media_manager') THEN
            RETURN jsonb_build_object('success', true, 'message', 'Program officer can create field-level roles');
        ELSE
            RETURN jsonb_build_object(
                'success', false,
                'error', 'Program officers cannot create admin or program officer accounts',
                'code', 'INSUFFICIENT_PERMISSIONS'
            );
        END IF;
    END IF;

    -- Other roles cannot create users
    RETURN jsonb_build_object(
        'success', false,
        'error', 'Only admins and program officers can create users',
        'code', 'INSUFFICIENT_PERMISSIONS'
    );
END;
$$;

-- Add comment for documentation
COMMENT ON FUNCTION validate_endpoint_access IS 'Updated server-side validation function to include new user roles: staff, partner, accountant, social_media_manager. Accountants have special access to book management endpoints.';
COMMENT ON FUNCTION validate_user_creation_permissions IS 'Validates user creation permissions. Program officers can only create field-level roles (staff, field_staff, partner, accountant, social_media_manager). Admins can create any role.';
