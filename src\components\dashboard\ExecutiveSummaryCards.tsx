import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import {
  Users,
  School,
  GraduationCap,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';
import { DashboardMetrics } from '@/hooks/dashboard/useDashboardMetrics';

interface ExecutiveSummaryCardsProps {
  metrics: DashboardMetrics;
  isLoading?: boolean;
}

interface SummaryCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  icon: React.ElementType;
  trend?: {
    value: number;
    isPositive: boolean;
    label: string;
  };
  color: 'green' | 'blue' | 'purple' | 'orange';
  onClick?: () => void;
}

const SummaryCard: React.FC<SummaryCardProps> = ({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  color,
  onClick
}) => {
  const colorClasses = {
    green: {
      bg: 'bg-green-100',
      icon: 'text-green-600',
      border: 'border-l-green-500',
    },
    blue: {
      bg: 'bg-blue-100',
      icon: 'text-blue-600',
      border: 'border-l-blue-500',
    },
    purple: {
      bg: 'bg-purple-100',
      icon: 'text-purple-600',
      border: 'border-l-purple-500',
    },
    orange: {
      bg: 'bg-orange-100',
      icon: 'text-orange-600',
      border: 'border-l-orange-500',
    },
  };

  const classes = colorClasses[color];

  const TrendIcon = trend?.isPositive ? TrendingUp : trend?.isPositive === false ? TrendingDown : Minus;
  const trendColor = trend?.isPositive ? 'text-green-600' : trend?.isPositive === false ? 'text-red-600' : 'text-gray-500';

  return (
    <Card 
      className={`border-l-4 ${classes.border} cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-105`}
      onClick={onClick}
    >
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-gray-600">{title}</p>
              <div className={`${classes.bg} p-2 rounded-lg`}>
                <Icon className={`h-5 w-5 ${classes.icon}`} />
              </div>
            </div>
            <div className="space-y-1">
              <p className="text-3xl font-bold text-gray-900">{value}</p>
              <p className="text-sm text-gray-500">{subtitle}</p>
              {trend && (
                <div className="flex items-center space-x-1">
                  <TrendIcon className={`h-4 w-4 ${trendColor}`} />
                  <span className={`text-sm font-medium ${trendColor}`}>
                    {Math.abs(trend.value)}%
                  </span>
                  <span className="text-sm text-gray-500">{trend.label}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const LoadingSummaryCard: React.FC = () => (
  <Card className="border-l-4 border-l-gray-300">
    <CardContent className="p-6">
      <div className="flex items-center justify-between">
        <div className="flex-1 space-y-3">
          <div className="flex items-center justify-between">
            <div className="h-4 bg-gray-200 rounded animate-pulse w-24" />
            <div className="h-8 w-8 bg-gray-200 rounded-lg animate-pulse" />
          </div>
          <div className="h-8 bg-gray-200 rounded animate-pulse w-16" />
          <div className="h-4 bg-gray-200 rounded animate-pulse w-32" />
        </div>
      </div>
    </CardContent>
  </Card>
);

export const ExecutiveSummaryCards: React.FC<ExecutiveSummaryCardsProps> = ({
  metrics,
  isLoading
}) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(3)].map((_, i) => (
          <LoadingSummaryCard key={i} />
        ))}
      </div>
    );
  }

  const summaryCards = [
    {
      title: 'Field Staff Activity',
      value: `${Math.round(metrics.fieldStaff.checkInComplianceRate)}%`,
      subtitle: `${metrics.fieldStaff.checkedInToday}/${metrics.fieldStaff.totalStaff} Active Today`,
      icon: Users,
      color: 'green' as const,
      trend: {
        value: 12, // Could be calculated from actual check-in trends
        isPositive: true,
        label: 'vs last week'
      },
    },
    {
      title: 'Schools Reached',
      value: `${metrics.programReach.schoolsCovered}/${metrics.programReach.totalSchools}`,
      subtitle: `${Math.round((metrics.programReach.schoolsCovered / Math.max(metrics.programReach.totalSchools, 1)) * 100)}% Coverage (Check-ins)`,
      icon: School,
      color: 'blue' as const,
      trend: {
        value: Math.round(Math.abs(metrics.programReach.monthlySchoolsComparison)),
        isPositive: metrics.programReach.monthlySchoolsComparison >= 0,
        label: 'vs last month'
      },
    },
    {
      title: 'Students Impacted',
      value: metrics.programReach.totalStudentsReached.toLocaleString(),
      subtitle: `${metrics.programReach.maleStudents.toLocaleString()} Male, ${metrics.programReach.femaleStudents.toLocaleString()} Female`,
      icon: GraduationCap,
      color: 'purple' as const,
      trend: {
        value: Math.round(Math.abs(metrics.programReach.weeklyStudentsComparison)),
        isPositive: metrics.programReach.weeklyStudentsComparison >= 0,
        label: 'vs last week'
      },
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {summaryCards.map((card, index) => (
        <SummaryCard
          key={index}
          {...card}
        />
      ))}
    </div>
  );
};
