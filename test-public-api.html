<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Public API</title>
</head>
<body>
    <h1>Testing Public API Functions</h1>
    <div id="results"></div>

    <script>
        const SUPABASE_URL = 'https://bygrspebofyofymivmib.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ5Z3JzcGVib2Z5b2Z5bWl2bWliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwMzIxODgsImV4cCI6MjA2NDYwODE4OH0.xxfeix-6F42NmVWaQHE19nnDCxZmiMDs1_fyLb0-lgE';

        async function testFunction(functionName, params = {}) {
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/${functionName}`, {
                    method: 'POST',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(params)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                return { success: true, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function runTests() {
            const resultsDiv = document.getElementById('results');
            
            const tests = [
                { name: 'get_public_impact_summary', params: {} },
                { name: 'get_public_trends', params: { period: 'weekly', periods: 12 } },
                { name: 'get_public_books_breakdown', params: {} },
                { name: 'get_public_coverage_summary', params: {} }
            ];

            for (const test of tests) {
                const result = await testFunction(test.name, test.params);
                const div = document.createElement('div');
                div.style.marginBottom = '20px';
                div.style.padding = '10px';
                div.style.border = '1px solid #ccc';
                div.style.borderRadius = '5px';
                
                if (result.success) {
                    div.style.backgroundColor = '#d4edda';
                    div.innerHTML = `
                        <h3 style="color: green;">✅ ${test.name}</h3>
                        <pre style="background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto;">${JSON.stringify(result.data, null, 2)}</pre>
                    `;
                } else {
                    div.style.backgroundColor = '#f8d7da';
                    div.innerHTML = `
                        <h3 style="color: red;">❌ ${test.name}</h3>
                        <p style="color: red;">Error: ${result.error}</p>
                    `;
                }
                
                resultsDiv.appendChild(div);
            }
        }

        // Run tests when page loads
        runTests();
    </script>
</body>
</html>
