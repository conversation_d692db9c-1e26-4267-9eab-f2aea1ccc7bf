/**
 * Real-time Notifications Hook
 * Replaces polling-based notifications with WebSocket subscriptions
 */

import { useState, useEffect, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/useAuth';
import { realtimeSubscriptionManager } from '@/services/realtimeSubscriptionManager';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// ============================================================================
// TYPES
// ============================================================================

export interface Notification {
  id: string;
  recipient_id: string;
  sender_id?: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  is_read: boolean;
  metadata?: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

export interface NotificationStats {
  total: number;
  unread: number;
  byType: Record<string, number>;
  recentCount: number;
}

// ============================================================================
// REAL-TIME NOTIFICATIONS HOOK
// ============================================================================

export const useRealtimeNotifications = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [subscriptionId, setSubscriptionId] = useState<string | null>(null);
  const [connectionStats, setConnectionStats] = useState({
    isConnected: false,
    lastUpdate: 0,
    errorCount: 0,
  });

  // ============================================================================
  // NOTIFICATIONS QUERY (INITIAL LOAD)
  // ============================================================================

  const {
    data: notifications,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['notifications', user?.id],
    queryFn: async (): Promise<Notification[]> => {
      if (!user) return [];

      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('recipient_id', user.id)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) {
        console.error('Error fetching notifications:', error);
        throw error;
      }

      return data || [];
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes (much longer since we have real-time updates)
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // ============================================================================
  // UNREAD COUNT QUERY
  // ============================================================================

  const {
    data: unreadCount,
    refetch: refetchUnreadCount,
  } = useQuery({
    queryKey: ['unread-notification-count', user?.id],
    queryFn: async (): Promise<number> => {
      if (!user) return 0;

      const { data, error } = await supabase
        .rpc('get_unread_notification_count', { user_id: user.id });

      if (error) {
        console.error('Error fetching unread count:', error);
        return 0;
      }

      return data || 0;
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // ============================================================================
  // REAL-TIME SUBSCRIPTION HANDLERS
  // ============================================================================

  const handleNewNotification = useCallback((payload: Record<string, unknown>) => {
    console.log('📧 New notification received:', payload);
    
    const newNotification = payload.new as Notification;
    
    // Update notifications cache
    queryClient.setQueryData(['notifications', user?.id], (oldData: Notification[] = []) => {
      return [newNotification, ...oldData].slice(0, 50); // Keep only latest 50
    });
    
    // Update unread count
    queryClient.setQueryData(['unread-notification-count', user?.id], (oldCount: number = 0) => {
      return oldCount + 1;
    });
    
    // Show toast notification for important types
    if (newNotification.type === 'error' || newNotification.type === 'warning') {
      toast[newNotification.type](newNotification.title, {
        description: newNotification.message,
        duration: 5000,
      });
    } else if (newNotification.type === 'success') {
      toast.success(newNotification.title, {
        description: newNotification.message,
        duration: 3000,
      });
    }
    
    // Update connection stats
    setConnectionStats(prev => ({
      ...prev,
      lastUpdate: Date.now(),
    }));
    
  }, [user?.id, queryClient]);

  const handleNotificationUpdate = useCallback((payload: Record<string, unknown>) => {
    console.log('📧 Notification updated:', payload);
    
    const updatedNotification = payload.new as Notification;
    
    // Update notifications cache
    queryClient.setQueryData(['notifications', user?.id], (oldData: Notification[] = []) => {
      return oldData.map(notification => 
        notification.id === updatedNotification.id ? updatedNotification : notification
      );
    });
    
    // If notification was marked as read, update unread count
    if (updatedNotification.is_read && !payload.old?.is_read) {
      queryClient.setQueryData(['unread-notification-count', user?.id], (oldCount: number = 0) => {
        return Math.max(0, oldCount - 1);
      });
    }
    
    setConnectionStats(prev => ({
      ...prev,
      lastUpdate: Date.now(),
    }));
    
  }, [user?.id, queryClient]);

  const handleSubscriptionError = useCallback((error: unknown) => {
    console.error('📧 Notification subscription error:', error);
    
    setConnectionStats(prev => ({
      ...prev,
      errorCount: prev.errorCount + 1,
    }));
    
    // Fallback to polling if real-time fails
    toast.warning('Real-time notifications temporarily unavailable');
  }, []);

  // ============================================================================
  // SUBSCRIPTION MANAGEMENT
  // ============================================================================

  const setupRealtimeSubscription = useCallback(() => {
    if (!user || isSubscribed) return;

    try {
      console.log('📧 Setting up real-time notification subscription for user:', user.id);
      
      // Subscribe to new notifications
      const newNotificationId = realtimeSubscriptionManager.subscribe(
        {
          table: 'notifications',
          filter: `recipient_id=eq.${user.id}`,
          event: 'INSERT',
          priority: 'high',
        },
        handleNewNotification
      );
      
      // Subscribe to notification updates (e.g., marking as read)
      const updateNotificationId = realtimeSubscriptionManager.subscribe(
        {
          table: 'notifications',
          filter: `recipient_id=eq.${user.id}`,
          event: 'UPDATE',
          priority: 'high',
        },
        handleNotificationUpdate
      );
      
      setSubscriptionId(`${newNotificationId},${updateNotificationId}`);
      setIsSubscribed(true);
      setConnectionStats(prev => ({
        ...prev,
        isConnected: true,
        lastUpdate: Date.now(),
      }));
      
      console.log('📧 Real-time notification subscription established');
      
    } catch (error) {
      console.error('Failed to setup real-time notification subscription:', error);
      handleSubscriptionError(error);
    }
  }, [user, isSubscribed, handleNewNotification, handleNotificationUpdate, handleSubscriptionError]);

  const cleanupSubscription = useCallback(() => {
    if (subscriptionId) {
      const ids = subscriptionId.split(',');
      ids.forEach(id => {
        realtimeSubscriptionManager.unsubscribe(id);
      });
      
      setSubscriptionId(null);
      setIsSubscribed(false);
      setConnectionStats(prev => ({
        ...prev,
        isConnected: false,
      }));
      
      console.log('📧 Real-time notification subscription cleaned up');
    }
  }, [subscriptionId]);

  // ============================================================================
  // NOTIFICATION ACTIONS
  // ============================================================================

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true, updated_at: new Date().toISOString() })
        .eq('id', notificationId);

      if (error) {
        console.error('Error marking notification as read:', error);
        throw error;
      }

      // The real-time subscription will handle the cache update
      
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      toast.error('Failed to mark notification as read');
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true, updated_at: new Date().toISOString() })
        .eq('recipient_id', user.id)
        .eq('is_read', false);

      if (error) {
        console.error('Error marking all notifications as read:', error);
        throw error;
      }

      // Update local cache immediately for better UX
      queryClient.setQueryData(['notifications', user.id], (oldData: Notification[] = []) => {
        return oldData.map(notification => ({ ...notification, is_read: true }));
      });

      queryClient.setQueryData(['unread-notification-count', user.id], 0);

      toast.success('All notifications marked as read');
      
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      toast.error('Failed to mark all notifications as read');
    }
  }, [user, queryClient]);

  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId);

      if (error) {
        console.error('Error deleting notification:', error);
        throw error;
      }

      // Update local cache immediately
      queryClient.setQueryData(['notifications', user?.id], (oldData: Notification[] = []) => {
        return oldData.filter(notification => notification.id !== notificationId);
      });

      toast.success('Notification deleted');
      
    } catch (error) {
      console.error('Failed to delete notification:', error);
      toast.error('Failed to delete notification');
    }
  }, [user?.id, queryClient]);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    if (user && !isSubscribed) {
      setupRealtimeSubscription();
    }

    return () => {
      cleanupSubscription();
    };
  }, [user, setupRealtimeSubscription, cleanupSubscription, isSubscribed]);

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  const notificationStats: NotificationStats = {
    total: notifications?.length || 0,
    unread: unreadCount || 0,
    byType: notifications?.reduce((acc, notification) => {
      acc[notification.type] = (acc[notification.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {},
    recentCount: notifications?.filter(n => 
      Date.now() - new Date(n.created_at).getTime() < 24 * 60 * 60 * 1000
    ).length || 0,
  };

  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================

  return {
    // Data
    notifications: notifications || [],
    unreadCount: unreadCount || 0,
    notificationStats,
    
    // Loading states
    isLoading,
    error,
    
    // Real-time status
    isSubscribed,
    connectionStats,
    
    // Actions
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refetch,
    refetchUnreadCount,
    
    // Subscription management
    setupRealtimeSubscription,
    cleanupSubscription,
  };
};
