-- <PERSON>reate function to start an attendance session
CREATE OR REPLACE FUNCTION start_attendance_session(
  session_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  session_record attendance_sessions%ROWTYPE;
  result JSON;
BEGIN
  -- Check if session exists and is in scheduled status
  SELECT * INTO session_record
  FROM attendance_sessions
  WHERE id = session_id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Session not found';
  END IF;

  IF session_record.session_status != 'scheduled' THEN
    RAISE EXCEPTION 'Session cannot be started. Current status: %', session_record.session_status;
  END IF;

  -- Update session to started status with actual start time
  UPDATE attendance_sessions
  SET 
    session_status = 'in_progress',
    actual_start_time = NOW(),
    updated_at = NOW()
  WHERE id = session_id;

  -- Get updated session data
  SELECT row_to_json(s.*) INTO result
  FROM attendance_sessions s
  WHERE s.id = session_id;

  RETURN result;
END;
$$;

-- <PERSON>reate function to end an attendance session
CREATE OR REPLACE FUNCTION end_attendance_session(
  session_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  session_record attendance_sessions%ROWTYPE;
  result JSON;
BEGIN
  -- Check if session exists and is in progress
  SELECT * INTO session_record
  FROM attendance_sessions
  WHERE id = session_id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Session not found';
  END IF;

  IF session_record.session_status != 'in_progress' THEN
    RAISE EXCEPTION 'Session cannot be ended. Current status: %', session_record.session_status;
  END IF;

  -- Update session to completed status with actual end time
  UPDATE attendance_sessions
  SET 
    session_status = 'completed',
    actual_end_time = NOW(),
    updated_at = NOW()
  WHERE id = session_id;

  -- Get updated session data
  SELECT row_to_json(s.*) INTO result
  FROM attendance_sessions s
  WHERE s.id = session_id;

  RETURN result;
END;
$$;

-- Create function to cancel an attendance session
CREATE OR REPLACE FUNCTION cancel_attendance_session(
  session_id UUID,
  cancellation_reason TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  session_record attendance_sessions%ROWTYPE;
  result JSON;
BEGIN
  -- Check if session exists
  SELECT * INTO session_record
  FROM attendance_sessions
  WHERE id = session_id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Session not found';
  END IF;

  IF session_record.session_status IN ('completed', 'cancelled') THEN
    RAISE EXCEPTION 'Session cannot be cancelled. Current status: %', session_record.session_status;
  END IF;

  -- Update session to cancelled status
  UPDATE attendance_sessions
  SET 
    session_status = 'cancelled',
    notes = CASE 
      WHEN cancellation_reason IS NOT NULL THEN 
        COALESCE(notes, '') || CASE WHEN notes IS NOT NULL AND notes != '' THEN E'\n' ELSE '' END || 'Cancelled: ' || cancellation_reason
      ELSE notes
    END,
    updated_at = NOW()
  WHERE id = session_id;

  -- Get updated session data
  SELECT row_to_json(s.*) INTO result
  FROM attendance_sessions s
  WHERE s.id = session_id;

  RETURN result;
END;
$$;

-- Create function to get session status with attendance summary
CREATE OR REPLACE FUNCTION get_session_status(
  session_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  session_record attendance_sessions%ROWTYPE;
  attendance_summary JSON;
  result JSON;
BEGIN
  -- Get session data
  SELECT * INTO session_record
  FROM attendance_sessions
  WHERE id = session_id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Session not found';
  END IF;

  -- Get attendance summary
  SELECT json_build_object(
    'total_students', COUNT(*),
    'present_count', COUNT(*) FILTER (WHERE status = 'present'),
    'absent_count', COUNT(*) FILTER (WHERE status = 'absent'),
    'late_count', COUNT(*) FILTER (WHERE status = 'late'),
    'excused_count', COUNT(*) FILTER (WHERE status = 'excused'),
    'partial_count', COUNT(*) FILTER (WHERE status = 'partial')
  ) INTO attendance_summary
  FROM student_attendance
  WHERE session_id = session_id;

  -- Build result
  SELECT json_build_object(
    'session', row_to_json(session_record),
    'attendance_summary', attendance_summary
  ) INTO result;

  RETURN result;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION start_attendance_session(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION end_attendance_session(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION cancel_attendance_session(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_session_status(UUID) TO authenticated;

-- Add RLS policies for the functions (they use SECURITY DEFINER so they run with elevated privileges)
-- The functions themselves should check user permissions if needed

-- Add comments for documentation
COMMENT ON FUNCTION start_attendance_session(UUID) IS 'Starts an attendance session by updating status to in_progress and setting actual_start_time';
COMMENT ON FUNCTION end_attendance_session(UUID) IS 'Ends an attendance session by updating status to completed and setting actual_end_time';
COMMENT ON FUNCTION cancel_attendance_session(UUID, TEXT) IS 'Cancels an attendance session with optional reason';
COMMENT ON FUNCTION get_session_status(UUID) IS 'Gets session details with attendance summary statistics';
