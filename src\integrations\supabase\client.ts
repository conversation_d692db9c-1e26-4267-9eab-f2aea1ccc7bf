// Supabase Client Configuration
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';
import { config } from '@/config/environment';

// Create optimized client with connection pooling
const createOptimizedSupabaseClient = () => {
  return createClient<Database>(config.supabaseUrl, config.supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
    db: {
      schema: 'public',
    },
    global: {
      headers: {
        'x-client-info': config.appName.toLowerCase().replace(/\s+/g, '-'),
      },
    },
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
  });
};

// Primary client instance
export const supabase = createOptimizedSupabaseClient();

// SECURITY: Admin operations moved to secure Edge Functions
// Service role key is no longer exposed in frontend code
// Use userApi.createUser() and userApi.bulkCreateUsers() instead
export const supabaseAdmin = null;

// Optimized client getters for different operations
// For now, all operations use the same client
// In production with connection pooling, these could return different clients
export const getReadClient = () => supabase;
export const getWriteClient = () => supabase;
export const getRealtimeClient = () => supabase;
export const getAdminClient = () => {
  console.warn('Admin client is deprecated. Use userApi for user management operations.');
  return null;
};