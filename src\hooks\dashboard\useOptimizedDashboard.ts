/**
 * Optimized Dashboard Hook with Advanced Caching
 * Replaces multiple individual queries with single RPC calls
 */

import { useQuery, useQueries, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useMemo } from 'react';

// ============================================================================
// TYPES
// ============================================================================

export interface OptimizedDashboardMetrics {
  fieldStaff: {
    totalStaff: number;
    activeStaff: number;
    checkedInToday: number;
    averageHoursPerDay: number;
    checkInComplianceRate: number;
    reportSubmissionRate: number;
  };
  programReach: {
    totalStudentsReached: number;
    maleStudents: number;
    femaleStudents: number;
    schoolsCovered: number;
    totalSchools: number;
    sessionCompletionRate: number;
    averageAttendancePerSession: number;
    bookDistributionRate: number;
    monthlySchoolsComparison: number;
    studentEngagementPercentage: number;
    totalStudentsInSchools: number;
  };
  operational: {
    taskCompletionRate: number;
    averageTaskCompletionTime: number;
    reportQualityScore: number;
    resourceUtilization: number;
    offlineSyncSuccessRate: number;
  };
  quality: {
    sessionAttendanceTrend: number;
    studentEngagementScore: number;
    followUpCompletionRate: number;
    challengeResolutionTime: number;
    feedbackSentiment: number;
  };
  lastUpdated: number;
}

export interface ActivitySummary {
  todayActivities: {
    checkIns: number;
    reports: number;
    studentsReached: number;
    hoursWorked: number;
  };
  weeklyTrend: Array<{
    date: string;
    activities: number;
    students: number;
    hours: number;
  }>;
  criticalAlerts: Array<{
    id: string;
    type: 'info' | 'warning' | 'error';
    message: string;
    timestamp: string;
    priority: 'low' | 'medium' | 'high';
  }>;
}

export interface StaffPerformanceData {
  id: string;
  name: string;
  role: string;
  isCheckedIn: boolean;
  currentSchool?: string;
  todayHours: number;
  weeklyHours: number;
  studentsReached: number;
  schoolsVisited: number;
  reportsSubmitted: number;
  performanceScore: number;
  lastActivity: string;
}

// ============================================================================
// QUERY KEYS
// ============================================================================

export const optimizedDashboardKeys = {
  all: ['optimized-dashboard'] as const,
  metrics: (userId: string, role: string) => [...optimizedDashboardKeys.all, 'metrics', userId, role] as const,
  activity: (userId: string, days: number) => [...optimizedDashboardKeys.all, 'activity', userId, days] as const,
  staff: (limit: number, offset: number) => [...optimizedDashboardKeys.all, 'staff', limit, offset] as const,
  distributions: () => [...optimizedDashboardKeys.all, 'distributions'] as const,
  performance: () => [...optimizedDashboardKeys.all, 'performance'] as const,
} as const;

// ============================================================================
// CACHING CONFIGURATION
// ============================================================================

const CACHE_CONFIG = {
  // Dashboard metrics - refresh every 2 minutes
  metrics: {
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchInterval: 5 * 60 * 1000, // 5 minutes
  },
  // Activity data - refresh every minute
  activity: {
    staleTime: 1 * 60 * 1000, // 1 minute
    cacheTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 2 * 60 * 1000, // 2 minutes
  },
  // Staff performance - refresh every 5 minutes
  staff: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 15 * 60 * 1000, // 15 minutes
    refetchInterval: 10 * 60 * 1000, // 10 minutes
  },
  // Distribution stats - refresh every 10 minutes
  distributions: {
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    refetchInterval: 15 * 60 * 1000, // 15 minutes
  },
} as const;

// ============================================================================
// OPTIMIZED DASHBOARD METRICS HOOK
// ============================================================================

export const useOptimizedDashboardMetrics = () => {
  const { user, profile } = useAuth();

  return useQuery({
    queryKey: optimizedDashboardKeys.metrics(user?.id || '', profile?.role || ''),
    queryFn: async (): Promise<OptimizedDashboardMetrics> => {
      console.log('🚀 Fetching optimized dashboard metrics...');
      
      const { data, error } = await supabase.rpc('get_dashboard_metrics_optimized', {
        p_user_id: user?.id || null,
        p_user_role: profile?.role || null,
      });

      if (error) {
        console.error('❌ Error fetching optimized dashboard metrics:', error);
        throw error;
      }

      console.log('✅ Optimized dashboard metrics loaded successfully');
      return data as OptimizedDashboardMetrics;
    },
    enabled: !!user && !!profile?.role,
    ...CACHE_CONFIG.metrics,
    // Enable background refetch
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    // Retry configuration
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// ============================================================================
// ACTIVITY SUMMARY HOOK
// ============================================================================

export const useOptimizedActivitySummary = (daysBack: number = 7) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: optimizedDashboardKeys.activity(user?.id || '', daysBack),
    queryFn: async (): Promise<ActivitySummary> => {
      console.log('🚀 Fetching optimized activity summary...');
      
      const { data, error } = await supabase.rpc('get_dashboard_activity_summary', {
        p_user_id: user?.id || null,
        p_days_back: daysBack,
      });

      if (error) {
        console.error('❌ Error fetching activity summary:', error);
        throw error;
      }

      console.log('✅ Activity summary loaded successfully');
      return data as ActivitySummary;
    },
    enabled: !!user,
    ...CACHE_CONFIG.activity,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    retry: 2,
  });
};

// ============================================================================
// STAFF PERFORMANCE HOOK
// ============================================================================

export const useOptimizedStaffPerformance = (limit: number = 10, offset: number = 0) => {
  return useQuery({
    queryKey: optimizedDashboardKeys.staff(limit, offset),
    queryFn: async (): Promise<StaffPerformanceData[]> => {
      console.log('🚀 Fetching optimized staff performance...');
      
      const { data, error } = await supabase.rpc('get_staff_performance_metrics', {
        p_limit: limit,
        p_offset: offset,
      });

      if (error) {
        console.error('❌ Error fetching staff performance:', error);
        throw error;
      }

      console.log('✅ Staff performance loaded successfully');
      return (data as StaffPerformanceData[]) || [];
    },
    ...CACHE_CONFIG.staff,
    refetchOnWindowFocus: false, // Less frequent updates for staff data
    retry: 2,
  });
};

// ============================================================================
// DISTRIBUTION STATISTICS HOOK
// ============================================================================

export const useOptimizedDistributionStats = () => {
  return useQuery({
    queryKey: optimizedDashboardKeys.distributions(),
    queryFn: async () => {
      console.log('🚀 Fetching optimized distribution statistics...');
      
      const { data, error } = await supabase.rpc('get_distribution_statistics');

      if (error) {
        console.error('❌ Error fetching distribution statistics:', error);
        throw error;
      }

      console.log('✅ Distribution statistics loaded successfully');
      return data;
    },
    ...CACHE_CONFIG.distributions,
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// ============================================================================
// COMBINED DASHBOARD HOOK
// ============================================================================

export const useOptimizedDashboard = () => {
  const { user, profile } = useAuth();
  const queryClient = useQueryClient();

  // Use parallel queries for better performance
  const queries = useQueries({
    queries: [
      {
        queryKey: optimizedDashboardKeys.metrics(user?.id || '', profile?.role || ''),
        queryFn: async () => {
          const { data, error } = await supabase.rpc('get_dashboard_metrics_optimized', {
            p_user_id: user?.id || null,
            p_user_role: profile?.role || null,
          });
          if (error) throw error;
          return data as OptimizedDashboardMetrics;
        },
        enabled: !!user && !!profile?.role,
        ...CACHE_CONFIG.metrics,
      },
      {
        queryKey: optimizedDashboardKeys.activity(user?.id || '', 7),
        queryFn: async () => {
          const { data, error } = await supabase.rpc('get_dashboard_activity_summary', {
            p_user_id: user?.id || null,
            p_days_back: 7,
          });
          if (error) throw error;
          return data as ActivitySummary;
        },
        enabled: !!user,
        ...CACHE_CONFIG.activity,
      },
      {
        queryKey: optimizedDashboardKeys.distributions(),
        queryFn: async () => {
          const { data, error } = await supabase.rpc('get_distribution_statistics');
          if (error) throw error;
          return data;
        },
        ...CACHE_CONFIG.distributions,
      },
    ],
  });

  // Memoized results
  const result = useMemo(() => {
    const [metricsQuery, activityQuery, distributionsQuery] = queries;
    
    return {
      metrics: {
        data: metricsQuery.data,
        isLoading: metricsQuery.isLoading,
        error: metricsQuery.error,
        isSuccess: metricsQuery.isSuccess,
      },
      activity: {
        data: activityQuery.data,
        isLoading: activityQuery.isLoading,
        error: activityQuery.error,
        isSuccess: activityQuery.isSuccess,
      },
      distributions: {
        data: distributionsQuery.data,
        isLoading: distributionsQuery.isLoading,
        error: distributionsQuery.error,
        isSuccess: distributionsQuery.isSuccess,
      },
      // Overall loading state
      isLoading: queries.some(q => q.isLoading),
      isSuccess: queries.every(q => q.isSuccess),
      hasError: queries.some(q => q.error),
      // Refresh function
      refresh: () => {
        queries.forEach(q => queryClient.invalidateQueries({ queryKey: q.queryKey }));
      },
    };
  }, [queries, queryClient]);

  return result;
};

// ============================================================================
// CACHE MANAGEMENT UTILITIES
// ============================================================================

export const useDashboardCacheManager = () => {
  const queryClient = useQueryClient();

  return {
    // Invalidate all dashboard data
    invalidateAll: () => {
      queryClient.invalidateQueries({ queryKey: optimizedDashboardKeys.all });
    },
    
    // Invalidate specific data types
    invalidateMetrics: () => {
      queryClient.invalidateQueries({ 
        queryKey: optimizedDashboardKeys.all,
        predicate: (query) => query.queryKey.includes('metrics')
      });
    },
    
    // Prefetch dashboard data
    prefetchDashboard: async (userId: string, role: string) => {
      await Promise.all([
        queryClient.prefetchQuery({
          queryKey: optimizedDashboardKeys.metrics(userId, role),
          queryFn: async () => {
            const { data, error } = await supabase.rpc('get_dashboard_metrics_optimized', {
              p_user_id: userId,
              p_user_role: role,
            });
            if (error) throw error;
            return data;
          },
          staleTime: CACHE_CONFIG.metrics.staleTime,
        }),
        queryClient.prefetchQuery({
          queryKey: optimizedDashboardKeys.activity(userId, 7),
          queryFn: async () => {
            const { data, error } = await supabase.rpc('get_dashboard_activity_summary', {
              p_user_id: userId,
              p_days_back: 7,
            });
            if (error) throw error;
            return data;
          },
          staleTime: CACHE_CONFIG.activity.staleTime,
        }),
      ]);
    },
    
    // Get cache statistics
    getCacheStats: () => {
      const cache = queryClient.getQueryCache();
      const dashboardQueries = cache.findAll({ queryKey: optimizedDashboardKeys.all });
      
      return {
        totalQueries: dashboardQueries.length,
        staleQueries: dashboardQueries.filter(q => q.isStale()).length,
        loadingQueries: dashboardQueries.filter(q => q.state.fetchStatus === 'fetching').length,
        errorQueries: dashboardQueries.filter(q => q.state.status === 'error').length,
      };
    },
  };
};
