/**
 * Photo Gallery Test Component
 * Test component to verify photo gallery functionality
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  TestTube, 
  Plus, 
  Trash2, 
  Eye,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import PhotoGallery from './PhotoGallery';
import { useSimplePhotoValidation } from '@/hooks/usePhotoValidation';

// ============================================================================
// TEST COMPONENT
// ============================================================================

export const PhotoGalleryTest: React.FC = () => {
  const [testPhotos, setTestPhotos] = useState<string[]>([
    // Valid test images (these should work)
    'https://images.unsplash.com/photo-1546410531-bb4caa6b424d?w=400',
    'https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=400',
    'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=400',
    
    // Invalid test images (these should fail)
    'https://invalid-url-that-does-not-exist.com/photo1.jpg',
    'https://httpstat.us/404.jpg',
    'https://httpstat.us/500.jpg',
  ]);
  
  const [newPhotoUrl, setNewPhotoUrl] = useState('');
  const [testMode, setTestMode] = useState<'mixed' | 'valid' | 'invalid'>('mixed');

  // Validate photos
  const {
    validPhotos,
    brokenPhotos,
    isLoading,
    hasValidPhotos,
    hasBrokenPhotos,
    validationRate,
  } = useSimplePhotoValidation(testPhotos);

  // Test photo sets
  const validTestPhotos = [
    'https://images.unsplash.com/photo-1546410531-bb4caa6b424d?w=400',
    'https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=400',
    'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=400',
    'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400',
    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
  ];

  const invalidTestPhotos = [
    'https://invalid-url-that-does-not-exist.com/photo1.jpg',
    'https://httpstat.us/404.jpg',
    'https://httpstat.us/500.jpg',
    'https://broken-image-url.com/missing.png',
  ];

  const mixedTestPhotos = [
    ...validTestPhotos.slice(0, 3),
    ...invalidTestPhotos.slice(0, 2),
  ];

  // Handle test mode changes
  const handleTestModeChange = (mode: 'mixed' | 'valid' | 'invalid') => {
    setTestMode(mode);
    switch (mode) {
      case 'valid':
        setTestPhotos(validTestPhotos);
        break;
      case 'invalid':
        setTestPhotos(invalidTestPhotos);
        break;
      case 'mixed':
        setTestPhotos(mixedTestPhotos);
        break;
    }
  };

  // Add new photo URL
  const addPhotoUrl = () => {
    if (newPhotoUrl.trim() && !testPhotos.includes(newPhotoUrl.trim())) {
      setTestPhotos(prev => [...prev, newPhotoUrl.trim()]);
      setNewPhotoUrl('');
    }
  };

  // Remove photo URL
  const removePhotoUrl = (index: number) => {
    setTestPhotos(prev => prev.filter((_, i) => i !== index));
  };

  // Clear all photos
  const clearAllPhotos = () => {
    setTestPhotos([]);
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Photo Gallery Test Component
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Test Controls */}
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium">Test Mode</Label>
              <div className="flex gap-2 mt-2">
                <Button
                  variant={testMode === 'mixed' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleTestModeChange('mixed')}
                >
                  Mixed Photos
                </Button>
                <Button
                  variant={testMode === 'valid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleTestModeChange('valid')}
                >
                  Valid Only
                </Button>
                <Button
                  variant={testMode === 'invalid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleTestModeChange('invalid')}
                >
                  Invalid Only
                </Button>
              </div>
            </div>

            {/* Add Custom Photo URL */}
            <div className="flex gap-2">
              <div className="flex-1">
                <Label htmlFor="photo-url" className="text-sm font-medium">
                  Add Photo URL
                </Label>
                <Input
                  id="photo-url"
                  value={newPhotoUrl}
                  onChange={(e) => setNewPhotoUrl(e.target.value)}
                  placeholder="https://example.com/photo.jpg"
                  className="mt-1"
                />
              </div>
              <div className="flex items-end">
                <Button
                  onClick={addPhotoUrl}
                  disabled={!newPhotoUrl.trim()}
                  size="sm"
                  className="flex items-center gap-1"
                >
                  <Plus className="h-4 w-4" />
                  Add
                </Button>
              </div>
            </div>

            {/* Clear All */}
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Badge variant="outline">
                  {testPhotos.length} photo{testPhotos.length !== 1 ? 's' : ''}
                </Badge>
                {!isLoading && (
                  <>
                    {hasValidPhotos && (
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        {validPhotos.length} valid
                      </Badge>
                    )}
                    {hasBrokenPhotos && (
                      <Badge variant="destructive">
                        {brokenPhotos.length} broken
                      </Badge>
                    )}
                  </>
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllPhotos}
                disabled={testPhotos.length === 0}
                className="flex items-center gap-1"
              >
                <Trash2 className="h-4 w-4" />
                Clear All
              </Button>
            </div>
          </div>

          {/* Validation Status */}
          {!isLoading && testPhotos.length > 0 && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Validation Results
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Valid: {validPhotos.length}</span>
                </div>
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <span>Broken: {brokenPhotos.length}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">Success Rate: {Math.round(validationRate)}%</span>
                </div>
              </div>
            </div>
          )}

          {/* Photo URLs List */}
          {testPhotos.length > 0 && (
            <div>
              <Label className="text-sm font-medium">Current Photo URLs</Label>
              <div className="mt-2 space-y-2 max-h-40 overflow-y-auto">
                {testPhotos.map((url, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded text-sm">
                    <span className="flex-1 truncate">{url}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removePhotoUrl(index)}
                      className="h-6 w-6 p-0"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Photo Gallery Component */}
      {testPhotos.length > 0 ? (
        <PhotoGallery
          photos={testPhotos}
          title="Test Photo Gallery"
          description="Testing photo gallery with validation and modal viewing"
          maxDisplayPhotos={6}
          showValidationStatus={true}
          allowDownload={true}
          gridCols={3}
          aspectRatio="square"
        />
      ) : (
        <Card>
          <CardContent className="p-8 text-center">
            <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No photos to display</p>
            <p className="text-sm text-gray-400 mt-1">
              Select a test mode or add custom photo URLs to see the gallery
            </p>
          </CardContent>
        </Card>
      )}

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">How to Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm">
          <div>
            <strong>Test Modes:</strong>
            <ul className="list-disc list-inside mt-1 space-y-1 text-gray-600">
              <li><strong>Mixed Photos:</strong> Combination of valid and invalid URLs to test validation</li>
              <li><strong>Valid Only:</strong> All photos should load successfully</li>
              <li><strong>Invalid Only:</strong> All photos should show as broken/unavailable</li>
            </ul>
          </div>
          <div>
            <strong>Features to Test:</strong>
            <ul className="list-disc list-inside mt-1 space-y-1 text-gray-600">
              <li>Click on photos to open full-size modal viewer</li>
              <li>Use keyboard navigation (arrow keys, ESC, +/-, R for rotate)</li>
              <li>Download individual photos or all valid photos</li>
              <li>Observe validation status indicators and broken photo handling</li>
              <li>Test with custom photo URLs</li>
            </ul>
          </div>
          <div>
            <strong>Expected Behavior:</strong>
            <ul className="list-disc list-inside mt-1 space-y-1 text-gray-600">
              <li>Valid photos should display with green status indicators</li>
              <li>Invalid photos should show error state with red indicators</li>
              <li>Photo modal should support zoom, rotation, and navigation</li>
              <li>Download functionality should work for accessible photos</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PhotoGalleryTest;
