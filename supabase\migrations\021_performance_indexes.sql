-- Performance Optimization Indexes Migration
-- Migration 021: Add critical indexes for dashboard performance

-- ============================================================================
-- DASHBOARD QUERY PERFORMANCE INDEXES
-- ============================================================================

-- Field Staff Attendance Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_field_staff_attendance_date_staff 
ON field_staff_attendance(attendance_date, staff_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_field_staff_attendance_date_status 
ON field_staff_attendance(attendance_date, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_field_staff_attendance_created_at 
ON field_staff_attendance(created_at DESC);

-- Field Reports Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_field_reports_date_staff 
ON field_reports(report_date, staff_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_field_reports_school_date 
ON field_reports(school_id, report_date);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_field_reports_total_students 
ON field_reports(total_students) WHERE total_students > 0;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_field_reports_participants 
ON field_reports(male_participants, female_participants);

-- Tasks Performance Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_assigned_status 
ON tasks(assigned_to, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_created_status 
ON tasks(created_by, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_due_date_status 
ON tasks(due_date, status) WHERE due_date IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_school_status 
ON tasks(school_id, status) WHERE school_id IS NOT NULL;

-- Schools Performance Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_schools_status_type 
ON schools(registration_status, school_type);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_schools_student_count 
ON schools(student_count) WHERE student_count > 0;

-- Book Distribution Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_book_distributions_date_status 
ON book_distributions(delivery_date, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_book_distributions_school_date 
ON book_distributions(school_id, delivery_date);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_book_distributions_supervisor 
ON book_distributions(supervisor_id, delivery_date);

-- Book Inventory Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_book_inventory_available 
ON book_inventory(available_quantity) WHERE available_quantity > 0;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_book_inventory_threshold 
ON book_inventory(available_quantity, minimum_threshold) 
WHERE available_quantity <= minimum_threshold;

-- Profiles Performance Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profiles_role_active 
ON profiles(role, is_active);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profiles_created_at 
ON profiles(created_at DESC);

-- Activities Feed Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_user_created 
ON activities(user_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_type_created 
ON activities(activity_type, created_at DESC);

-- Notifications Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_recipient_read 
ON notifications(recipient_id, is_read, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_type_created 
ON notifications(notification_type, created_at DESC);

-- ============================================================================
-- COMPOSITE INDEXES FOR COMPLEX DASHBOARD QUERIES
-- ============================================================================

-- Weekly attendance tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_weekly_stats 
ON field_staff_attendance(staff_id, attendance_date, total_duration_minutes);

-- Student impact tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_reports_student_impact 
ON field_reports(school_id, report_date, total_students, male_participants, female_participants);

-- Task completion tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_completion_tracking 
ON tasks(assigned_to, status, created_at, updated_at);

-- Distribution performance tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_distributions_performance 
ON book_distributions(supervisor_id, status, delivery_date, quantity);

-- ============================================================================
-- PARTIAL INDEXES FOR SPECIFIC CONDITIONS
-- ============================================================================

-- Active staff only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profiles_active_staff 
ON profiles(id, role, created_at) 
WHERE is_active = true AND role = 'field_staff';

-- Pending tasks only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_pending 
ON tasks(assigned_to, due_date, created_at) 
WHERE status IN ('pending', 'in_progress');

-- Recent activities (last 30 days)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activities_recent 
ON activities(user_id, activity_type, created_at) 
WHERE created_at > (NOW() - INTERVAL '30 days');

-- Unread notifications
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_unread 
ON notifications(recipient_id, created_at DESC) 
WHERE is_read = false;

-- ============================================================================
-- ANALYZE TABLES FOR QUERY PLANNER
-- ============================================================================

-- Update table statistics for better query planning
ANALYZE field_staff_attendance;
ANALYZE field_reports;
ANALYZE tasks;
ANALYZE schools;
ANALYZE book_distributions;
ANALYZE book_inventory;
ANALYZE profiles;
ANALYZE activities;
ANALYZE notifications;

-- ============================================================================
-- INDEX MONITORING VIEWS
-- ============================================================================

-- Create view to monitor index usage
CREATE OR REPLACE VIEW index_usage_stats AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE 
        WHEN idx_scan = 0 THEN 'UNUSED'
        WHEN idx_scan < 100 THEN 'LOW_USAGE'
        WHEN idx_scan < 1000 THEN 'MODERATE_USAGE'
        ELSE 'HIGH_USAGE'
    END as usage_level
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- Create view to monitor table sizes and index effectiveness
CREATE OR REPLACE VIEW table_performance_stats AS
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_rows,
    n_dead_tup as dead_rows,
    CASE 
        WHEN n_live_tup > 0 
        THEN ROUND((n_dead_tup::float / n_live_tup::float) * 100, 2)
        ELSE 0 
    END as dead_row_percentage
FROM pg_stat_user_tables
WHERE schemaname = 'public'
ORDER BY n_live_tup DESC;

-- ============================================================================
-- PERFORMANCE MONITORING FUNCTIONS
-- ============================================================================

-- Function to get slow queries
CREATE OR REPLACE FUNCTION get_slow_queries(min_duration_ms INTEGER DEFAULT 1000)
RETURNS TABLE(
    query_text TEXT,
    calls BIGINT,
    total_time DOUBLE PRECISION,
    mean_time DOUBLE PRECISION,
    max_time DOUBLE PRECISION
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pg_stat_statements.query,
        pg_stat_statements.calls,
        pg_stat_statements.total_exec_time,
        pg_stat_statements.mean_exec_time,
        pg_stat_statements.max_exec_time
    FROM pg_stat_statements
    WHERE pg_stat_statements.mean_exec_time > min_duration_ms
    ORDER BY pg_stat_statements.mean_exec_time DESC
    LIMIT 20;
END;
$$ LANGUAGE plpgsql;

-- Function to analyze index effectiveness
CREATE OR REPLACE FUNCTION analyze_index_effectiveness()
RETURNS TABLE(
    table_name TEXT,
    index_name TEXT,
    index_size TEXT,
    usage_count BIGINT,
    effectiveness_score NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.tablename::TEXT,
        i.indexname::TEXT,
        pg_size_pretty(pg_relation_size(i.indexname::regclass))::TEXT,
        i.idx_scan,
        CASE 
            WHEN i.idx_scan = 0 THEN 0
            ELSE ROUND((i.idx_scan::NUMERIC / GREATEST(t.n_tup_ins + t.n_tup_upd + t.n_tup_del, 1)) * 100, 2)
        END as effectiveness_score
    FROM pg_stat_user_indexes i
    JOIN pg_stat_user_tables t ON i.relid = t.relid
    WHERE i.schemaname = 'public'
    ORDER BY effectiveness_score DESC;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- MAINTENANCE PROCEDURES
-- ============================================================================

-- Function to maintain index health
CREATE OR REPLACE FUNCTION maintain_index_health()
RETURNS TEXT AS $$
DECLARE
    result TEXT := '';
    rec RECORD;
BEGIN
    -- Reindex tables with high dead row percentage
    FOR rec IN 
        SELECT tablename 
        FROM table_performance_stats 
        WHERE dead_row_percentage > 20
    LOOP
        EXECUTE 'REINDEX TABLE ' || rec.tablename;
        result := result || 'Reindexed table: ' || rec.tablename || E'\n';
    END LOOP;
    
    -- Update statistics for all tables
    EXECUTE 'ANALYZE';
    result := result || 'Updated table statistics' || E'\n';
    
    RETURN COALESCE(result, 'No maintenance required');
END;
$$ LANGUAGE plpgsql;
