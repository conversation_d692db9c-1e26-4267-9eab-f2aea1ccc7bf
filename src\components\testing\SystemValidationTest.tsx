import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Play, 
  Shield, 
  Navigation as NavigationIcon,
  FileCheck 
} from 'lucide-react';
import { useAccessControl } from '@/hooks/useAccessControl';
import { useFormValidation } from '@/hooks/useFormValidation';
import { validationSchemas } from '@/utils/validation';
import { 
  navigationConfig, 
  canAccessRoute, 
  getNavigationForRole,
  legacyRouteRedirects 
} from '@/config/navigation';

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: string;
}

const SystemValidationTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const { 
    currentUser, 
    canAccessRoute: userCanAccessRoute, 
    canAccessFeature, 
    hasPermission 
  } = useAccessControl();

  const runTests = async () => {
    setIsRunning(true);
    const results: TestResult[] = [];

    try {
      // Test 1: Access Control System
      results.push(await testAccessControlSystem());
      
      // Test 2: Navigation System
      results.push(await testNavigationSystem());
      
      // Test 3: Validation System
      results.push(await testValidationSystem());
      
      // Test 4: Role-Based Features
      results.push(await testRoleBasedFeatures());
      
      // Test 5: Legacy Route Redirects
      results.push(await testLegacyRouteRedirects());

    } catch (error) {
      results.push({
        name: 'System Test',
        status: 'fail',
        message: 'Test suite failed to complete',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    setTestResults(results);
    setIsRunning(false);
  };

  const testAccessControlSystem = async (): Promise<TestResult> => {
    try {
      if (!currentUser) {
        return {
          name: 'Access Control System',
          status: 'warning',
          message: 'No user logged in - cannot test access control',
        };
      }

      // Test basic access control functions
      const canViewDashboard = userCanAccessRoute('dashboard');
      const canManageBooks = canAccessFeature('manage-books');
      const hasViewPermission = hasPermission('view_own_data');

      if (canViewDashboard && typeof canManageBooks === 'boolean' && typeof hasViewPermission === 'boolean') {
        return {
          name: 'Access Control System',
          status: 'pass',
          message: 'Access control functions working correctly',
          details: `User: ${currentUser.role}, Dashboard: ${canViewDashboard}, Books: ${canManageBooks}`
        };
      }

      return {
        name: 'Access Control System',
        status: 'fail',
        message: 'Access control functions not working properly',
      };
    } catch (error) {
      return {
        name: 'Access Control System',
        status: 'fail',
        message: 'Access control system error',
        details: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  };

  const testNavigationSystem = async (): Promise<TestResult> => {
    try {
      // Test navigation configuration
      const hasNavItems = navigationConfig.length > 0;
      const hasValidRoutes = navigationConfig.every(item => item.route && item.roles.length > 0);
      
      if (currentUser) {
        const userNavItems = getNavigationForRole(currentUser.role);
        const hasUserNavItems = userNavItems.length > 0;
        
        if (hasNavItems && hasValidRoutes && hasUserNavItems) {
          return {
            name: 'Navigation System',
            status: 'pass',
            message: 'Navigation system working correctly',
            details: `${userNavItems.length} nav items for ${currentUser.role}`
          };
        }
      }

      return {
        name: 'Navigation System',
        status: hasNavItems && hasValidRoutes ? 'warning' : 'fail',
        message: hasNavItems && hasValidRoutes ? 'Navigation config valid but no user context' : 'Navigation configuration invalid',
      };
    } catch (error) {
      return {
        name: 'Navigation System',
        status: 'fail',
        message: 'Navigation system error',
        details: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  };

  const testValidationSystem = async (): Promise<TestResult> => {
    try {
      // Test validation schemas exist
      const hasUserSchema = !!validationSchemas.user;
      const hasBookSchema = !!validationSchemas.book;
      const hasTaskSchema = !!validationSchemas.task;
      
      // Test form validation hook availability (without calling it)
      const hasFormValidation = typeof useFormValidation === 'function';

      if (hasUserSchema && hasBookSchema && hasTaskSchema && hasFormValidation) {
        return {
          name: 'Validation System',
          status: 'pass',
          message: 'Validation system working correctly',
          details: 'All schemas and hooks available'
        };
      }

      return {
        name: 'Validation System',
        status: 'fail',
        message: 'Validation system incomplete',
        details: `User: ${hasUserSchema}, Book: ${hasBookSchema}, Task: ${hasTaskSchema}, Hook: ${hasFormValidation}`
      };
    } catch (error) {
      return {
        name: 'Validation System',
        status: 'fail',
        message: 'Validation system error',
        details: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  };

  const testRoleBasedFeatures = async (): Promise<TestResult> => {
    try {
      if (!currentUser) {
        return {
          name: 'Role-Based Features',
          status: 'warning',
          message: 'No user logged in - cannot test role-based features',
        };
      }

      const role = currentUser.role;
      let expectedFeatures: string[] = [];

      switch (role) {
        case 'admin':
          expectedFeatures = ['manage-user', 'view-analytics', 'access_impact_data'];
          break;
        case 'program_officer':
          expectedFeatures = ['manage-books', 'view-all-reports'];
          break;
        case 'field_staff':
          expectedFeatures = ['create-report', 'check-in-out'];
          break;
      }

      const featureResults = expectedFeatures.map(feature => ({
        feature,
        hasAccess: canAccessFeature(feature)
      }));

      const allFeaturesWork = featureResults.length > 0;

      return {
        name: 'Role-Based Features',
        status: allFeaturesWork ? 'pass' : 'warning',
        message: allFeaturesWork ? 'Role-based features working' : 'No features to test',
        details: featureResults.map(r => `${r.feature}: ${r.hasAccess}`).join(', ')
      };
    } catch (error) {
      return {
        name: 'Role-Based Features',
        status: 'fail',
        message: 'Role-based features error',
        details: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  };

  const testLegacyRouteRedirects = async (): Promise<TestResult> => {
    try {
      const redirectCount = Object.keys(legacyRouteRedirects).length;
      const hasRedirects = redirectCount > 0;
      
      // Test a few key redirects
      const testRedirects = [
        { from: 'attendance', to: 'field-visits' },
        { from: 'book-management', to: 'books' },
        { from: 'tasks-list', to: 'tasks' }
      ];

      const redirectsWork = testRedirects.every(test => 
        legacyRouteRedirects[test.from] === test.to
      );

      if (hasRedirects && redirectsWork) {
        return {
          name: 'Legacy Route Redirects',
          status: 'pass',
          message: 'Legacy route redirects working correctly',
          details: `${redirectCount} redirects configured`
        };
      }

      return {
        name: 'Legacy Route Redirects',
        status: 'fail',
        message: 'Legacy route redirects not working properly',
        details: `Redirects: ${hasRedirects}, Test redirects: ${redirectsWork}`
      };
    } catch (error) {
      return {
        name: 'Legacy Route Redirects',
        status: 'fail',
        message: 'Legacy route redirects error',
        details: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'fail':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return 'bg-green-100 text-green-800';
      case 'fail':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            System Validation Test Suite
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <Button 
                onClick={runTests} 
                disabled={isRunning}
                className="flex items-center gap-2"
              >
                {isRunning ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Running Tests...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4" />
                    Run System Tests
                  </>
                )}
              </Button>
              
              {currentUser && (
                <Badge variant="outline">
                  Testing as: {currentUser.role}
                </Badge>
              )}
            </div>

            {testResults.length > 0 && (
              <div className="space-y-3">
                <h3 className="text-lg font-semibold">Test Results</h3>
                {testResults.map((result, index) => (
                  <Alert key={index}>
                    <div className="flex items-start gap-3">
                      {getStatusIcon(result.status)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium">{result.name}</span>
                          <Badge className={getStatusColor(result.status)}>
                            {result.status.toUpperCase()}
                          </Badge>
                        </div>
                        <AlertDescription>
                          {result.message}
                          {result.details && (
                            <div className="mt-1 text-sm text-gray-600">
                              {result.details}
                            </div>
                          )}
                        </AlertDescription>
                      </div>
                    </div>
                  </Alert>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SystemValidationTest;
