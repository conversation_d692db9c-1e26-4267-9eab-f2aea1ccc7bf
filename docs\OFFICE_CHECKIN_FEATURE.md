# Office Check-in Feature Documentation

## Overview

The Office Check-in feature allows field staff to check in when working from the office for administrative tasks, meetings, or planning activities. This extends the existing school-based check-in system to support office-based work tracking.

## Features

### Core Functionality
- **Dual Check-in Types**: Support for both school visits and office check-ins
- **Geofencing Validation**: 1km radius validation for office check-ins
- **GPS Integration**: Leverages existing adaptive GPS system with battery optimization
- **Offline Support**: Full offline sync capability for office check-ins
- **Role-based Access**: Available to all field staff, program officers, and admins

### User Interface
- **Check-in Type Selector**: Radio button interface to choose between school and office
- **Office Location Selector**: Dropdown with distance validation and status indicators
- **Real-time Validation**: Live feedback on location proximity and GPS accuracy
- **Error Handling**: Comprehensive error messages and fallback options

## Technical Architecture

### Database Schema

#### New Tables
```sql
-- Office locations with geofencing support
CREATE TABLE office_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    location_coordinates POINT NOT NULL,
    geofence_radius_meters INTEGER DEFAULT 1000,
    address TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Schema Extensions
```sql
-- Extended field_staff_attendance table
ALTER TABLE field_staff_attendance 
ADD COLUMN check_in_type check_in_type DEFAULT 'school',
ADD COLUMN office_id UUID REFERENCES office_locations(id);

-- Constraint to ensure either school_id or office_id is provided
ALTER TABLE field_staff_attendance 
ADD CONSTRAINT check_school_or_office_provided 
CHECK (
    (check_in_type = 'school' AND school_id IS NOT NULL AND office_id IS NULL) OR
    (check_in_type = 'office' AND office_id IS NOT NULL AND school_id IS NULL)
);
```

### API Functions

#### Updated RPC Functions
```sql
-- Enhanced field_staff_checkin function
CREATE OR REPLACE FUNCTION field_staff_checkin(
    p_school_id UUID DEFAULT NULL,
    p_office_id UUID DEFAULT NULL,
    p_check_in_type VARCHAR(20) DEFAULT 'school',
    p_latitude DECIMAL(10,8),
    p_longitude DECIMAL(11,8),
    -- ... other parameters
)
```

### Frontend Components

#### New Components
- `CheckInTypeSelector`: Radio button interface for check-in type selection
- `OfficeLocationSelector`: Office selection with distance validation
- `officeGeofencing.ts`: Geofencing utilities and validation logic

#### Updated Components
- `FieldStaffCheckIn`: Enhanced to support both check-in types
- `useFieldStaffAttendance`: Extended hook with office check-in support

## Configuration

### Office Locations
Office locations are configured in the database and can be managed through the admin interface:

```typescript
// Main office configuration
export const MAIN_OFFICE: OfficeLocation = {
  id: 'main-office',
  name: 'Main Office',
  coordinates: {
    latitude: 0.32581475334630144,
    longitude: 32.57564046659227,
  },
  geofenceRadius: 1000, // 1km radius
  address: 'iLead Main Office',
  description: 'Primary office location for administrative staff',
  isActive: true,
};
```

### Geofencing Parameters
- **Office Geofence Radius**: 1000 meters (1km)
- **GPS Accuracy Threshold**: 100 meters
- **GPS Timeout**: 15 seconds
- **High Accuracy GPS**: Enabled for office check-ins

## Usage Workflow

### Office Check-in Process
1. **Select Check-in Type**: User chooses "Office Check-in" from radio options
2. **Office Selection**: System displays available office locations
3. **Location Validation**: GPS location is acquired and validated against geofence
4. **Proximity Check**: System verifies user is within 1km of selected office
5. **Check-in Completion**: Attendance record is created with office reference

### Validation Rules
- **Location Required**: GPS or manual location entry is mandatory
- **Geofence Enforcement**: User must be within office geofence radius
- **Single Check-in**: Only one active check-in per day per location
- **GPS Accuracy**: Warnings for accuracy > 100m, rejection for accuracy > 200m

## Error Handling

### Common Error Scenarios
- **Outside Geofence**: "You are X meters from Office. You must be within 1km to check in."
- **GPS Unavailable**: Fallback to manual location entry
- **Poor GPS Accuracy**: Warning message with option to retry
- **Already Checked In**: Prevention of duplicate check-ins

### Fallback Mechanisms
- **Manual Location Entry**: Available when GPS fails
- **Offline Sync**: Check-ins queued when offline
- **Retry Logic**: Multiple GPS acquisition attempts with adaptive settings

## Testing

### Test Coverage
- **Unit Tests**: Geofencing validation, distance calculations
- **Integration Tests**: Complete check-in flow validation
- **Edge Cases**: Boundary conditions, GPS errors, invalid coordinates
- **Mock Utilities**: Comprehensive testing helpers and mock data

### Test Scenarios
```typescript
export const TEST_SCENARIOS = {
  VALID_OFFICE_CHECKIN: {
    location: INSIDE_OFFICE,
    expectedResult: { isValid: true },
  },
  OUTSIDE_GEOFENCE: {
    location: OUTSIDE_OFFICE,
    expectedResult: { isValid: false },
  },
  POOR_GPS_ACCURACY: {
    location: POOR_ACCURACY,
    expectedResult: { isValid: true, hasWarning: true },
  },
};
```

## Performance Considerations

### Optimizations
- **Battery Optimization**: Adaptive GPS polling with longer intervals
- **Caching**: Office location data cached for 5 minutes
- **Lazy Loading**: Components loaded on-demand
- **Efficient Queries**: Indexed database queries for location lookups

### Monitoring
- **GPS Accuracy Tracking**: Monitor accuracy distribution
- **Geofence Success Rate**: Track validation success/failure rates
- **Performance Metrics**: Check-in completion times and error rates

## Security

### Access Control
- **Role-based Access**: All staff roles can use office check-in
- **Location Validation**: Server-side geofence validation
- **Data Integrity**: Constraints prevent invalid check-in combinations

### Privacy
- **Location Data**: GPS coordinates stored securely
- **Audit Trail**: All check-in activities logged
- **Data Retention**: Location data subject to retention policies

## Future Enhancements

### Planned Features
- **Multiple Offices**: Support for additional office locations
- **Dynamic Geofences**: Configurable radius per office
- **Check-in Analytics**: Office vs field time reporting
- **QR Code Check-in**: Alternative to GPS for indoor locations

### Scalability
- **Office Management UI**: Admin interface for office configuration
- **Bulk Office Import**: CSV import for multiple office locations
- **Regional Offices**: Support for distributed office network
- **Integration APIs**: External system integration capabilities

## Migration Guide

### Database Migration
1. Run migration `023_add_office_checkin_support.sql`
2. Run migration `024_update_checkin_functions_for_office.sql`
3. Verify office location data insertion
4. Test RPC function updates

### Frontend Deployment
1. Deploy new components and utilities
2. Update existing check-in flows
3. Test offline sync compatibility
4. Verify role-based access controls

### Rollback Plan
- Database schema changes are backward compatible
- Frontend gracefully degrades to school-only mode
- Existing check-in data remains unaffected
