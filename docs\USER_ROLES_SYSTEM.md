# User Roles System Documentation

## Overview

The iLead Uganda application implements a comprehensive role-based access control (RBAC) system with seven distinct user roles. This system ensures that users have appropriate access to features and data based on their organizational responsibilities.

## User Roles

### 1. Admin
- **Hierarchy Level**: 3 (Highest)
- **Description**: System administrators with full access to all features
- **Permissions**:
  - Manage all users (create, update, delete)
  - Manage all schools and data
  - Full book management and distributions
  - View all reports and analytics
  - Manage system settings
  - Access impact measurement tools
  - Full staff management capabilities
  - Access to all administrative functions

### 2. Program Officer
- **Hierarchy Level**: 2
- **Description**: Program management staff with elevated access
- **Permissions**:
  - Manage schools and educational data
  - Full book management and distributions
  - View all field reports
  - Manage and assign tasks
  - View analytics and impact data
  - Limited user management (add users, reset passwords)
  - Access to program oversight features

### 3. Field Staff (Legacy)
- **Hierarchy Level**: 1
- **Description**: Original field-level role, maintained for backward compatibility
- **Permissions**:
  - View own data only
  - Create field reports
  - Manage own tasks
  - Check-in/check-out functionality
  - View assigned schools (read-only)
  - Access profile settings

### 4. Staff (Default)
- **Hierarchy Level**: 1
- **Description**: General staff members, new default role for users
- **Permissions**: Same as Field Staff
  - View own data only
  - Create field reports
  - Manage own tasks
  - Check-in/check-out functionality
  - View assigned schools (read-only)
  - Access profile settings

### 5. Partner
- **Hierarchy Level**: 1
- **Description**: External partners and collaborators
- **Permissions**: Same as Field Staff
  - View own data only
  - Create field reports
  - Manage own tasks
  - Check-in/check-out functionality
  - View assigned schools (read-only)
  - Access profile settings

### 6. Accountant
- **Hierarchy Level**: 1
- **Description**: Financial and inventory management staff
- **Permissions**: Field Staff permissions plus:
  - **Full book management access** (create, update, delete books)
  - **Full distribution management** (log and manage distributions)
  - Access to book inventory features
  - Financial reporting capabilities

### 7. Social Media Manager
- **Hierarchy Level**: 1
- **Description**: Communications and social media staff
- **Permissions**: Same as Field Staff
  - View own data only
  - Create field reports
  - Manage own tasks
  - Check-in/check-out functionality
  - View assigned schools (read-only)
  - Access profile settings

## Role Hierarchy and Access Patterns

### Elevated Access Roles
- **Admin** and **Program Officer** have elevated access
- Can view all data across the system
- Can manage other users and system-wide settings

### Field-Level Roles
- **Field Staff**, **Staff**, **Partner**, **Accountant**, **Social Media Manager**
- Restricted to own data access
- Cannot view other users' data unless explicitly granted
- Focus on field operations and data collection

### Special Access: Accountant
- Only field-level role with additional permissions
- Gets book management access typically reserved for elevated roles
- Can manage inventory and financial aspects of the program

## Navigation and Feature Access

### Available to All Roles
- Dashboard (role-specific views)
- Field Visits (own data)
- Tasks (own tasks)
- Schools (read-only)
- Settings (profile and notifications)
- Help & Support

### Admin and Program Officer Only
- Staff Management
- Impact Analytics
- System Administration
- User Management

### Admin, Program Officer, and Accountant
- Books Management
- Distribution Management
- Inventory Control

## Implementation Details

### Database Schema
```sql
-- User role enum in PostgreSQL
CREATE TYPE user_role AS ENUM (
    'admin',
    'program_officer', 
    'field_staff',
    'staff',
    'partner',
    'accountant',
    'social_media_manager'
);
```

### TypeScript Types
```typescript
// Generated from Supabase
type UserRole = 
  | 'admin'
  | 'program_officer'
  | 'field_staff'
  | 'staff'
  | 'partner'
  | 'accountant'
  | 'social_media_manager';
```

### Role Checking Utilities
```typescript
// RBAC utility functions
RoleChecker.isAdmin(userRole)
RoleChecker.isProgramOfficer(userRole)
RoleChecker.isFieldStaff(userRole)
RoleChecker.isStaff(userRole)
RoleChecker.isPartner(userRole)
RoleChecker.isAccountant(userRole)
RoleChecker.isSocialMediaManager(userRole)
RoleChecker.isFieldLevelRole(userRole)
RoleChecker.canManageBooks(userRole)
```

## Security Implementation

### Row Level Security (RLS)
- All database tables have RLS policies updated for new roles
- Field-level roles can only access their own data
- Elevated roles have broader access patterns
- Accountants have special book management access

### Frontend Access Control
- Route-level access control
- Component-level access control
- Feature-level permissions
- Data filtering based on role

### API Endpoint Security
- Server-side validation functions
- Role-based endpoint access
- Ownership validation for field-level roles

## Migration and Backward Compatibility

### Database Migrations
1. `031_add_new_user_roles.sql` - Adds new roles to enum
2. `032_update_rls_policies_for_new_roles.sql` - Updates RLS policies
3. `033_update_server_validation_for_new_roles.sql` - Updates server functions

### Default Role Change
- New users now default to 'staff' role instead of 'field_staff'
- Existing 'field_staff' users maintain their role and permissions
- No breaking changes for existing users

## Best Practices

### Role Assignment
- Use 'staff' as default for new general users
- Reserve 'field_staff' for legacy compatibility
- Assign 'accountant' only to users needing book management
- Use 'partner' for external collaborators
- Use 'social_media_manager' for communications staff

### Access Control
- Always use RBAC utilities instead of hardcoded role checks
- Implement defense in depth (frontend + backend validation)
- Test role permissions thoroughly
- Document any role-specific business logic

### Future Considerations
- Role system is extensible for additional roles
- Permissions can be refined per role as needed
- Consider role-based feature flags for gradual rollouts
- Monitor role usage and access patterns
