# iLead Field Track - Project Index

## 📋 Project Overview

**iLead Field Beacon** is a comprehensive web-based platform designed to streamline field operations for iLead's leadership training initiatives across African schools. The system manages task assignment, book distribution tracking, attendance monitoring, and impact measurement for NGO field operations.

### 🎯 Core Objectives
- Streamline task management and field reporting
- Track book distribution to schools with inventory management
- Monitor attendance and session tracking with GPS integration
- Measure impact through student leadership training programs
- Provide analytics and reporting for stakeholders
- Enable mobile-friendly field operations

## 🛠️ Technology Stack

### Frontend
- **Framework**: React 18 + TypeScript
- **Build Tool**: Vite
- **UI Library**: shadcn/ui + Radix UI
- **Styling**: Tailwind CSS + tailwindcss-animate
- **State Management**: TanStack Query (React Query)
- **Routing**: React Router DOM
- **Forms**: React Hook Form + Zod validation
- **Charts**: Recharts
- **Icons**: Lucide React
- **Notifications**: Sonner + Custom Toast

### Backend & Database
- **Backend**: Supabase (PostgreSQL + Auth + Real-time)
- **Authentication**: Supa<PERSON> Auth with role-based access
- **Database**: PostgreSQL with 20+ migrations
- **Real-time**: Supabase real-time subscriptions
- **File Storage**: Supabase Storage (for photos/documents)

### Development Tools
- **Package Manager**: npm
- **Linting**: ESLint + TypeScript ESLint
- **Type Checking**: TypeScript 5.5+
- **CSS Processing**: PostCSS + Autoprefixer
- **Deployment**: Lovable Platform

## 🏗️ Architecture Overview

### Application Structure
```
src/
├── components/          # Feature-based component modules
├── hooks/              # Custom React hooks for state management
├── integrations/       # External service integrations (Supabase)
├── lib/               # Utility libraries and configurations
├── pages/             # Route components
├── types/             # TypeScript type definitions
└── utils/             # Helper functions and utilities
```

### Authentication Flow
1. **Login Component** → Supabase Auth
2. **Role-based Routing** → Admin/Program Officer/Field Staff
3. **Protected Routes** → AuthenticatedApp wrapper
4. **Profile Management** → User roles and permissions

## 🎯 Core Features & Modules

### 1. User Management & Authentication
- **Roles**: Admin, Program Officer, Field Staff
- **Permissions**: Role-based access control
- **Profile Management**: User profiles with role assignments
- **Security**: Supabase Auth with JWT tokens

### 2. Task Management System
- **CRUD Operations**: Create, read, update, delete tasks
- **Assignment Workflow**: Assign tasks to field staff
- **Status Tracking**: Pending, In Progress, Completed
- **Comments System**: Task collaboration and updates
- **Priority Management**: High, Medium, Low priority levels
- **Due Date Tracking**: Deadline management and reminders

### 3. School Management
- **School Types**: Primary, Secondary, Tertiary, Vocational
- **School Status**: Active, Inactive school tracking
- **Contact Management**: Head teacher and school contact details
- **Location Tracking**: GPS coordinates and address information
- **Student Counts**: Enrollment tracking per term

### 4. Book Distribution & Inventory
- **Book Catalog**: Comprehensive book management with categories
- **Inventory Tracking**: Stock levels, conditions, and availability
- **Distribution Logging**: Track book deliveries to schools
- **Grade Level Mapping**: Books categorized by educational level
- **Language Support**: Multiple local languages supported
- **Condition Tracking**: New, Good, Fair, Poor condition states

### 5. Attendance & GPS Tracking
- **Field Staff Check-in/out**: GPS-based attendance tracking
- **Session Management**: Track training sessions and participants
- **Location Verification**: GPS accuracy and verification methods
- **Offline Sync**: Support for offline data collection
- **Device Information**: Track device and network information
- **Duration Tracking**: Automatic time calculation

### 6. Impact Measurement & Analytics
- **Student Leadership Training**: Track training programs and outcomes
- **Beneficiary Feedback**: Collect and analyze feedback
- **Longitudinal Tracking**: Long-term impact measurement
- **Performance Metrics**: Staff and program performance analytics
- **Dashboard Analytics**: Real-time insights and KPIs

### 7. Reporting & Export
- **Excel Export**: Data export functionality
- **Custom Reports**: Filtered and customized reporting
- **Analytics Dashboard**: Visual data representation
- **Activity Feed**: Real-time activity tracking
- **Performance Reports**: Staff and program performance metrics

## 🗄️ Database Schema

### Core Tables
- **profiles**: User management and roles
- **schools**: School information and contacts
- **tasks**: Task management with assignments
- **books**: Book catalog and metadata
- **book_inventory**: Stock tracking and conditions
- **book_distributions**: Distribution logging
- **field_reports**: Field activity reports
- **attendance_sessions**: Session tracking
- **location_logs**: GPS tracking data
- **activities**: Activity feed and notifications

### Key Enums
- **user_role**: admin, program_officer, field_staff, staff, partner, accountant, social_media_manager
- **school_type**: primary, secondary, tertiary, vocational
- **book_category**: mathematics, english, science, etc.
- **grade_level**: nursery through s6
- **book_language**: english, luganda, runyankole, etc.
- **book_condition**: new, good, fair, poor

## 📱 Component Architecture

### Layout Components
- **AuthenticatedApp**: Main application wrapper
- **Navigation**: Sidebar and mobile navigation
- **Header/Footer**: Application chrome
- **Dashboard**: Main dashboard with KPIs

### Feature Components
```
components/
├── attendance/         # GPS tracking, session management
├── books/             # Book management and inventory
├── distributions/     # Book distribution workflows
├── field-staff/       # Staff attendance and reporting
├── impact/           # Analytics and impact measurement
├── reports/          # Reporting modules
├── schools/          # School management
├── tasks/            # Task management UI
└── ui/               # Reusable UI components
```

### Custom Hooks
```
hooks/
├── auth/             # Authentication state
├── tasks/            # Task operations and queries
├── attendance/       # GPS and attendance tracking
├── activities/       # Activity feed management
├── field-staff/      # Staff-specific operations
└── impact/           # Impact measurement hooks
```

## 🚀 Development Setup

### Prerequisites
- Node.js 18+ with npm
- Supabase account and project
- Git for version control

### Installation
```bash
# Clone repository
git clone https://github.com/tmx34/ilead-field-track.git
cd ilead-field-track

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Run linting
npm run lint
```

### Environment Configuration
- Supabase URL and API keys configured in `src/integrations/supabase/client.ts`
- Database types generated via `npm run update-types`

## 🔄 Key Workflows

### 1. Field Staff Daily Workflow
1. **Login** → Role-based dashboard
2. **Check-in** → GPS location verification
3. **View Tasks** → Assigned tasks for the day
4. **Visit School** → GPS tracking and session logging
5. **Log Activities** → Book distributions, training sessions
6. **Submit Reports** → Field reports with photos
7. **Check-out** → End of day GPS verification

### 2. Admin Management Workflow
1. **Dashboard Overview** → KPIs and recent activities
2. **Manage Schools** → Add/edit school information
3. **Book Inventory** → Manage book catalog and stock
4. **Task Assignment** → Create and assign tasks to staff
5. **Monitor Progress** → Track task completion and attendance
6. **Generate Reports** → Export data and analytics
7. **Impact Analysis** → Review program effectiveness

### 3. Book Distribution Workflow
1. **Inventory Check** → Verify available stock
2. **Create Distribution** → Select school and books
3. **Field Delivery** → GPS-tracked delivery process
4. **Log Receipt** → Record delivery confirmation
5. **Update Inventory** → Automatic stock adjustment
6. **Generate Report** → Distribution summary and impact

## 📊 Current Implementation Status

### ✅ Completed Features
- User authentication and role management
- Task management with CRUD operations
- School management (primary/secondary)
- Book catalog and inventory management
- Basic GPS tracking and attendance
- Activity feed and notifications
- Dashboard with KPIs
- Export functionality
- Mobile-responsive design

### 🚧 In Development
- Advanced impact measurement
- Enhanced reporting and analytics
- Multi-region support
- Offline synchronization
- Advanced GPS verification

### 📋 Future Roadmap
- **Phase 2**: Enhanced tracking and notifications
- **Phase 3**: Performance assessment and analytics
- **Phase 4**: Multi-region scaling and optimization
- **Phase 5**: Stakeholder dashboards and API integration

## 🔗 Key Integrations

### Supabase Services
- **Authentication**: User management and sessions
- **Database**: PostgreSQL with real-time subscriptions
- **Storage**: File uploads for photos and documents
- **Edge Functions**: Server-side processing (planned)

### External APIs
- **GPS Services**: Browser geolocation API
- **Export Services**: Client-side Excel generation
- **Notification Services**: Browser notifications

## 📚 Documentation

### Available Documentation
- `docs/project_brief.md` - Project requirements and roadmap
- `docs/book-management-system-completion-summary.md` - Book system implementation
- `docs/book-management-user-guide.md` - User guide for book management
- `docs/book-management-audit-report.md` - System audit and validation
- `docs/RULES.md` - Development guidelines and rules

### Code Documentation
- TypeScript interfaces and types
- Component prop documentation
- Hook usage examples
- Database schema documentation

---

**Last Updated**: June 26, 2025  
**Version**: Sprint-2  
**Repository**: https://github.com/tmx34/ilead-field-track.git  
**Deployment**: Lovable Platform
