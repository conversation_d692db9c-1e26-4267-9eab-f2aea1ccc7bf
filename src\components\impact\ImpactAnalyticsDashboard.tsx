import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  TrendingUp,
  Users,
  GraduationCap,
  BookOpen,
  School,
  BarChart3,
  Download,
  Filter,
  Calendar,
  Target
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { PageLayout, PageHeader } from '@/components/layout';
import StudentOutcomesModule from './student-outcomes/StudentOutcomesModule';
import SchoolPerformanceDashboard from './school-performance/SchoolPerformanceDashboard';
import StudentLeadershipTrainingModule from './student-leadership/StudentLeadershipTrainingModule';
import BeneficiaryFeedbackModule from './beneficiary-feedback/BeneficiaryFeedbackModule';
import LongTermImpactModule from './longitudinal-tracking/LongTermImpactModule';
import ImpactMetricsCards from './analytics/ImpactMetricsCards';
import ImpactCharts from './analytics/ImpactCharts';
import ImpactReports from './analytics/ImpactReports';
import ComprehensiveImpactDashboard from './analytics/ComprehensiveImpactDashboard';

interface ImpactAnalyticsDashboardProps {
  onViewChange?: (view: string) => void;
}

const ImpactAnalyticsDashboard: React.FC<ImpactAnalyticsDashboardProps> = ({ onViewChange }) => {
  const { profile } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedSchool, setSelectedSchool] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState({
    start: new Date(new Date().getFullYear() - 1, 0, 1),
    end: new Date()
  });

  const isAdmin = profile?.role === 'admin';
  const isProgramOfficer = profile?.role === 'program_officer';
  const canViewAllData = isAdmin || isProgramOfficer;

  const impactModules = [
    {
      id: 'student-outcomes',
      title: 'Student Leadership Development',
      description: 'Track leadership skills development and program effectiveness',
      icon: GraduationCap,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      component: StudentOutcomesModule
    },
    {
      id: 'school-performance',
      title: 'School Performance',
      description: 'Monitor infrastructure, attendance, and improvements',
      icon: School,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      component: SchoolPerformanceDashboard
    },
    {
      id: 'student-leadership',
      title: 'Student Leadership Training',
      description: 'Track student leadership development and program effectiveness',
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      component: StudentLeadershipTrainingModule
    },
    {
      id: 'beneficiary-feedback',
      title: 'Beneficiary Feedback',
      description: 'Collect and analyze stakeholder satisfaction',
      icon: BookOpen,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      component: BeneficiaryFeedbackModule
    },
    {
      id: 'longitudinal-tracking',
      title: 'Long-term Impact',
      description: 'Analyze trends and sustained improvements',
      icon: TrendingUp,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      component: LongTermImpactModule
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <ComprehensiveImpactDashboard
            schoolId={selectedSchool}
            dateRange={dateRange}
            canViewAllData={canViewAllData}
          />
        );
      
      case 'reports':
        return (
          <ImpactReports 
            schoolId={selectedSchool} 
            dateRange={dateRange}
            canViewAllData={canViewAllData}
          />
        );
      
      default: {
        const module = impactModules.find(m => m.id === activeTab);
        if (module) {
          const Component = module.component;
          return (
            <Component
              schoolId={selectedSchool}
              dateRange={dateRange}
              canViewAllData={canViewAllData}
            />
          );
        }
        return null;
      }
    }
  };

  return (
    <PageLayout>
      <PageHeader
        title="Impact Measurement & Outcomes"
        description="Comprehensive tracking of educational impact and program effectiveness"
        icon={BarChart3}
        actions={[
          {
            label: 'Filters',
            onClick: () => {},
            icon: Filter,
            variant: 'outline'
          },
          {
            label: 'Date Range',
            onClick: () => {},
            icon: Calendar,
            variant: 'outline'
          },
          {
            label: 'Export Report',
            onClick: () => {},
            icon: Download,
          }
        ]}
      />

      {/* Quick Stats Overview */}
      {canViewAllData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {impactModules.map((module) => {
            const Icon = module.icon;
            return (
              <Card
                key={module.id}
                className="border-l-4 border-l-ilead-green cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => setActiveTab(module.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className={`${module.bgColor} p-2 rounded-lg`}>
                      <Icon className={`h-5 w-5 ${module.color}`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {module.title}
                      </p>
                      <p className="text-xs text-gray-500 truncate">
                        {module.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 lg:grid-cols-7">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <Target className="h-4 w-4" />
            <span className="hidden sm:inline">Overview</span>
          </TabsTrigger>
          
          {impactModules.map((module) => {
            const Icon = module.icon;
            return (
              <TabsTrigger 
                key={module.id} 
                value={module.id}
                className="flex items-center space-x-2"
              >
                <Icon className="h-4 w-4" />
                <span className="hidden lg:inline">{module.title.split(' ')[0]}</span>
              </TabsTrigger>
            );
          })}
          
          <TabsTrigger value="reports" className="flex items-center space-x-2">
            <Download className="h-4 w-4" />
            <span className="hidden sm:inline">Reports</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-6">
          {renderTabContent()}
        </TabsContent>
      </Tabs>

      {/* Role-based Access Notice */}
      {!canViewAllData && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="bg-yellow-100 p-2 rounded-lg">
                <Users className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-yellow-800">
                  Limited Access
                </p>
                <p className="text-xs text-yellow-700">
                  You can view impact data for schools where you have assigned tasks. 
                  Contact your program officer for broader access.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </PageLayout>
  );
};

export default ImpactAnalyticsDashboard;
