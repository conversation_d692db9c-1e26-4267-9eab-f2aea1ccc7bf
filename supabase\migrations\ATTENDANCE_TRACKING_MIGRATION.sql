-- =====================================================
-- ATTENDANCE TRACKING SYSTEM - COMPLETE MIGRATION
-- =====================================================
-- This script contains all the database functions needed for the attendance tracking system
-- Run this in your Supabase SQL Editor or database console

-- =====================================================
-- 1. OFFICE CHECK-IN SUPPORT (if not already applied)
-- =====================================================

-- Create check-in type enum if it doesn't exist
DO $$ BEGIN
    CREATE TYPE check_in_type AS ENUM ('school', 'office');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create office locations table if it doesn't exist
CREATE TABLE IF NOT EXISTS office_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    location_coordinates POINT NOT NULL,
    geofence_radius_meters INTEGER DEFAULT 1000,
    address TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add office check-in support to field_staff_attendance if columns don't exist
DO $$ BEGIN
    ALTER TABLE field_staff_attendance 
    ADD COLUMN check_in_type check_in_type DEFAULT 'school';
EXCEPTION
    WHEN duplicate_column THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE field_staff_attendance 
    ADD COLUMN office_id UUID REFERENCES office_locations(id);
EXCEPTION
    WHEN duplicate_column THEN null;
END $$;

-- Make school_id optional for office check-ins
ALTER TABLE field_staff_attendance 
ALTER COLUMN school_id DROP NOT NULL;

-- Add constraint to ensure either school_id or office_id is provided
DO $$ BEGIN
    ALTER TABLE field_staff_attendance 
    ADD CONSTRAINT check_school_or_office_provided 
    CHECK (
        (check_in_type = 'school' AND school_id IS NOT NULL AND office_id IS NULL) OR
        (check_in_type = 'office' AND office_id IS NOT NULL AND school_id IS NULL)
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Insert main office location if it doesn't exist
INSERT INTO office_locations (
    name, 
    location_coordinates, 
    geofence_radius_meters, 
    address, 
    description
) 
SELECT 
    'Main Office',
    POINT(32.57564046659227, 0.32581475334630144),
    1000,
    'iLead Main Office',
    'Primary office location for administrative staff and field staff office days'
WHERE NOT EXISTS (
    SELECT 1 FROM office_locations WHERE name = 'Main Office'
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_field_staff_attendance_check_in_type ON field_staff_attendance(check_in_type);
CREATE INDEX IF NOT EXISTS idx_field_staff_attendance_office_id ON field_staff_attendance(office_id);
CREATE INDEX IF NOT EXISTS idx_office_locations_active ON office_locations(is_active);
CREATE INDEX IF NOT EXISTS idx_office_locations_coordinates ON office_locations USING GIST(location_coordinates);
CREATE INDEX IF NOT EXISTS idx_field_staff_attendance_date_staff ON field_staff_attendance(attendance_date, staff_id);
CREATE INDEX IF NOT EXISTS idx_field_staff_attendance_type_date ON field_staff_attendance(check_in_type, attendance_date);
CREATE INDEX IF NOT EXISTS idx_field_staff_attendance_status ON field_staff_attendance(status);

-- Update existing records to have 'school' check-in type
UPDATE field_staff_attendance 
SET check_in_type = 'school' 
WHERE check_in_type IS NULL;

-- =====================================================
-- 2. ATTENDANCE TRACKING FUNCTIONS
-- =====================================================

-- Function to get office locations
CREATE OR REPLACE FUNCTION get_office_locations()
RETURNS TABLE (
    id UUID,
    name VARCHAR(255),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    geofence_radius_meters INTEGER,
    address TEXT,
    description TEXT,
    is_active BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ol.id,
        ol.name,
        ST_Y(ol.location_coordinates)::DECIMAL(10,8) as latitude,
        ST_X(ol.location_coordinates)::DECIMAL(11,8) as longitude,
        ol.geofence_radius_meters,
        ol.address,
        ol.description,
        ol.is_active
    FROM office_locations ol
    WHERE ol.is_active = true
    ORDER BY ol.name;
END;
$$;
