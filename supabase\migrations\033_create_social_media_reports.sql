-- Social Media Reports Migration
-- Migration 033: Create social media reports table and related functionality

-- Create social media report status enum
CREATE TYPE social_media_report_status AS ENUM ('draft', 'submitted', 'reviewed', 'approved', 'published');

-- Create media asset type enum
CREATE TYPE media_asset_type AS ENUM ('photo', 'video', 'audio', 'document');

-- Create story type enum
CREATE TYPE story_type AS ENUM ('success_story', 'challenge_overcome', 'student_spotlight', 'teacher_feature', 'community_impact', 'program_highlight');

-- Create social media platform enum
CREATE TYPE social_media_platform AS ENUM ('facebook', 'instagram', 'twitter', 'linkedin', 'youtube', 'tiktok', 'website');

-- Create social media reports table
CREATE TABLE social_media_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    field_report_id UUID REFERENCES field_reports(id),
    staff_id UUID REFERENCES profiles(id) NOT NULL,
    school_id UUID REFERENCES schools(id),
    
    -- Basic Information
    activity_date DATE NOT NULL,
    activity_duration INTERVAL,
    primary_activity VARCHAR(100),
    location_context TEXT,
    
    -- Media Assets (JSONB array)
    -- Structure: [{"type": "photo", "title": "...", "description": "...", "google_drive_link": "...", "file_format": "JPG", "estimated_size": "2.5MB", "capture_timestamp": "...", "location_context": "...", "usage_rights": "full", "featured": true}]
    media_assets JSONB DEFAULT '[]',
    
    -- Story Elements (JSONB array)
    -- Structure: [{"story_type": "success_story", "headline": "...", "narrative": "...", "key_quotes": ["..."], "participants": [{"name": "...", "role": "student", "consent_obtained": true, "quote": "..."}], "social_media_angle": "..."}]
    story_elements JSONB DEFAULT '[]',
    
    -- Activity Documentation
    -- Structure: {"activity_name": "...", "activity_type": "leadership_training", "participants_count": {"total": 25, "male": 12, "female": 13, "age_groups": {"10-14": 15, "15-18": 10, "19+": 0}}, "key_moments": ["..."], "learning_outcomes": ["..."], "visual_highlights": ["..."]}
    activity_documentation JSONB DEFAULT '{}',
    
    -- Social Media Strategy
    -- Structure: {"primary_platforms": ["facebook", "instagram"], "content_themes": ["youth empowerment"], "hashtag_suggestions": ["#iLeadUganda"], "posting_schedule": {"immediate": ["..."], "weekly": ["..."], "monthly": ["..."]}, "cross_promotion_opportunities": ["..."]}
    content_strategy JSONB DEFAULT '{}',
    
    -- Impact Messaging
    -- Structure: {"quantitative_impact": {"students_reached": 25, "sessions_conducted": 3, "skills_developed": ["leadership"], "books_distributed": 50}, "qualitative_impact": {"behavior_changes": ["..."], "confidence_improvements": ["..."], "leadership_demonstrations": ["..."], "community_feedback": ["..."]}, "success_metrics": ["..."], "call_to_action": "..."}
    impact_messaging JSONB DEFAULT '{}',
    
    -- Media Management
    -- Structure: {"google_drive_folder": {"main_folder_link": "...", "photos_subfolder": "...", "videos_subfolder": "...", "raw_footage_subfolder": "..."}, "file_organization": {"naming_convention": "...", "quality_levels": {"high_res": "...", "web_optimized": "...", "social_media": "..."}}, "backup_locations": ["..."]}
    media_management JSONB DEFAULT '{}',
    
    -- Compliance & Permissions
    -- Structure: {"photo_consent_forms": {"students_consent": true, "parents_consent": true, "teachers_consent": true, "consent_form_links": ["..."]}, "usage_permissions": {"internal_use": true, "social_media_use": true, "website_use": true, "print_materials": false, "donor_reports": true}, "privacy_considerations": ["..."], "data_retention_period": "2 years"}
    compliance_permissions JSONB DEFAULT '{}',
    
    -- Metadata
    status social_media_report_status DEFAULT 'draft',
    submitted_at TIMESTAMP WITH TIME ZONE,
    reviewed_by UUID REFERENCES profiles(id),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    review_comments TEXT,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_activity_date CHECK (activity_date <= CURRENT_DATE),
    CONSTRAINT valid_duration CHECK (activity_duration IS NULL OR activity_duration >= INTERVAL '0 minutes')
);

-- Create indexes for performance
CREATE INDEX idx_social_media_reports_staff_id ON social_media_reports(staff_id);
CREATE INDEX idx_social_media_reports_school_id ON social_media_reports(school_id);
CREATE INDEX idx_social_media_reports_activity_date ON social_media_reports(activity_date);
CREATE INDEX idx_social_media_reports_status ON social_media_reports(status);
CREATE INDEX idx_social_media_reports_field_report_id ON social_media_reports(field_report_id);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_social_media_reports_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_social_media_reports_updated_at
    BEFORE UPDATE ON social_media_reports
    FOR EACH ROW
    EXECUTE FUNCTION update_social_media_reports_updated_at();

-- RLS Policies for social_media_reports
ALTER TABLE social_media_reports ENABLE ROW LEVEL SECURITY;

-- Social media managers can view and manage their own reports
CREATE POLICY "Social media managers can view their own reports" ON social_media_reports
    FOR SELECT USING (
        staff_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

CREATE POLICY "Social media managers can create their own reports" ON social_media_reports
    FOR INSERT WITH CHECK (
        staff_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('social_media_manager', 'admin', 'program_officer')
        )
    );

CREATE POLICY "Social media managers can update their own reports" ON social_media_reports
    FOR UPDATE USING (
        staff_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

CREATE POLICY "Admins and program officers can delete social media reports" ON social_media_reports
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON social_media_reports TO authenticated;
GRANT DELETE ON social_media_reports TO authenticated;

-- Add activity type for social media reports
ALTER TYPE activity_type ADD VALUE IF NOT EXISTS 'social_media_report_submitted';

-- Create function to submit social media report
CREATE OR REPLACE FUNCTION submit_social_media_report(
    p_report_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    report_record social_media_reports%ROWTYPE;
BEGIN
    -- Validate that user is authenticated
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated';
    END IF;

    -- Get the report
    SELECT * INTO report_record
    FROM social_media_reports
    WHERE id = p_report_id AND staff_id = auth.uid();

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Social media report not found or access denied';
    END IF;

    -- Check if already submitted
    IF report_record.status != 'draft' THEN
        RAISE EXCEPTION 'Report has already been submitted';
    END IF;

    -- Update report status
    UPDATE social_media_reports
    SET status = 'submitted',
        submitted_at = NOW()
    WHERE id = p_report_id;

    -- Log activity
    INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description)
    VALUES (
        'social_media_report_submitted'::activity_type,
        auth.uid(),
        'task'::entity_type,
        p_report_id,
        'Social media report submitted for ' || report_record.primary_activity
    );

    RETURN TRUE;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION submit_social_media_report TO authenticated;

-- Add comment for documentation
COMMENT ON TABLE social_media_reports IS 'Social media documentation reports for field activities, focusing on content creation and communications strategy';
COMMENT ON COLUMN social_media_reports.media_assets IS 'Array of media assets with Google Drive links and metadata';
COMMENT ON COLUMN social_media_reports.story_elements IS 'Array of story elements for social media content creation';
COMMENT ON COLUMN social_media_reports.content_strategy IS 'Social media strategy and platform planning';
COMMENT ON COLUMN social_media_reports.compliance_permissions IS 'Consent forms and usage permissions for media assets';
