/**
 * Real-time Tasks Hook
 * Provides real-time task updates using WebSocket subscriptions
 */

import { useState, useEffect, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/useAuth';
import { realtimeSubscriptionManager } from '@/services/realtimeSubscriptionManager';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// ============================================================================
// TYPES
// ============================================================================

export interface Task {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assigned_to?: string;
  assigned_by?: string;
  due_date?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, unknown>;
}

export interface TaskUpdate {
  taskId: string;
  field: string;
  oldValue: unknown;
  newValue: unknown;
  updatedBy: string;
  timestamp: string;
}

export interface TaskStats {
  total: number;
  pending: number;
  inProgress: number;
  completed: number;
  overdue: number;
  byPriority: Record<string, number>;
}

// ============================================================================
// REAL-TIME TASKS HOOK
// ============================================================================

export const useRealtimeTasks = (userId?: string) => {
  const { user, profile } = useAuth();
  const queryClient = useQueryClient();
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [subscriptionIds, setSubscriptionIds] = useState<string[]>([]);
  const [recentUpdates, setRecentUpdates] = useState<TaskUpdate[]>([]);

  const targetUserId = userId || user?.id;

  // ============================================================================
  // TASKS QUERY (INITIAL LOAD)
  // ============================================================================

  const {
    data: tasks,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['tasks', targetUserId],
    queryFn: async (): Promise<Task[]> => {
      if (!targetUserId) return [];

      let query = supabase
        .from('tasks')
        .select('*')
        .order('created_at', { ascending: false });

      // Filter based on user role and target user
      if (profile?.role === 'field_staff') {
        query = query.eq('assigned_to', targetUserId);
      } else if (targetUserId !== user?.id) {
        query = query.eq('assigned_to', targetUserId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching tasks:', error);
        throw error;
      }

      return data || [];
    },
    enabled: !!targetUserId,
    staleTime: 5 * 60 * 1000, // 5 minutes (longer since we have real-time updates)
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // ============================================================================
  // REAL-TIME SUBSCRIPTION HANDLERS
  // ============================================================================

  const handleTaskInsert = useCallback((payload: Record<string, unknown>) => {
    console.log('📋 New task received:', payload);
    
    const newTask = payload.new as Task;
    
    // Only add if it's relevant to the current user
    const isRelevant = 
      newTask.assigned_to === targetUserId ||
      (profile?.role !== 'field_staff' && !userId); // Admins/POs see all tasks if no specific user filter
    
    if (!isRelevant) return;
    
    // Update tasks cache
    queryClient.setQueryData(['tasks', targetUserId], (oldData: Task[] = []) => {
      // Check if task already exists (prevent duplicates)
      const exists = oldData.some(task => task.id === newTask.id);
      if (exists) return oldData;
      
      return [newTask, ...oldData];
    });
    
    // Show notification for new assignments
    if (newTask.assigned_to === user?.id) {
      toast.info('New Task Assigned', {
        description: newTask.title,
        duration: 5000,
      });
    }
    
    // Track update
    setRecentUpdates(prev => [{
      taskId: newTask.id,
      field: 'created',
      oldValue: null,
      newValue: newTask,
      updatedBy: newTask.assigned_by || 'system',
      timestamp: newTask.created_at,
    }, ...prev].slice(0, 20));
    
  }, [targetUserId, profile?.role, userId, user?.id, queryClient]);

  const handleTaskUpdate = useCallback((payload: Record<string, unknown>) => {
    console.log('📋 Task updated:', payload);
    
    const updatedTask = payload.new as Task;
    const oldTask = payload.old as Task;
    
    // Only process if it's relevant to the current user
    const isRelevant = 
      updatedTask.assigned_to === targetUserId ||
      oldTask.assigned_to === targetUserId ||
      (profile?.role !== 'field_staff' && !userId);
    
    if (!isRelevant) return;
    
    // Update tasks cache
    queryClient.setQueryData(['tasks', targetUserId], (oldData: Task[] = []) => {
      return oldData.map(task => 
        task.id === updatedTask.id ? updatedTask : task
      );
    });
    
    // Determine what changed and show appropriate notification
    const changes: string[] = [];
    if (oldTask.status !== updatedTask.status) {
      changes.push(`status: ${oldTask.status} → ${updatedTask.status}`);
    }
    if (oldTask.priority !== updatedTask.priority) {
      changes.push(`priority: ${oldTask.priority} → ${updatedTask.priority}`);
    }
    if (oldTask.assigned_to !== updatedTask.assigned_to) {
      changes.push('assignment changed');
    }
    
    // Show notification for significant changes
    if (changes.length > 0 && updatedTask.assigned_to === user?.id) {
      toast.info('Task Updated', {
        description: `${updatedTask.title}: ${changes.join(', ')}`,
        duration: 4000,
      });
    }
    
    // Track update
    setRecentUpdates(prev => [{
      taskId: updatedTask.id,
      field: changes.join(', ') || 'updated',
      oldValue: oldTask,
      newValue: updatedTask,
      updatedBy: 'system', // Would need to track who made the change
      timestamp: updatedTask.updated_at,
    }, ...prev].slice(0, 20));
    
  }, [targetUserId, profile?.role, userId, user?.id, queryClient]);

  const handleTaskDelete = useCallback((payload: Record<string, unknown>) => {
    console.log('📋 Task deleted:', payload);
    
    const deletedTask = payload.old as Task;
    
    // Update tasks cache
    queryClient.setQueryData(['tasks', targetUserId], (oldData: Task[] = []) => {
      return oldData.filter(task => task.id !== deletedTask.id);
    });
    
    // Show notification if it was assigned to current user
    if (deletedTask.assigned_to === user?.id) {
      toast.info('Task Removed', {
        description: deletedTask.title,
        duration: 3000,
      });
    }
    
    // Track update
    setRecentUpdates(prev => [{
      taskId: deletedTask.id,
      field: 'deleted',
      oldValue: deletedTask,
      newValue: null,
      updatedBy: 'system',
      timestamp: new Date().toISOString(),
    }, ...prev].slice(0, 20));
    
  }, [targetUserId, user?.id, queryClient]);

  // ============================================================================
  // SUBSCRIPTION MANAGEMENT
  // ============================================================================

  const setupRealtimeSubscription = useCallback(() => {
    if (!targetUserId || isSubscribed) return;

    try {
      console.log('📋 Setting up real-time task subscription for user:', targetUserId);
      
      const subscriptions: string[] = [];
      
      // Subscribe to task inserts
      const insertId = realtimeSubscriptionManager.subscribe(
        {
          table: 'tasks',
          event: 'INSERT',
          priority: 'high',
        },
        handleTaskInsert
      );
      subscriptions.push(insertId);
      
      // Subscribe to task updates
      const updateId = realtimeSubscriptionManager.subscribe(
        {
          table: 'tasks',
          event: 'UPDATE',
          priority: 'high',
        },
        handleTaskUpdate
      );
      subscriptions.push(updateId);
      
      // Subscribe to task deletes
      const deleteId = realtimeSubscriptionManager.subscribe(
        {
          table: 'tasks',
          event: 'DELETE',
          priority: 'medium',
        },
        handleTaskDelete
      );
      subscriptions.push(deleteId);
      
      setSubscriptionIds(subscriptions);
      setIsSubscribed(true);
      
      console.log('📋 Real-time task subscription established');
      
    } catch (error) {
      console.error('Failed to setup real-time task subscription:', error);
      toast.warning('Real-time task updates temporarily unavailable');
    }
  }, [targetUserId, isSubscribed, handleTaskInsert, handleTaskUpdate, handleTaskDelete]);

  const cleanupSubscription = useCallback(() => {
    if (subscriptionIds.length > 0) {
      subscriptionIds.forEach(id => {
        realtimeSubscriptionManager.unsubscribe(id);
      });
      
      setSubscriptionIds([]);
      setIsSubscribed(false);
      
      console.log('📋 Real-time task subscription cleaned up');
    }
  }, [subscriptionIds]);

  // ============================================================================
  // TASK ACTIONS
  // ============================================================================

  const updateTaskStatus = useCallback(async (taskId: string, status: Task['status']) => {
    try {
      const { error } = await supabase
        .from('tasks')
        .update({ 
          status, 
          updated_at: new Date().toISOString() 
        })
        .eq('id', taskId);

      if (error) {
        console.error('Error updating task status:', error);
        throw error;
      }

      // The real-time subscription will handle the cache update
      
    } catch (error) {
      console.error('Failed to update task status:', error);
      toast.error('Failed to update task status');
    }
  }, []);

  const updateTaskPriority = useCallback(async (taskId: string, priority: Task['priority']) => {
    try {
      const { error } = await supabase
        .from('tasks')
        .update({ 
          priority, 
          updated_at: new Date().toISOString() 
        })
        .eq('id', taskId);

      if (error) {
        console.error('Error updating task priority:', error);
        throw error;
      }

      // The real-time subscription will handle the cache update
      
    } catch (error) {
      console.error('Failed to update task priority:', error);
      toast.error('Failed to update task priority');
    }
  }, []);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    if (targetUserId && !isSubscribed) {
      setupRealtimeSubscription();
    }

    return () => {
      cleanupSubscription();
    };
  }, [targetUserId, setupRealtimeSubscription, cleanupSubscription, isSubscribed]);

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  const taskStats: TaskStats = {
    total: tasks?.length || 0,
    pending: tasks?.filter(t => t.status === 'pending').length || 0,
    inProgress: tasks?.filter(t => t.status === 'in_progress').length || 0,
    completed: tasks?.filter(t => t.status === 'completed').length || 0,
    overdue: tasks?.filter(t => 
      t.due_date && new Date(t.due_date) < new Date() && t.status !== 'completed'
    ).length || 0,
    byPriority: tasks?.reduce((acc, task) => {
      acc[task.priority] = (acc[task.priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {},
  };

  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================

  return {
    // Data
    tasks: tasks || [],
    taskStats,
    recentUpdates,
    
    // Loading states
    isLoading,
    error,
    
    // Real-time status
    isSubscribed,
    
    // Actions
    updateTaskStatus,
    updateTaskPriority,
    refetch,
    
    // Subscription management
    setupRealtimeSubscription,
    cleanupSubscription,
    
    // Utilities
    clearRecentUpdates: () => setRecentUpdates([]),
  };
};
