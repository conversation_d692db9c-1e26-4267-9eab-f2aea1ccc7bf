import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface ReportDataParams {
  period: 'weekly' | 'monthly' | 'quarterly';
  month: number;
  year: number;
}

export const useReportData = ({ period, month, year }: ReportDataParams) => {
  return useQuery({
    queryKey: ['report-data', period, month, year],
    queryFn: async () => {
      try {
        // Calculate date range based on period
        const startDate = new Date(year, month - 1, 1);
        const endDate = new Date(year, month, 0); // Last day of the month

        // Fetch schools count
        const { count: schoolsCount, error: schoolsError } = await supabase
          .from('schools')
          .select('id', { count: 'exact' })
          .eq('status', 'active');

        if (schoolsError) throw schoolsError;

        // Fetch field reports
        const { data: fieldReports, error: reportsError } = await supabase
          .from('field_reports')
          .select(`
            id,
            total_students,
            male_participants,
            female_participants,
            round_table_sessions,
            challenges,
            school_id,
            report_date
          `)
          .gte('report_date', startDate.toISOString().split('T')[0])
          .lte('report_date', endDate.toISOString().split('T')[0]);

        if (reportsError) throw reportsError;

        // Calculate statistics efficiently
        const totalStudents = fieldReports?.reduce((sum, report) =>
          sum + (report.total_students || 0), 0) || 0;

        const totalMaleParticipants = fieldReports?.reduce((sum, report) =>
          sum + (report.male_participants || 0), 0) || 0;

        const totalFemaleParticipants = fieldReports?.reduce((sum, report) =>
          sum + (report.female_participants || 0), 0) || 0;

        const uniqueSchools = new Set(fieldReports?.map(report => report.school_id)).size;

        const roundTableSessions = fieldReports?.reduce((sum, report) =>
          sum + (report.round_table_sessions || 0), 0) || 0;

        // Extract activities from field reports
        const plannedActivities = [
          {
            name: 'Leadership Training Sessions',
            venue: 'Various Schools',
            period: `${period === 'monthly' ? 'Monthly' : period === 'weekly' ? 'Weekly' : 'Quarterly'} Schedule`
          },
          {
            name: 'Teacher Champion Training',
            venue: 'District Centers',
            period: 'Ongoing'
          }
        ];

        const conductedActivities = [
          {
            planned: 'Leadership Training Sessions',
            achieved: `Conducted ${fieldReports?.length || 0} sessions across ${uniqueSchools} schools`,
            progress: 'Completed',
            observation: 'High student engagement and participation'
          },
          {
            planned: 'Student Roundtable Sessions',
            achieved: `Facilitated ${roundTableSessions} roundtable sessions`,
            progress: 'Ongoing',
            observation: 'Students showing improved leadership skills'
          }
        ];

        // Mock student comments (in real implementation, these would come from feedback forms)
        const studentComments = [
          "The iLead lessons have helped me become more confident in speaking up during class discussions.",
          "I now understand the importance of taking responsibility for my actions.",
          "The leadership skills I learned help me in organizing group activities.",
          "I feel more prepared to take on leadership roles in my community."
        ];

        // Extract challenges from field reports (limit processing)
        const challenges = fieldReports?.map(report => report.challenges)
          .filter(Boolean)
          .slice(0, 5) || [
          "Limited internet connectivity in some remote schools",
          "Weather conditions affecting transportation to schools",
          "Some students missing sessions due to household responsibilities"
        ];

        // Generate way forward based on challenges
        const wayForward = [
          "Implement offline data collection methods",
          "Coordinate with local authorities for better road access",
          "Engage with parents to ensure consistent student attendance",
          "Develop mobile-friendly training materials"
        ];

        return {
          schools_onboarded: schoolsCount || 0,
          students_oriented: totalStudents,
          teacher_champions: 156, // This would come from a teacher_champions table
          student_roundtables: roundTableSessions,
          total_male_participants: totalMaleParticipants,
          total_female_participants: totalFemaleParticipants,
          unique_schools_reached: uniqueSchools,
          planned_activities: plannedActivities,
          conducted_activities: conductedActivities,
          student_comments: studentComments,
          challenges: challenges,
          way_forward: wayForward,
          field_reports: fieldReports || []
        };
      } catch (error) {
        console.error('Error fetching report data:', error);
        return {
          schools_onboarded: 0,
          students_oriented: 0,
          teacher_champions: 0,
          student_roundtables: 0,
          total_male_participants: 0,
          total_female_participants: 0,
          unique_schools_reached: 0,
          planned_activities: [],
          conducted_activities: [],
          student_comments: [],
          challenges: ["Unable to load data at this time"],
          way_forward: ["Please try refreshing the page"],
          field_reports: []
        };
      }
    },
    staleTime: 5 * 60 * 1000,
    retry: 2,
    retryDelay: 1000,
  });
};

export const useReportPhotos = ({ month, year }: { month: number; year: number }) => {
  return useQuery({
    queryKey: ['report-photos', month, year],
    queryFn: async () => {
      try {
        const startDate = new Date(year, month - 1, 1);
        const endDate = new Date(year, month, 0);

        const { data: fieldReports, error } = await supabase
          .from('field_reports')
          .select('photos')
          .gte('report_date', startDate.toISOString().split('T')[0])
          .lte('report_date', endDate.toISOString().split('T')[0])
          .not('photos', 'is', null)
          .limit(20);

        if (error) throw error;

        const allPhotos = fieldReports?.flatMap(report => report.photos || []) || [];
        return allPhotos.slice(0, 12);
      } catch (error) {
        console.error('Error fetching photos:', error);
        return [];
      }
    },
    staleTime: 10 * 60 * 1000,
    retry: 1,
    retryDelay: 500,
  });
};
