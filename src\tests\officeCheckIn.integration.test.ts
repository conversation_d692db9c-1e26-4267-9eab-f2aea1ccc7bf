/**
 * Office Check-in Integration Tests
 * Tests the complete office check-in flow including validation and geofencing
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { validateOfficeGeofence, validateOfficeCheckIn } from '@/utils/officeGeofencing';
import { MAIN_OFFICE } from '@/constants/officeLocations';
import {
  MOCK_LOCATIONS,
  TEST_SCENARIOS,
  setupMockGeolocation,
  MockGeolocation,
  createOfficeCheckInTest,
} from '@/utils/testing/officeCheckInTestUtils';

describe('Office Check-in Integration Tests', () => {
  let mockGeolocation: MockGeolocation;

  beforeEach(() => {
    mockGeolocation = setupMockGeolocation();
  });

  afterEach(() => {
    // Reset to default position
    mockGeolocation.setMockPosition(MOCK_LOCATIONS.INSIDE_OFFICE);
  });

  describe('Office Geofence Validation', () => {
    it('should validate user inside office geofence', () => {
      const result = validateOfficeGeofence(MOCK_LOCATIONS.INSIDE_OFFICE, MAIN_OFFICE);
      
      expect(result.isValid).toBe(true);
      expect(result.distance).toBeLessThan(MAIN_OFFICE.geofenceRadius);
      expect(result.errorMessage).toBeUndefined();
    });

    it('should reject user outside office geofence', () => {
      const result = validateOfficeGeofence(MOCK_LOCATIONS.OUTSIDE_OFFICE, MAIN_OFFICE);
      
      expect(result.isValid).toBe(false);
      expect(result.distance).toBeGreaterThan(MAIN_OFFICE.geofenceRadius);
      expect(result.errorMessage).toContain('You are');
      expect(result.errorMessage).toContain('meters from');
    });

    it('should warn about poor GPS accuracy but still allow check-in', () => {
      const result = validateOfficeGeofence(MOCK_LOCATIONS.POOR_ACCURACY, MAIN_OFFICE);
      
      expect(result.isValid).toBe(true);
      expect(result.warningMessage).toContain('GPS accuracy');
    });

    it('should handle boundary cases correctly', () => {
      const result = validateOfficeGeofence(MOCK_LOCATIONS.NEAR_OFFICE_BOUNDARY, MAIN_OFFICE);
      
      expect(result.isValid).toBe(true);
      expect(result.distance).toBeLessThan(MAIN_OFFICE.geofenceRadius);
    });
  });

  describe('Office Check-in Validation Pipeline', () => {
    it('should complete successful office check-in validation', async () => {
      mockGeolocation.setMockPosition(MOCK_LOCATIONS.INSIDE_OFFICE);

      const getCurrentLocation = () => Promise.resolve(MOCK_LOCATIONS.INSIDE_OFFICE);
      
      const result = await validateOfficeCheckIn(MAIN_OFFICE, getCurrentLocation);
      
      expect(result.isValid).toBe(true);
      expect(result.location).toBeDefined();
      expect(result.distance).toBeLessThan(MAIN_OFFICE.geofenceRadius);
      expect(result.errorMessage).toBeUndefined();
    });

    it('should reject check-in when outside geofence', async () => {
      mockGeolocation.setMockPosition(MOCK_LOCATIONS.OUTSIDE_OFFICE);

      const getCurrentLocation = () => Promise.resolve(MOCK_LOCATIONS.OUTSIDE_OFFICE);
      
      const result = await validateOfficeCheckIn(MAIN_OFFICE, getCurrentLocation);
      
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('meters from');
    });

    it('should handle GPS errors gracefully', async () => {
      const getCurrentLocation = () => Promise.reject(new Error('GPS unavailable'));
      
      const result = await validateOfficeCheckIn(MAIN_OFFICE, getCurrentLocation);
      
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('GPS unavailable');
    });

    it('should reject check-in with poor GPS accuracy', async () => {
      const poorAccuracyLocation = {
        ...MOCK_LOCATIONS.INSIDE_OFFICE,
        accuracy: 200, // Very poor accuracy
      };

      const getCurrentLocation = () => Promise.resolve(poorAccuracyLocation);
      
      const result = await validateOfficeCheckIn(MAIN_OFFICE, getCurrentLocation);
      
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toContain('GPS accuracy');
    });
  });

  describe('Test Scenarios', () => {
    Object.entries(TEST_SCENARIOS).forEach(([key, scenario]) => {
      it(`should handle ${scenario.name}`, () => {
        const test = createOfficeCheckInTest(scenario);
        test.setup();

        const result = validateOfficeGeofence(scenario.location, scenario.office);

        expect(result.isValid).toBe(scenario.expectedResult.isValid);
        
        if (scenario.expectedResult.hasWarning) {
          expect(result.warningMessage).toBeDefined();
        }

        test.cleanup();
      });
    });
  });

  describe('Distance Calculations', () => {
    it('should calculate accurate distances', () => {
      // Test with known coordinates
      const office = MAIN_OFFICE;
      const nearbyLocation = {
        latitude: office.coordinates.latitude + 0.001, // ~111 meters north
        longitude: office.coordinates.longitude,
        accuracy: 10,
        timestamp: Date.now(),
      };

      const result = validateOfficeGeofence(nearbyLocation, office);
      
      expect(result.distance).toBeGreaterThan(100);
      expect(result.distance).toBeLessThan(150);
    });

    it('should handle same location (zero distance)', () => {
      const result = validateOfficeGeofence(
        {
          latitude: MAIN_OFFICE.coordinates.latitude,
          longitude: MAIN_OFFICE.coordinates.longitude,
          accuracy: 10,
          timestamp: Date.now(),
        },
        MAIN_OFFICE
      );

      expect(result.distance).toBeLessThan(1); // Should be very close to 0
      expect(result.isValid).toBe(true);
    });
  });

  describe('Edge Cases', () => {
    it('should handle invalid coordinates gracefully', () => {
      const invalidLocation = {
        latitude: NaN,
        longitude: NaN,
        accuracy: 10,
        timestamp: Date.now(),
      };

      expect(() => {
        validateOfficeGeofence(invalidLocation, MAIN_OFFICE);
      }).not.toThrow();
    });

    it('should handle missing accuracy data', () => {
      const locationWithoutAccuracy = {
        latitude: MAIN_OFFICE.coordinates.latitude,
        longitude: MAIN_OFFICE.coordinates.longitude,
        timestamp: Date.now(),
      };

      const result = validateOfficeGeofence(locationWithoutAccuracy, MAIN_OFFICE);
      
      expect(result.isValid).toBe(true);
      expect(result.warningMessage).toBeUndefined();
    });
  });
});
