-- Update Check-in Functions for Office Support
-- Migration 024: Modify field_staff_checkin and field_staff_checkout to support office check-ins

-- Drop existing function to recreate with new signature
DROP FUNCTION IF EXISTS field_staff_checkin(UUID, DECIMAL, DECIMAL, DECIMAL, TEXT, VARCHAR, JSONB, JSONB, BOOLEAN);

-- Updated field staff check-in function with office support
CREATE OR REPLACE FUNCTION field_staff_checkin(
    p_school_id UUID DEFAULT NULL,
    p_office_id UUID DEFAULT NULL,
    p_check_in_type VARCHAR(20) DEFAULT 'school',
    p_latitude DECIMAL(10,8),
    p_longitude DECIMAL(11,8),
    p_accuracy DECIMAL(8,2) DEFAULT NULL,
    p_address TEXT DEFAULT NULL,
    p_verification_method VARCHAR(50) DEFAULT 'gps',
    p_device_info JSONB DEFAULT '{}',
    p_network_info JSONB DEFAULT '{}',
    p_offline_sync BOOLEAN DEFAULT false
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    attendance_id UUID;
    existing_attendance_id UUID;
    distance_meters DECIMAL(8,2);
    target_location POINT;
    geofence_radius INTEGER;
    location_name TEXT;
    check_in_type_enum check_in_type;
BEGIN
    -- Validate user permissions
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
    ) THEN
        RAISE EXCEPTION 'Only authorized staff can check in';
    END IF;

    -- Validate check-in type and location parameters
    IF p_check_in_type NOT IN ('school', 'office') THEN
        RAISE EXCEPTION 'Invalid check-in type. Must be "school" or "office"';
    END IF;

    -- Cast to enum type
    check_in_type_enum := p_check_in_type::check_in_type;

    IF p_check_in_type = 'school' THEN
        IF p_school_id IS NULL THEN
            RAISE EXCEPTION 'School ID is required for school check-ins';
        END IF;
        IF p_office_id IS NOT NULL THEN
            RAISE EXCEPTION 'Office ID must be NULL for school check-ins';
        END IF;
    ELSIF p_check_in_type = 'office' THEN
        IF p_office_id IS NULL THEN
            RAISE EXCEPTION 'Office ID is required for office check-ins';
        END IF;
        IF p_school_id IS NOT NULL THEN
            RAISE EXCEPTION 'School ID must be NULL for office check-ins';
        END IF;
    END IF;

    -- Check if staff already checked in today at this location
    IF p_check_in_type = 'school' THEN
        SELECT id INTO existing_attendance_id
        FROM field_staff_attendance
        WHERE staff_id = auth.uid()
        AND school_id = p_school_id
        AND attendance_date = CURRENT_DATE
        AND status = 'active';
        
        IF existing_attendance_id IS NOT NULL THEN
            RAISE EXCEPTION 'Already checked in at this school today';
        END IF;
    ELSE
        SELECT id INTO existing_attendance_id
        FROM field_staff_attendance
        WHERE staff_id = auth.uid()
        AND office_id = p_office_id
        AND attendance_date = CURRENT_DATE
        AND status = 'active';
        
        IF existing_attendance_id IS NOT NULL THEN
            RAISE EXCEPTION 'Already checked in at this office today';
        END IF;
    END IF;

    -- Get target location and validate distance
    IF p_check_in_type = 'school' THEN
        SELECT location_coordinates, name INTO target_location, location_name
        FROM schools WHERE id = p_school_id;
        
        geofence_radius := 100; -- 100 meters for schools
    ELSE
        SELECT location_coordinates, geofence_radius_meters, name 
        INTO target_location, geofence_radius, location_name
        FROM office_locations 
        WHERE id = p_office_id AND is_active = true;
        
        IF target_location IS NULL THEN
            RAISE EXCEPTION 'Office location not found or inactive';
        END IF;
    END IF;

    -- Calculate distance from target location
    IF target_location IS NOT NULL THEN
        distance_meters := ST_Distance(
            ST_GeogFromText('POINT(' || p_longitude || ' ' || p_latitude || ')'),
            ST_GeogFromText('POINT(' || ST_X(target_location) || ' ' || ST_Y(target_location) || ')')
        );
        
        -- Validate geofencing for office check-ins (stricter validation)
        IF p_check_in_type = 'office' AND distance_meters > geofence_radius THEN
            RAISE EXCEPTION 'You are %.0f meters from the office. You must be within %.0f meters to check in.', 
                distance_meters, geofence_radius;
        END IF;
    END IF;

    -- Insert attendance record
    INSERT INTO field_staff_attendance (
        staff_id, school_id, office_id, check_in_type, attendance_date, check_in_time,
        check_in_location, check_in_accuracy, check_in_address,
        distance_from_school, location_verified, verification_method,
        device_info, network_info, offline_sync, status
    )
    VALUES (
        auth.uid(), p_school_id, p_office_id, check_in_type_enum, CURRENT_DATE, NOW(),
        POINT(p_longitude, p_latitude), p_accuracy, p_address,
        distance_meters, 
        (distance_meters IS NULL OR distance_meters <= COALESCE(geofence_radius, 100)),
        p_verification_method, p_device_info, p_network_info, p_offline_sync, 'active'
    )
    RETURNING id INTO attendance_id;

    -- Log activity
    INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description)
    VALUES (
        'task_created'::activity_type,
        auth.uid(),
        'task'::entity_type,
        attendance_id,
        CASE 
            WHEN p_check_in_type = 'school' THEN 'Checked in at school: ' || location_name
            ELSE 'Checked in at office: ' || location_name
        END
    );

    RETURN attendance_id;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION field_staff_checkin TO authenticated;

-- Update field staff check-out function to support office check-outs
CREATE OR REPLACE FUNCTION field_staff_checkout(
    p_attendance_id UUID,
    p_latitude DECIMAL(10,8) DEFAULT NULL,
    p_longitude DECIMAL(11,8) DEFAULT NULL,
    p_accuracy DECIMAL(8,2) DEFAULT NULL,
    p_address TEXT DEFAULT NULL,
    p_notes TEXT DEFAULT NULL,
    -- Field report data (only for school visits)
    p_activity_type field_activity_type DEFAULT NULL,
    p_round_table_sessions INTEGER DEFAULT 0,
    p_total_students INTEGER DEFAULT 0,
    p_students_per_session INTEGER DEFAULT 8,
    p_activities_conducted TEXT[] DEFAULT '{}',
    p_topics_covered TEXT[] DEFAULT '{}',
    p_materials_used TEXT[] DEFAULT '{}',
    p_challenges_encountered TEXT DEFAULT NULL,
    p_wins_achieved TEXT DEFAULT NULL,
    p_general_observations TEXT DEFAULT NULL,
    p_lessons_learned TEXT DEFAULT NULL,
    p_follow_up_required BOOLEAN DEFAULT false,
    p_follow_up_actions TEXT DEFAULT NULL,
    p_next_visit_planned DATE DEFAULT NULL,
    p_photos TEXT[] DEFAULT '{}',
    p_offline_sync BOOLEAN DEFAULT false
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    report_id UUID;
    attendance_record RECORD;
    duration_minutes INTEGER;
BEGIN
    -- Get attendance record and validate ownership
    SELECT * INTO attendance_record
    FROM field_staff_attendance
    WHERE id = p_attendance_id AND staff_id = auth.uid() AND status = 'active';

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Attendance record not found or already checked out';
    END IF;

    -- Calculate duration
    duration_minutes := EXTRACT(EPOCH FROM (NOW() - attendance_record.check_in_time)) / 60;

    -- Update attendance record with check-out information
    UPDATE field_staff_attendance
    SET
        check_out_time = NOW(),
        check_out_location = CASE
            WHEN p_latitude IS NOT NULL AND p_longitude IS NOT NULL
            THEN POINT(p_longitude, p_latitude)
            ELSE NULL
        END,
        check_out_accuracy = p_accuracy,
        check_out_address = p_address,
        total_duration_minutes = duration_minutes,
        status = 'completed',
        notes = p_notes,
        offline_sync = COALESCE(p_offline_sync, attendance_record.offline_sync),
        updated_at = NOW()
    WHERE id = p_attendance_id;

    -- Create field report only for school visits
    IF attendance_record.check_in_type = 'school' AND p_activity_type IS NOT NULL THEN
        INSERT INTO field_reports (
            attendance_id, staff_id, school_id, report_date, activity_type,
            round_table_sessions_count, total_students_attended, students_per_session,
            activities_conducted, topics_covered, materials_used,
            challenges_encountered, wins_achieved, general_observations, lessons_learned,
            follow_up_required, follow_up_actions, next_visit_planned,
            report_location, location_verified, photos, offline_created, report_status
        )
        VALUES (
            p_attendance_id, auth.uid(), attendance_record.school_id, CURRENT_DATE, p_activity_type,
            p_round_table_sessions, p_total_students, p_students_per_session,
            p_activities_conducted, p_topics_covered, p_materials_used,
            p_challenges_encountered, p_wins_achieved, p_general_observations, p_lessons_learned,
            p_follow_up_required, p_follow_up_actions, p_next_visit_planned,
            CASE
                WHEN p_latitude IS NOT NULL AND p_longitude IS NOT NULL
                THEN POINT(p_longitude, p_latitude)
                ELSE NULL
            END,
            false, p_photos, p_offline_sync, 'submitted'
        )
        RETURNING id INTO report_id;
    END IF;

    -- Log activity
    INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description)
    VALUES (
        'task_completed'::activity_type,
        auth.uid(),
        'task'::entity_type,
        p_attendance_id,
        CASE
            WHEN attendance_record.check_in_type = 'school' THEN
                'Checked out from school visit (Duration: ' || duration_minutes || ' minutes)'
            ELSE
                'Checked out from office (Duration: ' || duration_minutes || ' minutes)'
        END
    );

    RETURN COALESCE(report_id, p_attendance_id);
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION field_staff_checkout TO authenticated;
