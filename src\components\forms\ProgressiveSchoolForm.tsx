import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { ProgressiveForm, FormStep } from '@/components/ui/progressive-form';
import { FormDisclosure, FieldGroup, ConditionalField } from '@/components/ui/form-disclosure';
import { 
  School, 
  MapPin, 
  Users, 
  Mail, 
  Phone,
  GraduationCap,
  FileText,
  CheckCircle
} from 'lucide-react';
import { useFormValidation } from '@/hooks/useFormValidation';
import { schoolSchemas } from '@/utils/validation/schemas';

interface SchoolFormData {
  // Basic Information
  name: string;
  school_type: 'primary' | 'secondary' | 'tertiary' | 'vocational';
  school_code?: string;
  date_joined?: string;
  
  // Location
  district: string;
  sub_county?: string;
  address?: string;
  
  // Contact Information
  contact_phone?: string;
  contact_email?: string;
  
  // Staff Information
  head_teacher_name?: string;
  head_teacher_email?: string;
  deputy_teacher_name?: string;
  deputy_teacher_email?: string;
  champion_teacher_name: string;
  champion_teacher_email?: string;
  assistant_champion_teacher_name?: string;
  
  // Statistics
  student_count?: number;
  teacher_count?: number;
  champion_teacher_count: number;
  
  // Additional Information
  notes?: string;
  has_library?: boolean;
  has_computer_lab?: boolean;
  has_internet?: boolean;
}

interface ProgressiveSchoolFormProps {
  onSubmit: (data: SchoolFormData) => Promise<void>;
  onCancel?: () => void;
  initialData?: Partial<SchoolFormData>;
  title?: string;
}

// Step 1: Basic Information
const BasicInformationStep: React.FC<{ data: Record<string, unknown>; updateData: (data: Record<string, unknown>) => void }> = ({
  data,
  updateData
}) => {
  const handleChange = (field: string, value: unknown) => {
    updateData({ [field]: value });
  };

  return (
    <FormDisclosure
      sections={[
        {
          id: 'basic',
          title: 'School Details',
          description: 'Basic information about the school',
          icon: School,
          required: true,
          children: (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="name">School Name *</Label>
                <Input
                  id="name"
                  value={data.name || ''}
                  onChange={(e) => handleChange('name', e.target.value)}
                  placeholder="Enter school name"
                  className="input-mobile"
                />
              </div>
              
              <div>
                <Label htmlFor="school_type">School Type *</Label>
                <Select value={data.school_type || ''} onValueChange={(value) => handleChange('school_type', value)}>
                  <SelectTrigger className="input-mobile">
                    <SelectValue placeholder="Select school type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="primary">Primary</SelectItem>
                    <SelectItem value="secondary">Secondary</SelectItem>
                    <SelectItem value="tertiary">Tertiary</SelectItem>
                    <SelectItem value="vocational">Vocational</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="school_code">School Code</Label>
                <Input
                  id="school_code"
                  value={data.school_code || ''}
                  onChange={(e) => handleChange('school_code', e.target.value)}
                  placeholder="Optional school code"
                  className="input-mobile"
                />
              </div>
              
              <div>
                <Label htmlFor="date_joined">Date Joined Program</Label>
                <Input
                  id="date_joined"
                  type="date"
                  value={data.date_joined || ''}
                  onChange={(e) => handleChange('date_joined', e.target.value)}
                  className="input-mobile"
                />
              </div>
            </div>
          )
        }
      ]}
      data={data}
      mobileOptimized={true}
    />
  );
};

// Step 2: Location Information
const LocationStep: React.FC<{ data: Record<string, unknown>; updateData: (data: Record<string, unknown>) => void }> = ({
  data,
  updateData
}) => {
  const handleChange = (field: string, value: unknown) => {
    updateData({ [field]: value });
  };

  return (
    <FieldGroup
      title="Location Information"
      description="Where is the school located?"
      icon={MapPin}
      required={true}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="district">District *</Label>
          <Input
            id="district"
            value={data.district || ''}
            onChange={(e) => handleChange('district', e.target.value)}
            placeholder="Enter district"
            className="input-mobile"
          />
        </div>
        
        <div>
          <Label htmlFor="sub_county">Sub County</Label>
          <Input
            id="sub_county"
            value={data.sub_county || ''}
            onChange={(e) => handleChange('sub_county', e.target.value)}
            placeholder="Enter sub county"
            className="input-mobile"
          />
        </div>
        
        <div className="md:col-span-2">
          <Label htmlFor="address">Physical Address</Label>
          <Textarea
            id="address"
            value={data.address || ''}
            onChange={(e) => handleChange('address', e.target.value)}
            placeholder="Enter physical address or directions"
            rows={3}
          />
        </div>
      </div>
    </FieldGroup>
  );
};

// Step 3: Contact & Staff Information
const ContactStaffStep: React.FC<{ data: Record<string, unknown>; updateData: (data: Record<string, unknown>) => void }> = ({
  data,
  updateData
}) => {
  const handleChange = (field: string, value: unknown) => {
    updateData({ [field]: value });
  };

  return (
    <FormDisclosure
      sections={[
        {
          id: 'contact',
          title: 'Contact Information',
          description: 'How can we reach the school?',
          icon: Phone,
          children: (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="contact_phone">School Phone</Label>
                <Input
                  id="contact_phone"
                  value={data.contact_phone || ''}
                  onChange={(e) => handleChange('contact_phone', e.target.value)}
                  placeholder="+256 XXX XXX XXX"
                  className="input-mobile"
                />
              </div>
              
              <div>
                <Label htmlFor="contact_email">School Email</Label>
                <Input
                  id="contact_email"
                  type="email"
                  value={data.contact_email || ''}
                  onChange={(e) => handleChange('contact_email', e.target.value)}
                  placeholder="<EMAIL>"
                  className="input-mobile"
                />
              </div>
            </div>
          )
        },
        {
          id: 'staff',
          title: 'Staff Information',
          description: 'Key staff members at the school',
          icon: Users,
          required: true,
          children: (
            <div className="space-y-6">
              <FieldGroup
                title="Head Teacher"
                variant="bordered"
                collapsible={true}
                defaultOpen={false}
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="head_teacher_name">Name</Label>
                    <Input
                      id="head_teacher_name"
                      value={data.head_teacher_name || ''}
                      onChange={(e) => handleChange('head_teacher_name', e.target.value)}
                      placeholder="Head teacher name"
                      className="input-mobile"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="head_teacher_email">Email</Label>
                    <Input
                      id="head_teacher_email"
                      type="email"
                      value={data.head_teacher_email || ''}
                      onChange={(e) => handleChange('head_teacher_email', e.target.value)}
                      placeholder="<EMAIL>"
                      className="input-mobile"
                    />
                  </div>
                </div>
              </FieldGroup>

              <FieldGroup
                title="Champion Teacher"
                description="At least one champion teacher is required"
                variant="bordered"
                required={true}
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="champion_teacher_name">Name *</Label>
                    <Input
                      id="champion_teacher_name"
                      value={data.champion_teacher_name || ''}
                      onChange={(e) => handleChange('champion_teacher_name', e.target.value)}
                      placeholder="Champion teacher name"
                      className="input-mobile"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="champion_teacher_email">Email</Label>
                    <Input
                      id="champion_teacher_email"
                      type="email"
                      value={data.champion_teacher_email || ''}
                      onChange={(e) => handleChange('champion_teacher_email', e.target.value)}
                      placeholder="<EMAIL>"
                      className="input-mobile"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="champion_teacher_count">Number of Champion Teachers *</Label>
                    <Input
                      id="champion_teacher_count"
                      type="number"
                      min="1"
                      value={data.champion_teacher_count || 1}
                      onChange={(e) => handleChange('champion_teacher_count', parseInt(e.target.value))}
                      className="input-mobile"
                    />
                  </div>
                </div>
              </FieldGroup>

              <ConditionalField
                condition={(data) => data.champion_teacher_count > 1}
                data={data}
                animateEntry={true}
              >
                <FieldGroup
                  title="Assistant Champion Teacher"
                  variant="bordered"
                  collapsible={true}
                  defaultOpen={false}
                >
                  <div>
                    <Label htmlFor="assistant_champion_teacher_name">Name</Label>
                    <Input
                      id="assistant_champion_teacher_name"
                      value={data.assistant_champion_teacher_name || ''}
                      onChange={(e) => handleChange('assistant_champion_teacher_name', e.target.value)}
                      placeholder="Assistant champion teacher name"
                      className="input-mobile"
                    />
                  </div>
                </FieldGroup>
              </ConditionalField>
            </div>
          )
        }
      ]}
      data={data}
      allowMultipleOpen={true}
      mobileOptimized={true}
    />
  );
};

// Step 4: Statistics & Additional Information
const StatisticsStep: React.FC<{ data: Record<string, unknown>; updateData: (data: Record<string, unknown>) => void }> = ({
  data,
  updateData
}) => {
  const handleChange = (field: string, value: unknown) => {
    updateData({ [field]: value });
  };

  return (
    <FormDisclosure
      sections={[
        {
          id: 'stats',
          title: 'School Statistics',
          description: 'Student and teacher numbers',
          icon: GraduationCap,
          children: (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="student_count">Number of Students</Label>
                <Input
                  id="student_count"
                  type="number"
                  min="0"
                  value={data.student_count || ''}
                  onChange={(e) => handleChange('student_count', parseInt(e.target.value) || 0)}
                  placeholder="Total students"
                  className="input-mobile"
                />
              </div>
              
              <div>
                <Label htmlFor="teacher_count">Number of Teachers</Label>
                <Input
                  id="teacher_count"
                  type="number"
                  min="0"
                  value={data.teacher_count || ''}
                  onChange={(e) => handleChange('teacher_count', parseInt(e.target.value) || 0)}
                  placeholder="Total teachers"
                  className="input-mobile"
                />
              </div>
            </div>
          )
        },
        {
          id: 'facilities',
          title: 'School Facilities',
          description: 'Available facilities and resources',
          icon: School,
          collapsible: true,
          defaultOpen: false,
          children: (
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="has_library"
                  checked={data.has_library || false}
                  onCheckedChange={(checked) => handleChange('has_library', checked)}
                />
                <Label htmlFor="has_library">Has Library</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="has_computer_lab"
                  checked={data.has_computer_lab || false}
                  onCheckedChange={(checked) => handleChange('has_computer_lab', checked)}
                />
                <Label htmlFor="has_computer_lab">Has Computer Lab</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="has_internet"
                  checked={data.has_internet || false}
                  onCheckedChange={(checked) => handleChange('has_internet', checked)}
                />
                <Label htmlFor="has_internet">Has Internet Access</Label>
              </div>
            </div>
          )
        },
        {
          id: 'notes',
          title: 'Additional Notes',
          description: 'Any additional information about the school',
          icon: FileText,
          collapsible: true,
          defaultOpen: false,
          children: (
            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={data.notes || ''}
                onChange={(e) => handleChange('notes', e.target.value)}
                placeholder="Any additional information..."
                rows={4}
              />
            </div>
          )
        }
      ]}
      data={data}
      allowMultipleOpen={true}
      showProgress={true}
      mobileOptimized={true}
    />
  );
};

export const ProgressiveSchoolForm: React.FC<ProgressiveSchoolFormProps> = ({
  onSubmit,
  onCancel,
  initialData = {},
  title = "School Registration"
}) => {
  const steps: FormStep[] = [
    {
      id: 'basic',
      title: 'Basic Information',
      description: 'School name, type, and identification',
      icon: School,
      required: true,
      component: BasicInformationStep,
      validation: () => {
        return !!(initialData.name && initialData.school_type);
      }
    },
    {
      id: 'location',
      title: 'Location',
      description: 'Where the school is located',
      icon: MapPin,
      required: true,
      component: LocationStep,
      validation: () => {
        return !!initialData.district;
      }
    },
    {
      id: 'contact-staff',
      title: 'Contact & Staff',
      description: 'Contact information and key staff',
      icon: Users,
      required: true,
      component: ContactStaffStep,
      validation: () => {
        return !!(initialData.champion_teacher_name && initialData.champion_teacher_count);
      }
    },
    {
      id: 'statistics',
      title: 'Statistics & Facilities',
      description: 'Student numbers and school facilities',
      icon: GraduationCap,
      required: false,
      component: StatisticsStep
    }
  ];

  return (
    <ProgressiveForm
      steps={steps}
      onComplete={onSubmit}
      onCancel={onCancel}
      initialData={initialData}
      title={title}
      description="Register a new school in the iLead program"
      showProgress={true}
      showStepNumbers={true}
      allowSkipOptional={true}
      showStepPreview={true}
      mobileOptimized={true}
      className="max-w-4xl mx-auto"
    />
  );
};
