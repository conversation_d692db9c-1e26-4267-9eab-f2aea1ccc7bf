import React, { use<PERSON>emo, useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader } from '@/components/ui/dialog';
import { BookOpen, School, Users, TrendingUp, MapPin, LogIn, Loader2 } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar } from 'recharts';
import { usePublicImpactSummary, usePublicTrends, usePublicBooksBreakdown, usePublicCoverageSummary } from '@/hooks/public/usePublicImpact';
import Login from '@/components/Login';
import { useAuth } from '@/hooks/useAuth';

const StatCard: React.FC<{ icon: React.ElementType; label: string; value: string | number; loading?: boolean }> = ({ icon: Icon, label, value, loading }) => (
  <Card>
    <CardContent className="p-4 flex items-center gap-4">
      <div className="p-3 rounded-md bg-ilead-green/10 text-ilead-green"><Icon className="h-6 w-6" /></div>
      <div>
        <div className="text-2xl font-bold">
          {loading ? <Loader2 className="h-6 w-6 animate-spin" /> : value}
        </div>
        <div className="text-gray-600 text-sm">{label}</div>
      </div>
    </CardContent>
  </Card>
);

const COLORS = ['#8B5CF6', '#F59E0B', '#EF4444', '#10B981', '#3B82F6'];

const PublicLandingPage: React.FC = () => {
  const [loginOpen, setLoginOpen] = useState(false);
  const { signIn } = useAuth();
  const { data: summary, isLoading: summaryLoading } = usePublicImpactSummary();
  const { data: trends = [], isLoading: trendsLoading } = usePublicTrends('weekly', 12);
  const { data: books = [], isLoading: booksLoading } = usePublicBooksBreakdown();
  const { data: coverage = [], isLoading: coverageLoading } = usePublicCoverageSummary();

  const roundedSummary = useMemo(() => {
    if (!summary) return null;
    const round10 = (n: number) => Math.round((n || 0) / 10) * 10;
    return {
      students: round10(summary.students),
      schools: summary.schools,
      sessions: round10(summary.sessions),
      books: round10(summary.books),
      districts: summary.districts,
    };
  }, [summary]);

  const handleLogin = async (email: string, password: string) => {
    const result = await signIn(email, password);
    if (!result.error) {
      setLoginOpen(false);
    }
    return result;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-orange-50">
      {/* Header */}
      <div className="max-w-7xl mx-auto px-4 py-6 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <img src="/ilead-logo.svg" alt="iLead Uganda Logo" className="h-20" />
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('https://ileaduganda.org', '_blank')}
          >
            Visit Our Website
          </Button>
          <Button onClick={() => setLoginOpen(true)} className="bg-ilead-green hover:bg-ilead-dark-green">
            <LogIn className="h-4 w-4 mr-2" /> Login
          </Button>
        </div>
      </div>

      {/* Hero */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Empowering the Next Generation through Leadership</h1>
          <p className="text-gray-600 mt-2 max-w-2xl">
            A snapshot of our reach and achievements. This page shows aggregated, anonymized impact data. For detailed operational insights, please contact our team.
          </p>
        </div>
      </div>

      {/* Stats */}
      <div className="max-w-7xl mx-auto px-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <StatCard icon={Users} label="Students Impacted" value={roundedSummary?.students ?? '—'} loading={summaryLoading} />
        <StatCard icon={School} label="Schools Served" value={roundedSummary?.schools ?? '—'} loading={summaryLoading} />
        <StatCard icon={TrendingUp} label="Sessions Delivered" value={roundedSummary?.sessions ?? '—'} loading={summaryLoading} />
        <StatCard icon={BookOpen} label="Books Distributed" value={roundedSummary?.books ?? '—'} loading={summaryLoading} />
        <StatCard icon={MapPin} label="Districts Covered" value={roundedSummary?.districts ?? '—'} loading={summaryLoading} />
      </div>

      {/* Charts */}
      <div className="max-w-7xl mx-auto px-4 mt-8 grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Trends */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-ilead-green" />
              12-Week Impact Trend
            </CardTitle>
          </CardHeader>
          <CardContent>
            {trendsLoading ? (
              <div className="h-64 flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-ilead-green" />
              </div>
            ) : trends.length > 0 ? (
              <ResponsiveContainer width="100%" height={250}>
                <LineChart data={trends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period_start" tick={{ fontSize: 12 }} />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip />
                  <Line type="monotone" dataKey="students" stroke="#8B5CF6" strokeWidth={2} name="Students" />
                  <Line type="monotone" dataKey="sessions" stroke="#F59E0B" strokeWidth={2} name="Sessions" />
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-64 flex items-center justify-center text-gray-500">
                <p>No trend data available</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Books */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5 text-ilead-green" />
              Books Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent>
            {booksLoading ? (
              <div className="h-64 flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-ilead-green" />
              </div>
            ) : books.length > 0 ? (
              <ResponsiveContainer width="100%" height={250}>
                <PieChart>
                  <Pie
                    data={books}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="total"
                    label={({ title, total }) => `${title}: ${total}`}
                  >
                    {books.map((_, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-64 flex items-center justify-center text-gray-500">
                <p>No books data available</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Coverage */}
      <div className="max-w-7xl mx-auto px-4 mt-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-ilead-green" />
              Coverage Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            {coverageLoading ? (
              <div className="h-64 flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-ilead-green" />
              </div>
            ) : coverage.length > 0 ? (
              <ResponsiveContainer width="100%" height={250}>
                <BarChart data={coverage}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" tick={{ fontSize: 12 }} />
                  <YAxis dataKey="district" type="category" tick={{ fontSize: 12 }} width={80} />
                  <Tooltip />
                  <Bar dataKey="schools_count" fill="#8B5CF6" name="Schools" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-64 flex items-center justify-center text-gray-500">
                <p>No coverage data available</p>
              </div>
            )}
            <p className="text-sm text-gray-600 mt-4">
              Top districts by presence; small counts are grouped into "Other" to protect privacy.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Footer */}
      <div className="max-w-7xl mx-auto px-4 py-10 text-center text-gray-600 text-sm">
        Aggregated public data • No personal or location-identifiable information displayed • © {new Date().getFullYear()} iLead Uganda
      </div>

      {/* Login Modal */}
      <Dialog open={loginOpen} onOpenChange={setLoginOpen}>
        <DialogContent className="sm:max-w-lg bg-white rounded-lg shadow-xl border-0 p-0 overflow-hidden">
          <div className="bg-gradient-to-br from-purple-50 to-orange-50 p-6">
            <DialogHeader className="text-center space-y-3">
              <img src="/ilead-logo.svg" alt="iLEAD Uganda Logo" className="h-20 mx-auto mb-4" />
              <p className="text-gray-600 text-center">Field Management & Monitoring Platform</p>
            </DialogHeader>
          </div>
          <div className="bg-white p-6">
            <Login onLogin={handleLogin} variant="modal" />
            <p className="text-center text-sm text-gray-600 mt-4">
              © {new Date().getFullYear()} iLEAD Uganda. All rights reserved.
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PublicLandingPage;
