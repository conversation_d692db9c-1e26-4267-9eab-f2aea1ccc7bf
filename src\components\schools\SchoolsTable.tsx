
import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Eye, Edit, Trash2, UserX } from 'lucide-react';
import { School } from '@/types/school';
import { Database } from '@/integrations/supabase/types';

// Type definitions
type Profile = Database['public']['Functions']['get_user_profile']['Returns'][0];

interface SchoolsTableProps {
  schools: School[];
  currentUser: Profile;
  canManageSchools: boolean;
  onViewDetails: (school: School) => void;
  onEditSchool?: (school: School) => void;
  onDeleteSchool?: (school: School) => void;
  onToggleActive?: (school: School) => void;
}

const SchoolsTable = ({ 
  schools, 
  currentUser, 
  canManageSchools, 
  onViewDetails,
  onEditSchool,
  onDeleteSchool,
  onToggleActive
}: SchoolsTableProps) => {
  const isAdmin = currentUser?.role === 'admin';

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Students</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {schools.map((school) => (
            <TableRow key={school.id}>
              <TableCell className="font-medium">{school.name}</TableCell>
              <TableCell>
                <Badge variant={
                  school.school_type === 'primary' ? 'default' :
                  school.school_type === 'secondary' ? 'secondary' :
                  'outline'
                }>
                  {school.school_type}
                </Badge>
              </TableCell>
              <TableCell>{school.student_count ?? 'N/A'}</TableCell>
              <TableCell>{[school.district, school.sub_county].filter(Boolean).join(', ') || 'N/A'}</TableCell>
              <TableCell>
                <Badge variant={
                  school.registration_status === 'active' ? 'default' :
                  school.registration_status === 'pending' ? 'secondary' :
                  'destructive'
                }>
                  {school.registration_status === 'active' ? 'Active' :
                   school.registration_status === 'inactive' ? 'Inactive' :
                   'Inactive'}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onViewDetails(school)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  {canManageSchools && (
                    <>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEditSchool?.(school)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onToggleActive?.(school)}
                        title={school.registration_status === 'inactive' ? 'Mark as Active' : 'Mark as Inactive'}
                      >
                        <UserX className="h-4 w-4" />
                      </Button>
                      {isAdmin && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDeleteSchool?.(school)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default SchoolsTable;
