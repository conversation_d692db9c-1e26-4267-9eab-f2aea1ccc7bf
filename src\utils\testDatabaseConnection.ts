/**
 * Database Connection Test Utility
 * Test the database connection manager functionality
 */

import { getDbConnectionManager, getOptimizedClient, getDatabaseStats } from './databaseConnectionManager';

// ============================================================================
// TEST FUNCTIONS
// ============================================================================

/**
 * Test basic database connection
 */
export const testDatabaseConnection = async (): Promise<boolean> => {
  try {
    console.log('🧪 Testing database connection...');
    
    const client = getOptimizedClient('read');
    const { data, error } = await client.from('profiles').select('id').limit(1);
    
    if (error) {
      console.error('❌ Database connection test failed:', error);
      return false;
    }
    
    console.log('✅ Database connection test passed');
    return true;
  } catch (error) {
    console.error('❌ Database connection test error:', error);
    return false;
  }
};

/**
 * Test connection manager initialization
 */
export const testConnectionManager = (): boolean => {
  try {
    console.log('🧪 Testing connection manager...');
    
    const manager = getDbConnectionManager();
    const stats = getDatabaseStats();
    
    console.log('📊 Connection stats:', stats);
    console.log('✅ Connection manager test passed');
    return true;
  } catch (error) {
    console.error('❌ Connection manager test failed:', error);
    return false;
  }
};

/**
 * Test different operation types
 */
export const testOperationTypes = async (): Promise<boolean> => {
  try {
    console.log('🧪 Testing operation types...');
    
    // Test read operation
    const readClient = getOptimizedClient('read');
    const { data: readData, error: readError } = await readClient
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (readError) {
      console.error('❌ Read operation failed:', readError);
      return false;
    }
    
    // Test write operation client (we won't actually write)
    const writeClient = getOptimizedClient('write');
    if (!writeClient) {
      console.error('❌ Write client not available');
      return false;
    }
    
    // Test realtime operation client
    const realtimeClient = getOptimizedClient('realtime');
    if (!realtimeClient) {
      console.error('❌ Realtime client not available');
      return false;
    }
    
    console.log('✅ Operation types test passed');
    return true;
  } catch (error) {
    console.error('❌ Operation types test failed:', error);
    return false;
  }
};

/**
 * Run all database connection tests
 */
export const runAllDatabaseTests = async (): Promise<{
  connectionTest: boolean;
  managerTest: boolean;
  operationTest: boolean;
  allPassed: boolean;
}> => {
  console.log('🚀 Running all database connection tests...');
  
  const connectionTest = await testDatabaseConnection();
  const managerTest = testConnectionManager();
  const operationTest = await testOperationTypes();
  
  const allPassed = connectionTest && managerTest && operationTest;
  
  console.log('📋 Test Results:');
  console.log(`  Connection Test: ${connectionTest ? '✅' : '❌'}`);
  console.log(`  Manager Test: ${managerTest ? '✅' : '❌'}`);
  console.log(`  Operation Test: ${operationTest ? '✅' : '❌'}`);
  console.log(`  Overall: ${allPassed ? '✅ All tests passed' : '❌ Some tests failed'}`);
  
  return {
    connectionTest,
    managerTest,
    operationTest,
    allPassed,
  };
};

// ============================================================================
// DEVELOPMENT HELPER
// ============================================================================

/**
 * Log connection manager status (for development)
 */
export const logConnectionStatus = () => {
  try {
    const stats = getDatabaseStats();
    const manager = getDbConnectionManager();
    
    console.log('🔗 Database Connection Status:');
    console.log('  Stats:', stats);
    console.log('  Manager initialized:', !!manager);
    console.log('  Active connections:', stats.activeConnections);
    console.log('  Total connections:', stats.totalConnections);
    console.log('  Average response time:', `${stats.averageResponseTime.toFixed(2)}ms`);
    console.log('  Failed connections:', stats.failedConnections);
    console.log('  Last health check:', new Date(stats.lastHealthCheck).toLocaleTimeString());
  } catch (error) {
    console.error('❌ Failed to get connection status:', error);
  }
};

// Auto-run tests in development mode
if (process.env.NODE_ENV === 'development') {
  // Run tests after a short delay to ensure everything is initialized
  setTimeout(() => {
    runAllDatabaseTests().catch(console.error);
  }, 1000);
}
