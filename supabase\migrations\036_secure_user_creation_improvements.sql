-- Migration: Secure User Creation Improvements
-- Description: Add constraints and improvements for secure user creation via Edge Functions
-- Date: 2025-01-14

-- Add unique constraint on email (case-insensitive) to prevent duplicates
CREATE UNIQUE INDEX IF NOT EXISTS profiles_email_unique_idx 
ON profiles (LOWER(email));

-- Add check constraint for valid roles
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'valid_roles' AND table_name = 'profiles'
    ) THEN
        ALTER TABLE profiles 
        ADD CONSTRAINT valid_roles 
        CHECK (role IN ('admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'));
    END IF;
END $$;

-- Ensure email field is not null and properly formatted
ALTER TABLE profiles 
ALTER COLUMN email SET NOT NULL;

-- Add index on role for faster permission checks
CREATE INDEX IF NOT EXISTS profiles_role_idx ON profiles (role);

-- Add index on is_active for faster filtering
CREATE INDEX IF NOT EXISTS profiles_is_active_idx ON profiles (is_active);

-- Create audit table for user creation tracking
CREATE TABLE IF NOT EXISTS user_creation_audit (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_by UUID REFERENCES profiles(id),
    target_email TEXT NOT NULL,
    target_role TEXT NOT NULL,
    action TEXT NOT NULL, -- 'create', 'bulk_create'
    status TEXT NOT NULL, -- 'success', 'failed'
    error_details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for audit table
ALTER TABLE user_creation_audit ENABLE ROW LEVEL SECURITY;

-- Admins can view all audit logs
CREATE POLICY "Admins can view all audit logs" ON user_creation_audit
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() AND p.role = 'admin'
        )
    );

-- Program officers can view their own audit logs
CREATE POLICY "Program officers can view own audit logs" ON user_creation_audit
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() 
            AND p.role = 'program_officer'
            AND p.id = user_creation_audit.created_by
        )
    );

-- Service role can insert audit logs
CREATE POLICY "Service role can insert audit logs" ON user_creation_audit
    FOR INSERT
    TO service_role
    WITH CHECK (true);

-- Create function to log user creation attempts
CREATE OR REPLACE FUNCTION log_user_creation_attempt(
    p_created_by UUID,
    p_target_email TEXT,
    p_target_role TEXT,
    p_action TEXT,
    p_status TEXT,
    p_error_details JSONB DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    audit_id UUID;
BEGIN
    INSERT INTO user_creation_audit (
        created_by,
        target_email,
        target_role,
        action,
        status,
        error_details,
        ip_address,
        user_agent
    ) VALUES (
        p_created_by,
        p_target_email,
        p_target_role,
        p_action,
        p_status,
        p_error_details,
        p_ip_address,
        p_user_agent
    ) RETURNING id INTO audit_id;
    
    RETURN audit_id;
END;
$$;

-- Create function to clean up orphaned profiles (profiles without auth users)
CREATE OR REPLACE FUNCTION cleanup_orphaned_profiles()
RETURNS TABLE (
    deleted_profile_id UUID,
    deleted_email TEXT,
    deleted_name TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- This function would need to be called manually or via a scheduled job
    -- It identifies profiles that might not have corresponding auth users
    -- For now, it just returns a structure for future implementation
    
    RETURN QUERY
    SELECT 
        p.id,
        p.email,
        p.name
    FROM profiles p
    WHERE p.created_at < NOW() - INTERVAL '1 hour'
    AND p.email LIKE '%@example.com' -- Only clean up test accounts
    LIMIT 0; -- Don't actually delete anything yet
END;
$$;

-- Create function to validate email format
CREATE OR REPLACE FUNCTION is_valid_email(email TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
    RETURN email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$';
END;
$$;

-- Add email format validation trigger
CREATE OR REPLACE FUNCTION validate_profile_email()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    IF NEW.email IS NOT NULL AND NOT is_valid_email(NEW.email) THEN
        RAISE EXCEPTION 'Invalid email format: %', NEW.email;
    END IF;
    
    -- Ensure email is lowercase
    NEW.email = LOWER(NEW.email);
    
    RETURN NEW;
END;
$$;

-- Create trigger for email validation
DROP TRIGGER IF EXISTS validate_profile_email_trigger ON profiles;
CREATE TRIGGER validate_profile_email_trigger
    BEFORE INSERT OR UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION validate_profile_email();

-- Update existing emails to lowercase (if any exist)
UPDATE profiles SET email = LOWER(email) WHERE email != LOWER(email);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO service_role;
GRANT ALL ON TABLE user_creation_audit TO service_role;
GRANT ALL ON TABLE profiles TO service_role;
GRANT EXECUTE ON FUNCTION log_user_creation_attempt TO service_role;
GRANT EXECUTE ON FUNCTION cleanup_orphaned_profiles TO service_role;
GRANT EXECUTE ON FUNCTION is_valid_email TO service_role;

-- Add comment for documentation
COMMENT ON MIGRATION '036_secure_user_creation_improvements' IS 
'Implements security improvements for user creation system:
- Unique email constraint (case-insensitive)
- Role validation constraints
- Audit logging system
- Email format validation
- Cleanup functions for maintenance
- Proper RLS policies for audit data';

-- Create view for user creation statistics (admins only)
CREATE OR REPLACE VIEW user_creation_stats AS
SELECT 
    DATE_TRUNC('day', created_at) as date,
    action,
    status,
    target_role,
    COUNT(*) as count
FROM user_creation_audit
GROUP BY DATE_TRUNC('day', created_at), action, status, target_role
ORDER BY date DESC;

-- RLS policy for the stats view
CREATE POLICY "Admins can view user creation stats" ON user_creation_stats
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() AND p.role = 'admin'
        )
    );
