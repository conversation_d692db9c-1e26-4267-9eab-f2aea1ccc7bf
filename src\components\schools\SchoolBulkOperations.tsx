
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import {
  MoreHorizontal,
  Upload,
  Trash2,
  CheckSquare,
  Square
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { School } from '@/types/school';

interface SchoolBulkOperationsProps {
  schools: School[];
  selectedSchools: string[];
  onSelectAll: (selected: boolean) => void;
  onSelectSchool: (schoolId: string, selected: boolean) => void;
  onBulkStatusUpdate: (schoolIds: string[], status: School['registration_status']) => void;
  canManageSchools: boolean;
  isAdmin: boolean;
}

const SchoolBulkOperations = ({
  schools,
  selectedSchools,
  onSelectAll,
  onSelectSchool,
  onBulkStatusUpdate,
  canManageSchools,
  isAdmin
}: SchoolBulkOperationsProps) => {
  const [bulkStatus, setBulkStatus] = useState<School['registration_status']>('active');

  const allSelected = schools.length > 0 && selectedSchools.length === schools.length;
  const someSelected = selectedSchools.length > 0 && selectedSchools.length < schools.length;

  const handleBulkStatusUpdate = () => {
    if (selectedSchools.length > 0 && bulkStatus) {
      onBulkStatusUpdate(selectedSchools, bulkStatus);
    }
  };

  return (
    <div className="flex items-center justify-between p-4 bg-white border-b">
      <div className="flex items-center gap-4">
        {/* Select All Checkbox */}
        <div className="flex items-center space-x-2">
          <Checkbox
            checked={allSelected}
            ref={(ref) => {
              if (ref) ref.indeterminate = someSelected;
            }}
            onCheckedChange={(checked) => onSelectAll(!!checked)}
          />
          <span className="text-sm text-gray-600">
            {selectedSchools.length > 0 
              ? `${selectedSchools.length} selected`
              : 'Select all'
            }
          </span>
        </div>

        {/* Bulk Actions */}
        {selectedSchools.length > 0 && canManageSchools && (
          <div className="flex items-center gap-2">
            <Select value={bulkStatus} onValueChange={(value) => setBulkStatus(value as School['registration_status'])}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
            <Button 
              onClick={handleBulkStatusUpdate}
              size="sm"
              variant="outline"
            >
              Update Status
            </Button>
          </div>
        )}
      </div>

      <div className="flex items-center gap-2">
        {/* More Actions for Admins */}
        {isAdmin && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem>
                <Upload className="h-4 w-4 mr-2" />
                Import Schools
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {selectedSchools.length > 0 && (
                <DropdownMenuItem className="text-red-600">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Selected ({selectedSchools.length})
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  );
};

export default SchoolBulkOperations;
