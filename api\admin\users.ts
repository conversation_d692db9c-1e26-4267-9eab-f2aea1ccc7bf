// Backend API for user management operations
import { createClient } from '@supabase/supabase-js';
import type { Database } from '../../src/types/database.types';

// Server-side Supabase client with service role
const supabaseAdmin = createClient<Database>(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export interface CreateUserRequest {
  email: string;
  name: string;
  role: string;
  division_id?: string;
  phone?: string;
  created_by: string; // ID of the user creating this account
}

export interface CreateUserResponse {
  success: boolean;
  user?: {
    id: string;
    email: string;
    name: string;
    role: string;
    tempPassword: string;
  };
  error?: string;
}

export interface BulkCreateRequest {
  users: CreateUserRequest[];
  created_by: string;
}

export interface BulkCreateResponse {
  success: boolean;
  results: Array<{
    email: string;
    success: boolean;
    user?: CreateUserResponse['user'];
    error?: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

// Validate user creation permissions
async function validatePermissions(createdBy: string, targetRole: string): Promise<boolean> {
  const { data: creator } = await supabaseAdmin
    .from('profiles')
    .select('role')
    .eq('id', createdBy)
    .single();

  if (!creator) return false;

  // Admins can create any role
  if (creator.role === 'admin') return true;

  // Program officers can only create field-level roles
  if (creator.role === 'program_officer') {
    const allowedRoles = ['staff', 'field_staff', 'partner', 'accountant', 'social_media_manager'];
    return allowedRoles.includes(targetRole);
  }

  return false;
}

// Generate secure password
function generateSecurePassword(): string {
  const crypto = require('crypto');
  const length = 12;
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, charset.length);
    password += charset[randomIndex];
  }
  
  return password;
}

// Check if user already exists
async function checkUserExists(email: string): Promise<boolean> {
  const { data } = await supabaseAdmin
    .from('profiles')
    .select('id')
    .eq('email', email.toLowerCase())
    .single();
    
  return !!data;
}

// Create single user with transaction-like behavior
export async function createUser(request: CreateUserRequest): Promise<CreateUserResponse> {
  try {
    // Validate permissions
    const hasPermission = await validatePermissions(request.created_by, request.role);
    if (!hasPermission) {
      return {
        success: false,
        error: 'Insufficient permissions to create user with this role'
      };
    }

    // Check for duplicates
    const exists = await checkUserExists(request.email);
    if (exists) {
      return {
        success: false,
        error: `User with email ${request.email} already exists`
      };
    }

    // Generate secure password
    const tempPassword = generateSecurePassword();

    // Create auth user
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: request.email.toLowerCase(),
      password: tempPassword,
      email_confirm: true,
    });

    if (authError || !authData.user) {
      return {
        success: false,
        error: `Failed to create auth user: ${authError?.message || 'Unknown error'}`
      };
    }

    // Create profile
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .insert({
        id: authData.user.id,
        email: request.email.toLowerCase(),
        name: request.name,
        role: request.role,
        division_id: request.division_id || null,
        phone: request.phone || null,
        country: 'Uganda',
        is_active: true,
      });

    if (profileError) {
      // Cleanup auth user if profile creation fails
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id);
      return {
        success: false,
        error: `Failed to create profile: ${profileError.message}`
      };
    }

    return {
      success: true,
      user: {
        id: authData.user.id,
        email: request.email,
        name: request.name,
        role: request.role,
        tempPassword
      }
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

// Bulk create users
export async function bulkCreateUsers(request: BulkCreateRequest): Promise<BulkCreateResponse> {
  const results: BulkCreateResponse['results'] = [];
  
  for (const userData of request.users) {
    const result = await createUser({
      ...userData,
      created_by: request.created_by
    });
    
    results.push({
      email: userData.email,
      success: result.success,
      user: result.user,
      error: result.error
    });
  }
  
  const successful = results.filter(r => r.success).length;
  const failed = results.length - successful;
  
  return {
    success: failed === 0,
    results,
    summary: {
      total: results.length,
      successful,
      failed
    }
  };
}
