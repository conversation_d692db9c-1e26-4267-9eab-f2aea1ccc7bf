import React, { useState, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Upload, 
  Download, 
  FileText, 
  AlertCircle, 
  CheckCircle, 
  X,
  Plus,
  Trash2
} from 'lucide-react';
import { useStaffManagement } from '@/hooks/useStaffManagement';
import { useAuth } from '@/hooks/useAuth';
import { Database } from '@/integrations/supabase/types';

type UserRole = Database['public']['Enums']['user_role'];

interface UserData {
  email: string;
  name: string;
  role: UserRole;
  division_id?: string;
  phone?: string;
}

interface ValidationError {
  row: number;
  field: string;
  message: string;
}

interface BulkUserCreationProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const BulkUserCreation: React.FC<BulkUserCreationProps> = ({ open, onOpenChange }) => {
  const { bulkCreateUsers, isBulkCreating } = useStaffManagement();
  const { profile } = useAuth();
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [parsedUsers, setParsedUsers] = useState<UserData[]>([]);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [manualUsers, setManualUsers] = useState<UserData[]>([
    { email: '', name: '', role: 'staff', division_id: '', phone: '' }
  ]);

  // Get available roles based on current user's role
  const getAvailableRoles = () => {
    const currentUserRole = profile?.role;

    if (currentUserRole === 'admin') {
      // Admins can create any role
      return ['staff', 'field_staff', 'partner', 'accountant', 'social_media_manager', 'program_officer', 'admin'];
    } else if (currentUserRole === 'program_officer') {
      // Program officers can only create field-level roles
      return ['staff', 'field_staff', 'partner', 'accountant', 'social_media_manager'];
    } else {
      // Other roles shouldn't be able to create users, but just in case
      return ['staff', 'field_staff'];
    }
  };

  const csvTemplate = `email,name,role,division_id,phone
<EMAIL>,John Doe,field_staff,,+************
<EMAIL>,Jane Smith,program_officer,division-uuid-here,+************`;

  const downloadTemplate = () => {
    const blob = new Blob([csvTemplate], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'staff-import-template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateUser = useCallback((user: UserData, index: number): ValidationError[] => {
    const errors: ValidationError[] = [];

    if (!user.email.trim()) {
      errors.push({ row: index + 1, field: 'email', message: 'Email is required' });
    } else if (!validateEmail(user.email)) {
      errors.push({ row: index + 1, field: 'email', message: 'Invalid email format' });
    }

    if (!user.name.trim()) {
      errors.push({ row: index + 1, field: 'name', message: 'Name is required' });
    }

    const availableRoles = getAvailableRoles();
    if (!availableRoles.includes(user.role)) {
      errors.push({ row: index + 1, field: 'role', message: `Invalid role. Available roles: ${availableRoles.join(', ')}` });
    }

    return errors;
  }, []);

  const parseCSV = useCallback((csvText: string): UserData[] => {
    const lines = csvText.trim().split('\n');
    const headers = lines[0].split(',').map(h => h.trim());

    const users: UserData[] = [];
    const errors: ValidationError[] = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      const user: UserData = {
        email: values[headers.indexOf('email')] || '',
        name: values[headers.indexOf('name')] || '',
        role: (values[headers.indexOf('role')] as UserRole) || 'staff',
        division_id: values[headers.indexOf('division_id')] || undefined,
        phone: values[headers.indexOf('phone')] || undefined,
      };

      const userErrors = validateUser(user, i - 1);
      errors.push(...userErrors);
      users.push(user);
    }

    setValidationErrors(errors);
    return users;
  }, [validateUser]);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setCsvFile(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      const csvText = e.target?.result as string;
      const users = parseCSV(csvText);
      setParsedUsers(users);
    };
    reader.readAsText(file);
  };

  const addManualUser = () => {
    setManualUsers([...manualUsers, { email: '', name: '', role: 'staff', division_id: '', phone: '' }]);
  };

  const removeManualUser = (index: number) => {
    setManualUsers(manualUsers.filter((_, i) => i !== index));
  };

  const updateManualUser = (index: number, field: keyof UserData, value: string) => {
    const updated = [...manualUsers];
    updated[index] = { ...updated[index], [field]: value };
    setManualUsers(updated);
  };

  const validateManualUsers = (): ValidationError[] => {
    const errors: ValidationError[] = [];
    manualUsers.forEach((user, index) => {
      const userErrors = validateUser(user, index);
      errors.push(...userErrors);
    });
    return errors;
  };

  const handleSubmit = (users: UserData[]) => {
    const errors = users === parsedUsers ? validationErrors : validateManualUsers();
    
    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }

    const validUsers = users.filter(user => user.email.trim() && user.name.trim());
    
    if (validUsers.length === 0) {
      return;
    }

    bulkCreateUsers(validUsers);
    onOpenChange(false);
    
    // Reset state
    setCsvFile(null);
    setParsedUsers([]);
    setValidationErrors([]);
    setManualUsers([{ email: '', name: '', role: 'staff', division_id: '', phone: '' }]);
  };

  const hasValidUsers = (users: UserData[]) => {
    return users.some(user => user.email.trim() && user.name.trim());
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Bulk User Creation</DialogTitle>
          <DialogDescription>
            Create multiple user accounts at once using CSV upload or manual entry
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="csv" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="csv">CSV Upload</TabsTrigger>
            <TabsTrigger value="manual">Manual Entry</TabsTrigger>
          </TabsList>

          <TabsContent value="csv" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  CSV Template
                </CardTitle>
                <CardDescription>
                  Download the template to ensure your CSV file has the correct format
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button onClick={downloadTemplate} variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Download Template
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Upload CSV File</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="csv-upload">Select CSV File</Label>
                  <Input
                    id="csv-upload"
                    type="file"
                    accept=".csv"
                    onChange={handleFileUpload}
                    className="mt-1"
                  />
                </div>

                {csvFile && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      File uploaded: {csvFile.name} ({parsedUsers.length} users found)
                    </AlertDescription>
                  </Alert>
                )}

                {validationErrors.length > 0 && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Found {validationErrors.length} validation errors. Please fix them before proceeding.
                    </AlertDescription>
                  </Alert>
                )}

                {parsedUsers.length > 0 && (
                  <div className="space-y-4">
                    <h4 className="font-medium">Preview ({parsedUsers.length} users)</h4>
                    <div className="max-h-60 overflow-y-auto border rounded">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Email</TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Role</TableHead>
                            <TableHead>Phone</TableHead>
                            <TableHead>Status</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {parsedUsers.map((user, index) => {
                            const userErrors = validationErrors.filter(e => e.row === index + 1);
                            return (
                              <TableRow key={index}>
                                <TableCell className={userErrors.some(e => e.field === 'email') ? 'text-red-600' : ''}>
                                  {user.email}
                                </TableCell>
                                <TableCell className={userErrors.some(e => e.field === 'name') ? 'text-red-600' : ''}>
                                  {user.name}
                                </TableCell>
                                <TableCell>
                                  <Badge variant="outline">{user.role}</Badge>
                                </TableCell>
                                <TableCell>{user.phone || '-'}</TableCell>
                                <TableCell>
                                  {userErrors.length > 0 ? (
                                    <Badge variant="destructive">Error</Badge>
                                  ) : (
                                    <Badge variant="default">Valid</Badge>
                                  )}
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </div>

                    {validationErrors.length === 0 && (
                      <Button 
                        onClick={() => handleSubmit(parsedUsers)}
                        disabled={isBulkCreating || !hasValidUsers(parsedUsers)}
                        className="w-full"
                      >
                        {isBulkCreating ? 'Creating Users...' : `Create ${parsedUsers.length} Users`}
                      </Button>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="manual" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Manual User Entry</CardTitle>
                <CardDescription>
                  Add users one by one using the form below
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {manualUsers.map((user, index) => (
                  <div key={index} className="p-4 border rounded-lg space-y-4">
                    <div className="flex justify-between items-center">
                      <h5 className="font-medium">User {index + 1}</h5>
                      {manualUsers.length > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeManualUser(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>Email *</Label>
                        <Input
                          value={user.email}
                          onChange={(e) => updateManualUser(index, 'email', e.target.value)}
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div>
                        <Label>Name *</Label>
                        <Input
                          value={user.name}
                          onChange={(e) => updateManualUser(index, 'name', e.target.value)}
                          placeholder="Full Name"
                        />
                      </div>
                      <div>
                        <Label>Role</Label>
                        <select
                          value={user.role}
                          onChange={(e) => updateManualUser(index, 'role', e.target.value)}
                          className="w-full p-2 border rounded"
                        >
                          {getAvailableRoles().map((role) => (
                            <option key={role} value={role}>
                              {role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <Label>Phone</Label>
                        <Input
                          value={user.phone || ''}
                          onChange={(e) => updateManualUser(index, 'phone', e.target.value)}
                          placeholder="+************"
                        />
                      </div>
                    </div>
                  </div>
                ))}

                <Button onClick={addManualUser} variant="outline" className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Another User
                </Button>

                <Button 
                  onClick={() => handleSubmit(manualUsers)}
                  disabled={isBulkCreating || !hasValidUsers(manualUsers)}
                  className="w-full"
                >
                  {isBulkCreating ? 'Creating Users...' : `Create ${manualUsers.filter(u => u.email && u.name).length} Users`}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default BulkUserCreation;
