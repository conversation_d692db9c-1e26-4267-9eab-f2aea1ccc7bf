import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

import {
  GraduationCap,
  Plus,
  Users,
  FileText
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import AssessmentForm from './AssessmentForm';

import { StudentLearningOutcome } from '@/types/impact';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import { MetricCard } from '../shared';

// Clear any old localStorage data on component load
if (typeof window !== 'undefined') {
  localStorage.removeItem('ilead_assessments');
}

interface StudentOutcomesModuleProps {
  schoolId?: string | null;
  dateRange: {
    start: Date;
    end: Date;
  };
  canViewAllData: boolean;
}

const StudentOutcomesModule: React.FC<StudentOutcomesModuleProps> = ({
  schoolId,
  dateRange,
  canViewAllData
}) => {
  const [showAssessmentForm, setShowAssessmentForm] = useState(false);

  // Fetch schools for name mapping
  const { data: schools, isLoading: isLoadingSchools, error: schoolsError } = useQuery({
    queryKey: ['schools'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('schools')
        .select('id, name');

      if (error) throw error;
      return data || [];
    }
  });

  // Fetch student learning outcomes from database
  const { data: learningOutcomes, isLoading } = useQuery<StudentLearningOutcome[]>({
    queryKey: ['student-learning-outcomes', schoolId, dateRange, schools],
    queryFn: async () => {
      if (!schools || schools.length === 0) {
        return [];
      }

      // Fetch assessments from database
      let query = supabase
        .from('ilead_assessments')
        .select('*');

      // Apply school filter if specified
      if (schoolId) {
        query = query.eq('school_id', schoolId);
      }

      // Apply date range filter if specified
      if (dateRange?.from) {
        query = query.gte('assessment_date', dateRange.from.toISOString().split('T')[0]);
      }
      if (dateRange?.to) {
        query = query.lte('assessment_date', dateRange.to.toISOString().split('T')[0]);
      }

      const { data: assessments, error } = await query;

      if (error) {
        console.error('Error fetching assessments:', error);
        throw error;
      }

      console.log('Fetched assessments from database:', assessments);

      if (!assessments || assessments.length === 0) {
        console.log('No assessments found in database');
        return [];
      }

      // Create school name lookup
      const schoolLookup = new Map(schools.map(s => [s.id, s.name]));

      // Convert assessments to learning outcomes format
      const outcomes: StudentLearningOutcome[] = [];
      const schoolGroups = new Map<string, unknown[]>();

      // Group assessments by school
      assessments.forEach((assessment: Record<string, unknown>) => {
        if (!schoolGroups.has(assessment.school_id)) {
          schoolGroups.set(assessment.school_id, []);
        }
        schoolGroups.get(assessment.school_id)!.push(assessment);
      });

      // Convert to learning outcomes format
      for (const [schoolId, assessments] of schoolGroups) {
        // Calculate average leadership score
        const totalScores = assessments.map((a: Record<string, unknown>) => {
          const scores = [
            a.consider_myself_leader, a.strive_to_finish_started, a.understand_importance_decisions,
            a.take_full_responsibility, a.understand_leader_values, a.strive_push_limits,
            a.value_people_around, a.good_example_to_others, a.responsible_with_homework,
            a.responsible_with_housework, a.confident_in_myself
          ];
          return scores.reduce((sum: number, score: number) => sum + score, 0) / scores.length;
        });

        const avgScore = totalScores.reduce((sum: number, score: number) => sum + score, 0) / totalScores.length;

        outcomes.push({
          school_id: schoolId,
          school_name: schoolLookup.get(schoolId) || 'Unknown School',
          subject: 'leadership_skills' as const,
          total_students: assessments.length,
          avg_pre_score: avgScore,
          avg_post_score: null,
          avg_improvement: null,
          students_improved: null,
          improvement_rate: null
        });
      }

      return outcomes;
    },
    enabled: !!schools
  });

  // Calculate summary statistics
  const summaryStats = React.useMemo(() => {
    if (!learningOutcomes?.length) {
      return {
        totalStudents: 0,
        schoolsInvolved: 0
      };
    }

    const totalStudents = learningOutcomes.reduce((sum, outcome) => sum + outcome.total_students, 0);
    const schoolsInvolved = new Set(learningOutcomes.map(outcome => outcome.school_id)).size;

    return {
      totalStudents,
      schoolsInvolved
    };
  }, [learningOutcomes]);



  // Access control is now handled by AdminOnlyWrapper

  return (
    <PageLayout>
      <PageHeader
        title="Student Leadership Development"
        description="Track leadership skills development and program effectiveness"
        icon={GraduationCap}
        actions={[
          {
            label: 'New Assessment',
            onClick: () => setShowAssessmentForm(true),
            icon: Plus,
          }
        ]}
      />

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <MetricCard
          title="Total Students Assessed"
          value={summaryStats.totalStudents.toLocaleString()}
          icon={Users}
          color="blue"
        />
        <MetricCard
          title="Schools Involved"
          value={summaryStats.schoolsInvolved}
          icon={GraduationCap}
          color="orange"
        />
      </div>



      {/* Learning Outcomes */}
      <Card>
        <CardHeader>
          <CardTitle>Learning Outcomes</CardTitle>
          <CardDescription>
            Leadership development assessment results and participant progress
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-ilead-green mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading outcomes data...</p>
            </div>
          ) : learningOutcomes?.length ? (
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">School</th>
                    <th className="text-left p-2">Assessment Type</th>
                    <th className="text-right p-2">Participants</th>
                    <th className="text-right p-2">Avg Leadership Score</th>
                    <th className="text-right p-2">Assessment Count</th>
                  </tr>
                </thead>
                <tbody>
                  {learningOutcomes?.length ? learningOutcomes.map((outcome, index) => (
                    <tr key={index} className="border-b hover:bg-gray-50">
                      <td className="p-2 font-medium">{outcome.school_name}</td>
                      <td className="p-2 capitalize">{outcome.subject}</td>
                      <td className="p-2 text-right">{outcome.total_students}</td>
                      <td className="p-2 text-right">{outcome.avg_pre_score?.toFixed(1) || 'N/A'}</td>
                      <td className="p-2 text-right">{outcome.total_students}</td>
                    </tr>
                  )) : (
                    <tr>
                      <td colSpan={5} className="p-8 text-center text-gray-500">
                        No iLead assessment data available yet. Create your first assessment to see results here.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No iLead assessment data available</p>
              <Button
                onClick={() => setShowAssessmentForm(true)}
                className="mt-4"
                variant="outline"
              >
                Create First iLead Assessment
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Assessment Form Modal */}
      {showAssessmentForm && (
        <AssessmentForm
          onClose={() => setShowAssessmentForm(false)}
          schoolId={schoolId}
        />
      )}
    </PageLayout>
  );
};

export default StudentOutcomesModule;
