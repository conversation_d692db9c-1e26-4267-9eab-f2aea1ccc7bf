/**
 * Office Location Constants and Configuration
 * Centralized configuration for office check-in functionality
 */

export interface OfficeLocation {
  id: string;
  name: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  geofenceRadius: number; // meters
  address?: string;
  description?: string;
  isActive: boolean;
}

/**
 * Main office location configuration
 * Coordinates: 0.32581475334630144, 32.57564046659227
 */
export const MAIN_OFFICE: OfficeLocation = {
  id: 'main-office',
  name: 'Main Office',
  coordinates: {
    latitude: 0.32581475334630144,
    longitude: 32.57564046659227,
  },
  geofenceRadius: 1000, // 1km radius
  address: 'iLead Main Office',
  description: 'Primary office location for administrative staff and field staff office days',
  isActive: true,
};

/**
 * All office locations (expandable for future offices)
 */
export const OFFICE_LOCATIONS: Record<string, OfficeLocation> = {
  MAIN_OFFICE,
};

/**
 * Default geofence radius for office check-ins (in meters)
 */
export const DEFAULT_OFFICE_GEOFENCE_RADIUS = 1000;

/**
 * Check-in type enumeration
 */
export const CHECK_IN_TYPES = {
  SCHOOL: 'school',
  OFFICE: 'office',
} as const;

export type CheckInType = typeof CHECK_IN_TYPES[keyof typeof CHECK_IN_TYPES];

/**
 * Office check-in configuration
 */
export const OFFICE_CHECKIN_CONFIG = {
  // Maximum distance allowed for office check-in (meters)
  MAX_DISTANCE: 1000,
  
  // GPS accuracy threshold for office check-ins (meters)
  GPS_ACCURACY_THRESHOLD: 100,
  
  // Timeout for GPS acquisition (milliseconds)
  GPS_TIMEOUT: 15000,
  
  // Enable high accuracy GPS for office check-ins
  HIGH_ACCURACY_GPS: true,
  
  // Maximum age of cached GPS position (milliseconds)
  GPS_MAX_AGE: 60000, // 1 minute
} as const;

/**
 * Error messages for office check-in validation
 */
export const OFFICE_CHECKIN_ERRORS = {
  LOCATION_REQUIRED: 'Location access is required for office check-in',
  GPS_UNAVAILABLE: 'GPS is not available on this device',
  GPS_TIMEOUT: 'GPS location request timed out. Please try again.',
  GPS_DENIED: 'Location permission denied. Please enable location access.',
  OUTSIDE_GEOFENCE: 'You are outside the office area. Please move closer to the office.',
  POOR_GPS_ACCURACY: 'GPS accuracy is too low. Please move to an area with better signal.',
  ALREADY_CHECKED_IN: 'You are already checked in at this office today',
  OFFICE_NOT_FOUND: 'Office location not found or inactive',
  INVALID_OFFICE: 'Invalid office location selected',
} as const;

/**
 * Success messages for office check-in
 */
export const OFFICE_CHECKIN_SUCCESS = {
  CHECKED_IN: 'Successfully checked in at office!',
  CHECKED_OUT: 'Successfully checked out from office!',
} as const;
