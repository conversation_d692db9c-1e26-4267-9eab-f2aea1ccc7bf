-- Add new user roles to the user_role enum
-- This migration adds staff, partner, accountant, and social_media_manager roles

-- Add new values to the user_role enum
ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'staff';
ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'partner';
ALTER TYPE user_role ADD VALUE IF NOT <PERSON><PERSON><PERSON><PERSON> 'accountant';
ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'social_media_manager';

-- Update any existing RLS policies that might need to include the new roles
-- Note: Most policies will be updated in subsequent migrations for specific access patterns

-- Update the default role assignment function to handle new roles
CREATE OR REPLACE FUNCTION get_default_user_role()
RETURNS user_role
LANGUAGE plpgsql
AS $$
BEGIN
    -- Default new users to 'staff' role instead of 'field_staff'
    -- This provides a more generic starting point
    RETURN 'staff'::user_role;
END;
$$;

-- Add comment for documentation
COMMENT ON TYPE user_role IS 'User roles: admin (highest), program_officer, field_staff, staff, partner, accountant, social_media_manager. New roles (staff, partner, accountant, social_media_manager) inherit field_staff permissions with accountant having additional book management access.';
