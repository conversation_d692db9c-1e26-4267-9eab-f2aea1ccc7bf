/**
 * Photo URL Validation Hook
 * Validates photo URLs and tracks broken/accessible images
 */

import { useState, useEffect, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';

// ============================================================================
// TYPES
// ============================================================================

export interface PhotoValidationResult {
  url: string;
  isValid: boolean;
  isLoading: boolean;
  error?: string;
  lastChecked?: number;
}

export interface PhotoValidationSummary {
  total: number;
  valid: number;
  broken: number;
  loading: number;
  validPhotos: string[];
  brokenPhotos: string[];
  loadingPhotos: string[];
}

// ============================================================================
// PHOTO VALIDATION HOOK
// ============================================================================

export const usePhotoValidation = (photoUrls: string[], options: {
  enabled?: boolean;
  retryAttempts?: number;
  timeout?: number;
  cacheTime?: number;
} = {}) => {
  const {
    enabled = true,
    retryAttempts = 2,
    timeout = 10000,
    cacheTime = 5 * 60 * 1000, // 5 minutes
  } = options;

  const [validationResults, setValidationResults] = useState<Map<string, PhotoValidationResult>>(new Map());

  // Validate a single photo URL
  const validatePhotoUrl = useCallback(async (url: string): Promise<PhotoValidationResult> => {
    const startTime = Date.now();
    
    try {
      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      // Use HEAD request to check if image exists without downloading
      const response = await fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache',
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Check if it's actually an image
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.startsWith('image/')) {
        throw new Error(`Invalid content type: ${contentType}`);
      }

      return {
        url,
        isValid: true,
        isLoading: false,
        lastChecked: Date.now(),
      };

    } catch (error) {
      let errorMessage = 'Unknown error';
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = 'Request timeout';
        } else {
          errorMessage = error.message;
        }
      }

      return {
        url,
        isValid: false,
        isLoading: false,
        error: errorMessage,
        lastChecked: Date.now(),
      };
    }
  }, [timeout]);

  // Batch validate multiple URLs
  const validatePhotos = useCallback(async (urls: string[]): Promise<PhotoValidationResult[]> => {
    if (!urls.length) return [];

    // Set loading state for all URLs
    setValidationResults(prev => {
      const newResults = new Map(prev);
      urls.forEach(url => {
        newResults.set(url, {
          url,
          isValid: false,
          isLoading: true,
        });
      });
      return newResults;
    });

    // Validate URLs in batches to avoid overwhelming the server
    const batchSize = 5;
    const results: PhotoValidationResult[] = [];

    for (let i = 0; i < urls.length; i += batchSize) {
      const batch = urls.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (url) => {
        let attempts = 0;
        let lastError: Error | null = null;

        while (attempts < retryAttempts) {
          try {
            const result = await validatePhotoUrl(url);
            if (result.isValid || attempts === retryAttempts - 1) {
              return result;
            }
            lastError = new Error(result.error || 'Validation failed');
          } catch (error) {
            lastError = error instanceof Error ? error : new Error('Unknown error');
          }
          
          attempts++;
          
          // Wait before retry (exponential backoff)
          if (attempts < retryAttempts) {
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempts) * 1000));
          }
        }

        // Return failed result after all retries
        return {
          url,
          isValid: false,
          isLoading: false,
          error: lastError?.message || 'Validation failed after retries',
          lastChecked: Date.now(),
        };
      });

      const batchResults = await Promise.allSettled(batchPromises);
      
      // Process batch results
      batchResults.forEach((result, index) => {
        const url = batch[index];
        let validationResult: PhotoValidationResult;

        if (result.status === 'fulfilled') {
          validationResult = result.value;
        } else {
          validationResult = {
            url,
            isValid: false,
            isLoading: false,
            error: result.reason?.message || 'Promise rejected',
            lastChecked: Date.now(),
          };
        }

        results.push(validationResult);

        // Update state immediately for this URL
        setValidationResults(prev => {
          const newResults = new Map(prev);
          newResults.set(url, validationResult);
          return newResults;
        });
      });

      // Small delay between batches to be respectful to the server
      if (i + batchSize < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return results;
  }, [validatePhotoUrl, retryAttempts]);

  // Query for photo validation
  const {
    data: validationData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['photo-validation', photoUrls.sort().join(',')],
    queryFn: () => validatePhotos(photoUrls),
    enabled: enabled && photoUrls.length > 0,
    staleTime: cacheTime,
    gcTime: cacheTime * 2,
    retry: false, // We handle retries manually
    refetchOnWindowFocus: false,
  });

  // Compute summary statistics
  const summary: PhotoValidationSummary = {
    total: photoUrls.length,
    valid: 0,
    broken: 0,
    loading: 0,
    validPhotos: [],
    brokenPhotos: [],
    loadingPhotos: [],
  };

  photoUrls.forEach(url => {
    const result = validationResults.get(url);
    
    if (!result || result.isLoading) {
      summary.loading++;
      summary.loadingPhotos.push(url);
    } else if (result.isValid) {
      summary.valid++;
      summary.validPhotos.push(url);
    } else {
      summary.broken++;
      summary.brokenPhotos.push(url);
    }
  });

  // Manual validation function for individual URLs
  const validateSinglePhoto = useCallback(async (url: string) => {
    const result = await validatePhotoUrl(url);
    
    setValidationResults(prev => {
      const newResults = new Map(prev);
      newResults.set(url, result);
      return newResults;
    });
    
    return result;
  }, [validatePhotoUrl]);

  // Get validation result for a specific URL
  const getValidationResult = useCallback((url: string): PhotoValidationResult | undefined => {
    return validationResults.get(url);
  }, [validationResults]);

  // Check if a URL is valid (with fallback for unchecked URLs)
  const isPhotoValid = useCallback((url: string): boolean => {
    const result = validationResults.get(url);
    return result ? result.isValid : true; // Assume valid if not checked yet
  }, [validationResults]);

  // Retry validation for failed photos
  const retryFailedPhotos = useCallback(() => {
    const failedUrls = Array.from(validationResults.entries())
      .filter(([_, result]) => !result.isValid && !result.isLoading)
      .map(([url]) => url);
    
    if (failedUrls.length > 0) {
      return validatePhotos(failedUrls);
    }
    
    return Promise.resolve([]);
  }, [validationResults, validatePhotos]);

  // Clear validation cache
  const clearValidationCache = useCallback(() => {
    setValidationResults(new Map());
  }, []);

  return {
    // Data
    validationResults: Array.from(validationResults.values()),
    summary,
    
    // Loading states
    isLoading: isLoading || summary.loading > 0,
    error,
    
    // Actions
    validateSinglePhoto,
    retryFailedPhotos,
    refetch,
    clearValidationCache,
    
    // Utilities
    getValidationResult,
    isPhotoValid,
  };
};

// ============================================================================
// SIMPLE PHOTO VALIDATION HOOK
// ============================================================================

/**
 * Simplified hook for basic photo validation
 */
export const useSimplePhotoValidation = (photoUrls: string[]) => {
  const { summary, isLoading, isPhotoValid } = usePhotoValidation(photoUrls, {
    retryAttempts: 1,
    timeout: 5000,
  });

  return {
    validPhotos: summary.validPhotos,
    brokenPhotos: summary.brokenPhotos,
    isLoading,
    isPhotoValid,
    hasValidPhotos: summary.valid > 0,
    hasBrokenPhotos: summary.broken > 0,
    validationRate: summary.total > 0 ? (summary.valid / summary.total) * 100 : 0,
  };
};
