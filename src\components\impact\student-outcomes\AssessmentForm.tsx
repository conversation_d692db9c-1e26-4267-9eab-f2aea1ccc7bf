import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Save } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';
import { AssessmentType, ILeadParticipantInfo } from '@/types/impact';

const assessmentSchema = z.object({
  assessment_type: z.enum(['baseline', 'quarterly', 'annual']),
  school_id: z.string().uuid(),
  assessment_date: z.string(),
  academic_year: z.string(),
  assessor_name: z.string().optional(),
  notes: z.string().optional(),

  // Participant Information
  participant_info: z.object({
    full_name: z.string().min(1, 'Full name is required'),
    age: z.number().min(10).max(25).optional(),
    gender: z.enum(['male', 'female']),
    learning_goal: z.string().optional()
  }),

  // Leadership Assessment Scores (1-5 scale)
  leadership_scores: z.object({
    consider_myself_leader: z.string().refine(val => ['1', '2', '3', '4', '5'].includes(val), 'Please select a score'),
    strive_to_finish_started: z.string().refine(val => ['1', '2', '3', '4', '5'].includes(val), 'Please select a score'),
    understand_importance_decisions: z.string().refine(val => ['1', '2', '3', '4', '5'].includes(val), 'Please select a score'),
    take_full_responsibility: z.string().refine(val => ['1', '2', '3', '4', '5'].includes(val), 'Please select a score'),
    understand_leader_values: z.string().refine(val => ['1', '2', '3', '4', '5'].includes(val), 'Please select a score'),
    strive_push_limits: z.string().refine(val => ['1', '2', '3', '4', '5'].includes(val), 'Please select a score'),
    value_people_around: z.string().refine(val => ['1', '2', '3', '4', '5'].includes(val), 'Please select a score'),
    good_example_to_others: z.string().refine(val => ['1', '2', '3', '4', '5'].includes(val), 'Please select a score'),
    responsible_with_homework: z.string().refine(val => ['1', '2', '3', '4', '5'].includes(val), 'Please select a score'),
    responsible_with_housework: z.string().refine(val => ['1', '2', '3', '4', '5'].includes(val), 'Please select a score'),
    confident_in_myself: z.string().refine(val => ['1', '2', '3', '4', '5'].includes(val), 'Please select a score')
  })
});

type AssessmentFormData = z.infer<typeof assessmentSchema>;

interface AssessmentFormProps {
  onClose: () => void;
  schoolId?: string | null;
}

const AssessmentForm: React.FC<AssessmentFormProps> = ({ onClose, schoolId }) => {
  const { profile } = useAuth();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch schools for selection
  const { data: schools } = useQuery({
    queryKey: ['schools'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('schools')
        .select('id, name')
        .order('name');
      
      if (error) throw error;
      return data;
    }
  });

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<AssessmentFormData>({
    resolver: zodResolver(assessmentSchema),
    defaultValues: {
      school_id: schoolId || '',
      assessment_date: new Date().toISOString().split('T')[0],
      academic_year: new Date().getFullYear().toString(),
      participant_info: {
        full_name: '',
        gender: 'male'
      },
      leadership_scores: {
        consider_myself_leader: '3',
        strive_to_finish_started: '3',
        understand_importance_decisions: '3',
        take_full_responsibility: '3',
        understand_leader_values: '3',
        strive_push_limits: '3',
        value_people_around: '3',
        good_example_to_others: '3',
        responsible_with_homework: '3',
        responsible_with_housework: '3',
        confident_in_myself: '3'
      }
    }
  });



  // Create assessment mutation
  const createAssessmentMutation = useMutation({
    mutationFn: async (data: AssessmentFormData) => {
      // Create iLead assessment record using the database table structure
      const assessmentRecord = {
        school_id: data.school_id,
        assessment_type: data.assessment_type,
        assessment_date: data.assessment_date,
        academic_year: data.academic_year,
        assessor_name: data.assessor_name,
        notes: data.notes,
        created_by: profile?.id,

        // Participant information
        participant_full_name: data.participant_info.full_name,
        participant_age: data.participant_info.age,
        participant_gender: data.participant_info.gender,
        participant_learning_goal: data.participant_info.learning_goal,

        // Leadership scores (convert strings to numbers)
        consider_myself_leader: parseInt(data.leadership_scores.consider_myself_leader),
        strive_to_finish_started: parseInt(data.leadership_scores.strive_to_finish_started),
        understand_importance_decisions: parseInt(data.leadership_scores.understand_importance_decisions),
        take_full_responsibility: parseInt(data.leadership_scores.take_full_responsibility),
        understand_leader_values: parseInt(data.leadership_scores.understand_leader_values),
        strive_push_limits: parseInt(data.leadership_scores.strive_push_limits),
        value_people_around: parseInt(data.leadership_scores.value_people_around),
        good_example_to_others: parseInt(data.leadership_scores.good_example_to_others),
        responsible_with_homework: parseInt(data.leadership_scores.responsible_with_homework),
        responsible_with_housework: parseInt(data.leadership_scores.responsible_with_housework),
        confident_in_myself: parseInt(data.leadership_scores.confident_in_myself)
      };

      // Save to database using Supabase
      const { data: savedAssessment, error } = await supabase
        .from('ilead_assessments')
        .insert([assessmentRecord])
        .select()
        .single();

      if (error) {
        console.error('Error saving assessment to database:', error);
        throw error;
      }

      console.log('Assessment data saved to database:', savedAssessment);
      return savedAssessment;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['student-learning-outcomes'] });
      toast.success('Assessment data saved successfully');
      onClose();
    },
    onError: (error) => {
      console.error('Error saving assessment:', error);
      toast.error('Failed to save assessment data');
    }
  });

  const onSubmit = async (data: AssessmentFormData) => {
    setIsSubmitting(true);
    try {
      await createAssessmentMutation.mutateAsync(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  // No longer needed - we're collecting individual participant data

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>iLead Assessment</DialogTitle>
          <DialogDescription>
            Students' pre-iLead Self-evaluation - A youth values-based leadership program assessment
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Assessment Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Assessment Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="school_id">School *</Label>
                  <Select 
                    value={watch('school_id')} 
                    onValueChange={(value) => setValue('school_id', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select school" />
                    </SelectTrigger>
                    <SelectContent>
                      {schools?.map((school) => (
                        <SelectItem key={school.id} value={school.id}>
                          {school.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.school_id && (
                    <p className="text-sm text-red-600 mt-1">{errors.school_id.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="assessment_type">Assessment Type *</Label>
                  <Select 
                    value={watch('assessment_type')} 
                    onValueChange={(value) => setValue('assessment_type', value as AssessmentType)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select assessment type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="baseline">Baseline (Pre-iLead)</SelectItem>
                      <SelectItem value="quarterly">Quarterly</SelectItem>
                      <SelectItem value="annual">Annual</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.assessment_type && (
                    <p className="text-sm text-red-600 mt-1">{errors.assessment_type.message}</p>
                  )}
                </div>



                <div>
                  <Label htmlFor="assessment_date">Assessment Date *</Label>
                  <Input
                    type="date"
                    {...register('assessment_date')}
                  />
                  {errors.assessment_date && (
                    <p className="text-sm text-red-600 mt-1">{errors.assessment_date.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="academic_year">Academic Year *</Label>
                  <Input
                    placeholder="e.g., 2024"
                    {...register('academic_year')}
                  />
                  {errors.academic_year && (
                    <p className="text-sm text-red-600 mt-1">{errors.academic_year.message}</p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="assessor_name">Assessor Name</Label>
                <Input
                  placeholder="Name of person conducting assessment"
                  {...register('assessor_name')}
                />
              </div>

              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  placeholder="Additional notes about the assessment"
                  {...register('notes')}
                />
              </div>
            </CardContent>
          </Card>

          {/* Participant Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Participant Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Full name, Age, and Gender in one row */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="participant_full_name">Full Name *</Label>
                    <Input
                      {...register('participant_info.full_name')}
                      placeholder="Enter participant's full name"
                    />
                    {errors.participant_info?.full_name && (
                      <p className="text-sm text-red-600 mt-1">{errors.participant_info.full_name.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="participant_age">Age</Label>
                    <Input
                      type="number"
                      min="10"
                      max="25"
                      {...register('participant_info.age', { valueAsNumber: true })}
                      placeholder="Enter age"
                    />
                    {errors.participant_info?.age && (
                      <p className="text-sm text-red-600 mt-1">{errors.participant_info.age.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="participant_gender">Gender *</Label>
                    <Select
                      value={watch('participant_info.gender')}
                      onValueChange={(value) => setValue('participant_info.gender', value as 'male' | 'female')}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.participant_info?.gender && (
                      <p className="text-sm text-red-600 mt-1">{errors.participant_info.gender.message}</p>
                    )}
                  </div>
                </div>

                {/* Learning goal */}
                <div>
                  <Label htmlFor="participant_learning_goal">Name one thing you would like to learn in the iLead Program</Label>
                  <Textarea
                    {...register('participant_info.learning_goal')}
                    placeholder="What would you like to learn from this program?"
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Leadership Assessment Questions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Leadership Self-Assessment</CardTitle>
              <p className="text-sm text-gray-600">
                Please respond to the questions below by selecting a number which best represents your answer.
                There is no "right or wrong" answer. Just respond as honestly as you see yourself.
              </p>
              <div className="bg-gray-50 p-3 rounded-lg mt-2">
                <p className="text-sm font-medium">Scale:</p>
                <div className="grid grid-cols-5 gap-2 mt-1 text-xs">
                  <span>1 - Very Bad</span>
                  <span>2 - Bad</span>
                  <span>3 - Regular</span>
                  <span>4 - Very Good</span>
                  <span>5 - Excellent</span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {[
                  { key: 'consider_myself_leader', label: 'I consider myself a leader' },
                  { key: 'strive_to_finish_started', label: 'I strive to finish what I started' },
                  { key: 'understand_importance_decisions', label: 'I understand the importance of my decisions' },
                  { key: 'take_full_responsibility', label: 'I take full responsibility for my actions' },
                  { key: 'understand_leader_values', label: 'I understand the values of a great leader' },
                  { key: 'strive_push_limits', label: 'I strive to push my limits' },
                  { key: 'value_people_around', label: 'I value the people around me' },
                  { key: 'good_example_to_others', label: 'I am a good example to the people around me (home, friends)' },
                  { key: 'responsible_with_homework', label: 'I am responsible with my homework at school' },
                  { key: 'responsible_with_housework', label: 'I am responsible helping with the housework' },
                  { key: 'confident_in_myself', label: 'I am confident in myself' }
                ].map((question, index) => (
                  <div key={question.key} className="border-b pb-4 last:border-b-0">
                    <div className="flex items-start justify-between mb-3">
                      <label className="text-sm font-medium flex-1 pr-4">
                        {index + 1}. {question.label}
                      </label>
                    </div>
                    <div className="flex space-x-4">
                      {[1, 2, 3, 4, 5].map((score) => (
                        <label key={score} className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="radio"
                            value={score.toString()}
                            {...register(`leadership_scores.${question.key}`)}
                            className="w-4 h-4 text-ilead-green focus:ring-ilead-green"
                          />
                          <span className="text-sm">{score}</span>
                        </label>
                      ))}
                    </div>
                    {errors.leadership_scores?.[question.key as keyof typeof errors.leadership_scores] && (
                      <p className="text-sm text-red-600 mt-1">Please select a score</p>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="bg-ilead-green hover:bg-ilead-dark-green"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Assessment
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AssessmentForm;
