import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  MapPin,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2,
  Smartphone,
  Wifi,
  WifiOff,
  Battery,
  Signal
} from 'lucide-react';
import { useGPSWithFallback, LocationData } from '@/hooks/field-staff/useGPSWithFallback';
import { getDeviceInfo, getNetworkInfo, formatLocation } from '@/hooks/field-staff/useGPSLocation';
import { useAdaptiveGPS } from '@/hooks/field-staff/useAdaptiveGPS';
import { useFieldStaffCheckIn, useCurrentAttendance } from '@/hooks/field-staff/useFieldStaffAttendance';
import { useUnifiedCheckInStatus } from '@/hooks/attendance/useUnifiedCheckInStatus';
import { withFieldErrorBoundary, useErrorReporting } from '@/utils/fieldErrorBoundaryUtils';
import { FieldOperationError, ErrorHandler } from '@/utils/errorHandling';
import { useSchools } from '@/hooks/useSchools';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import OfflineStatusIndicator from './OfflineStatusIndicator';
import ManualLocationEntry from './ManualLocationEntry';
import CheckInTypeSelector from './CheckInTypeSelector';
import OfficeLocationSelector from './OfficeLocationSelector';
import { useOfficeLocations } from '@/hooks/useOfficeLocations';
import { CheckInType, CHECK_IN_TYPES } from '@/constants/officeLocations';
import { validateOfficeCheckIn } from '@/utils/officeGeofencing';
import { toast } from 'sonner';

const FieldStaffCheckIn: React.FC = () => {
  const [checkInType, setCheckInType] = useState<CheckInType>(CHECK_IN_TYPES.SCHOOL);
  const [selectedSchoolId, setSelectedSchoolId] = useState<string>('');
  const [selectedOfficeId, setSelectedOfficeId] = useState<string>('');
  const [isOfflineMode, setIsOfflineMode] = useState(false);
  const [retryAttempt, setRetryAttempt] = useState(0);
  const [lastError, setLastError] = useState<string | null>(null);

  const {
    location,
    error: locationError,
    loading: locationLoading,
    getLocationWithFallback,
    retryGPS,
    handleManualLocation,
    cancelManualEntry,
    showManualEntry,
    canRetryGPS,
    gpsSupported,
    movementState,
    batteryOptimized,
    setBatteryOptimized
  } = useGPSWithFallback();

  const checkInMutation = useFieldStaffCheckIn();
  const { data: currentAttendance, isLoading: attendanceLoading } = useCurrentAttendance();
  const { data: unifiedStatus } = useUnifiedCheckInStatus();
  const { data: schools, isLoading: schoolsLoading } = useSchools();
  const { data: offices, isLoading: officesLoading } = useOfficeLocations();
  const { reportError } = useErrorReporting();

  // Adaptive GPS with optimized battery settings (check-in only approach)
  const adaptiveGPS = useAdaptiveGPS({
    batteryOptimized: true, // Always use battery optimization
    movementThreshold: 15, // 15 meters
    stationaryTimeout: 600000, // 10 minutes - longer for better battery life
    activeInterval: 30000, // 30 seconds - optimized for battery
    stationaryInterval: 180000, // 3 minutes - optimized for battery
    backgroundInterval: 600000, // 10 minutes - optimized for battery
    highAccuracyEnabled: false, // Disabled for battery optimization
    maxAcceptableAccuracy: 100, // 100 meters - more lenient for battery mode
  });

  // Check if user is already checked in using unified status
  const isCheckedIn = unifiedStatus?.isCheckedIn ?? false;
  const isOnline = navigator.onLine;

  // Auto-detect offline mode
  useEffect(() => {
    const handleOnlineStatus = () => {
      setIsOfflineMode(!navigator.onLine);
    };

    window.addEventListener('online', handleOnlineStatus);
    window.addEventListener('offline', handleOnlineStatus);
    
    // Initial check
    setIsOfflineMode(!navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnlineStatus);
      window.removeEventListener('offline', handleOnlineStatus);
    };
  }, []);

  const handleCheckIn = async () => {
    // Validate selection based on check-in type
    if (checkInType === CHECK_IN_TYPES.SCHOOL && !selectedSchoolId) {
      toast.error('Please select a school');
      return;
    }

    if (checkInType === CHECK_IN_TYPES.OFFICE && !selectedOfficeId) {
      toast.error('Please select an office location');
      return;
    }

    try {
      setLastError(null);
      let currentLocation = location;

      // Get fresh location if not available with enhanced fallback logic
      if (!currentLocation) {
        try {
          currentLocation = await getLocationWithFallback({
            enableHighAccuracy: !batteryOptimized,
            timeout: batteryOptimized ? 30000 : 15000,
            maximumAge: batteryOptimized ? 300000 : 60000,
          });
        } catch (gpsError) {
          // Enhanced error handling will show manual entry option
          setLastError('Location unavailable. Please use manual entry or retry GPS.');
          return;
        }
      }

      if (!currentLocation) {
        setLastError('Location unavailable. Please enable GPS and try again.');
        return;
      }

      // For office check-ins, validate geofencing
      if (checkInType === CHECK_IN_TYPES.OFFICE && selectedOfficeId && offices) {
        const selectedOffice = offices.find(o => o.id === selectedOfficeId);
        if (selectedOffice) {
          const validation = await validateOfficeCheckIn(
            selectedOffice,
            async () => currentLocation!
          );

          if (!validation.isValid) {
            toast.error(validation.errorMessage || 'Office check-in validation failed');
            return;
          }

          if (validation.warningMessage) {
            toast.warning(validation.warningMessage);
          }
        }
      }

      // Validate location accuracy
      if (currentLocation.accuracy > 100 && !batteryOptimized) {
        toast.warning('Location accuracy is low. Consider moving to an open area for better GPS signal.');
      }

      const deviceInfo = getDeviceInfo();
      const networkInfo = getNetworkInfo();

      await checkInMutation.mutateAsync({
        school_id: checkInType === CHECK_IN_TYPES.SCHOOL ? selectedSchoolId : undefined,
        office_id: checkInType === CHECK_IN_TYPES.OFFICE ? selectedOfficeId : undefined,
        check_in_type: checkInType,
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        accuracy: currentLocation.accuracy,
        verification_method: 'gps',
        device_info: {
          ...deviceInfo,
          batteryOptimized,
          movementState: movementState.isStationary ? 'stationary' : 'moving',
        },
        network_info: networkInfo,
        offline_sync: isOfflineMode,
      });

      // Clear selections
      setSelectedSchoolId('');
      setSelectedOfficeId('');
      setRetryAttempt(0);

    } catch (error: unknown) {
      console.error('Check-in failed:', error);
      setRetryAttempt(prev => prev + 1);

      // Enhanced error handling
      if (error instanceof Error) {
        const fieldError = FieldOperationError.fromNetworkError(error, isOnline);
        await ErrorHandler.handle(fieldError);
        setLastError(fieldError.fieldError.message);
        reportError(error, {
          context: 'check-in',
          schoolId: selectedSchoolId,
          retryAttempt,
          isOffline: isOfflineMode
        });
      } else {
        setLastError('An unexpected error occurred during check-in');
        toast.error('Check-in failed. Please try again.');
      }
    }
  };

  const getLocationStatus = () => {
    if (!gpsSupported) {
      return { status: 'error', message: 'GPS not supported on this device' };
    }
    
    if (locationError) {
      return { status: 'error', message: locationError.message };
    }
    
    if (locationLoading) {
      return { status: 'loading', message: 'Getting your location...' };
    }
    
    if (location) {
      return { 
        status: 'success', 
        message: `Location: ${formatLocation(location)} (±${Math.round(location.accuracy)}m)` 
      };
    }
    
    return { status: 'waiting', message: 'Click to get your location' };
  };

  const locationStatus = getLocationStatus();

  if (attendanceLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <PageHeader
        title="Field Staff Check-In"
        description="Check in at schools for field activities or at the office for administrative work"
      />

      {/* Connection Status */}
      <OfflineStatusIndicator showDetails={true} />

      <ContentCard>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Smartphone className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-600">
              {new Date().toLocaleString()}
            </span>
          </div>
        </div>
      </ContentCard>

      {/* Current Status */}
      {isCheckedIn && currentAttendance && (
        <ContentCard>
          <div className="flex items-center gap-3 p-4 bg-green-50 rounded-lg border border-green-200">
            <CheckCircle className="h-6 w-6 text-green-600" />
            <div>
              <div className="font-medium text-green-800">
                Already Checked In
              </div>
              <div className="text-sm text-green-600">
                Checked in at {new Date(currentAttendance.check_in_time).toLocaleTimeString()}
              </div>
            </div>
          </div>
        </ContentCard>
      )}

      {/* Check-In Form */}
      {!isCheckedIn && (
        <>
          {/* Check-in Type Selection */}
          <CheckInTypeSelector
            selectedType={checkInType}
            onTypeChange={setCheckInType}
            disabled={checkInMutation.isPending}
          />

          {/* Location Selection based on check-in type */}
          {checkInType === CHECK_IN_TYPES.SCHOOL && (
            <ContentCard>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  School Selection
                </CardTitle>
                <CardDescription>
                  Select the school you're visiting for field activities
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* School Selection */}
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Select School <span className="text-red-500">*</span>
                  </label>
                  <Select value={selectedSchoolId} onValueChange={setSelectedSchoolId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a school..." />
                    </SelectTrigger>
                    <SelectContent>
                      {schoolsLoading ? (
                        <SelectItem value="loading-schools" disabled>Loading schools...</SelectItem>
                      ) : schools && schools.length > 0 ? (
                        schools.map((school) => (
                          <SelectItem key={school.id} value={school.id}>
                            {school.name} - {school.district}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-schools" disabled>No schools available</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </ContentCard>
          )}

          {/* Office Location Selection */}
          {checkInType === CHECK_IN_TYPES.OFFICE && (
            <OfficeLocationSelector
              selectedOfficeId={selectedOfficeId}
              onOfficeChange={setSelectedOfficeId}
              userLocation={location}
              disabled={checkInMutation.isPending}
            />
          )}

          {/* Location Status and Check-in Button */}
          <ContentCard>
            <CardContent className="space-y-6">

            {/* Location Status */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Your Location
              </label>
              <div className="flex items-center gap-3 p-3 border rounded-lg">
                {locationStatus.status === 'loading' && (
                  <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
                )}
                {locationStatus.status === 'success' && (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                )}
                {locationStatus.status === 'error' && (
                  <AlertCircle className="h-5 w-5 text-red-500" />
                )}
                {locationStatus.status === 'waiting' && (
                  <MapPin className="h-5 w-5 text-gray-500" />
                )}
                
                <div className="flex-1">
                  <div className="text-sm font-medium">
                    {locationStatus.status === 'success' ? 'Location Confirmed' : 'Location Status'}
                  </div>
                  <div className="text-xs text-gray-600">
                    {locationStatus.message}
                  </div>
                </div>

                {!location && !locationLoading && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => getLocationWithFallback()}
                    disabled={locationLoading}
                  >
                    Get Location
                  </Button>
                )}
              </div>
            </div>

            {/* Manual Location Entry */}
            {showManualEntry && (
              <div className="space-y-4">
                <ManualLocationEntry
                  onLocationSelected={handleManualLocation}
                  onCancel={cancelManualEntry}
                  showGPSRetry={canRetryGPS()}
                  onGPSRetry={retryGPS}
                  disabled={locationLoading}
                />
              </div>
            )}

            {/* Check-In Button */}
            <Button
              onClick={handleCheckIn}
              disabled={
                (checkInType === CHECK_IN_TYPES.SCHOOL && !selectedSchoolId) ||
                (checkInType === CHECK_IN_TYPES.OFFICE && !selectedOfficeId) ||
                !location ||
                checkInMutation.isPending ||
                locationLoading
              }
              className="w-full"
              size="lg"
            >
              {checkInMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Checking In...
                </>
              ) : (
                <>
                  <Clock className="h-4 w-4 mr-2" />
                  Check In to {checkInType === CHECK_IN_TYPES.SCHOOL ? 'School' : 'Office'}
                </>
              )}
            </Button>

            {/* Requirements */}
            <div className="text-xs text-gray-500 space-y-1">
              <div>• Location is required for check-in (GPS or manual entry)</div>
              {checkInType === CHECK_IN_TYPES.SCHOOL && (
                <div>• Make sure you're at or near the school location</div>
              )}
              {checkInType === CHECK_IN_TYPES.OFFICE && (
                <div>• You must be within 1km of the office to check in</div>
              )}
              <div>• Check-in time and location will be recorded</div>
              {showManualEntry && (
                <div className="text-orange-600">• Manual location entry available when GPS fails</div>
              )}
            </div>
            </CardContent>
          </ContentCard>
        </>
      )}

      {/* Today's Activity Summary */}
      {isCheckedIn && currentAttendance && (
        <ContentCard>
          <CardHeader>
            <CardTitle>Today's Activity</CardTitle>
            <CardDescription>Your current session details</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm font-medium text-gray-600">Check-in Time</div>
                <div className="text-lg">
                  {new Date(currentAttendance.check_in_time).toLocaleTimeString()}
                </div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-600">Duration</div>
                <div className="text-lg">
                  {Math.round((Date.now() - new Date(currentAttendance.check_in_time).getTime()) / (1000 * 60))} minutes
                </div>
              </div>
            </div>
          </CardContent>
        </ContentCard>
      )}
    </PageLayout>
  );
};

// Export the wrapped component with a proper name
const FieldStaffCheckInWithErrorBoundary = withFieldErrorBoundary(FieldStaffCheckIn);
FieldStaffCheckInWithErrorBoundary.displayName = 'FieldStaffCheckIn';

export default FieldStaffCheckInWithErrorBoundary;
