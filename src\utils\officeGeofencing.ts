/**
 * Office Geofencing Utilities
 * Location validation and distance calculation for office check-ins
 */

import { calculateDistance } from '@/hooks/field-staff/useGPSLocation';
import { 
  OfficeLocation, 
  OFFICE_CHECKIN_CONFIG, 
  OFFICE_CHECKIN_ERRORS 
} from '@/constants/officeLocations';

export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp?: number;
}

export interface GeofenceValidationResult {
  isValid: boolean;
  distance: number;
  errorMessage?: string;
  warningMessage?: string;
}

/**
 * Validate if user location is within office geofence
 */
export function validateOfficeGeofence(
  userLocation: LocationData,
  office: OfficeLocation
): GeofenceValidationResult {
  const distance = calculateDistance(
    userLocation.latitude,
    userLocation.longitude,
    office.coordinates.latitude,
    office.coordinates.longitude
  );

  const isWithinGeofence = distance <= office.geofenceRadius;

  if (!isWithinGeofence) {
    return {
      isValid: false,
      distance,
      errorMessage: `You are ${Math.round(distance)}m from ${office.name}. You must be within ${office.geofenceRadius}m to check in.`,
    };
  }

  // Check GPS accuracy if available
  if (userLocation.accuracy && userLocation.accuracy > OFFICE_CHECKIN_CONFIG.GPS_ACCURACY_THRESHOLD) {
    return {
      isValid: true,
      distance,
      warningMessage: `GPS accuracy is ${Math.round(userLocation.accuracy)}m. Consider moving to an area with better signal for more accurate location.`,
    };
  }

  return {
    isValid: true,
    distance,
  };
}

/**
 * Get the closest office to user location
 */
export function getClosestOffice(
  userLocation: LocationData,
  offices: OfficeLocation[]
): { office: OfficeLocation; distance: number } | null {
  if (offices.length === 0) return null;

  let closestOffice = offices[0];
  let minDistance = calculateDistance(
    userLocation.latitude,
    userLocation.longitude,
    closestOffice.coordinates.latitude,
    closestOffice.coordinates.longitude
  );

  for (let i = 1; i < offices.length; i++) {
    const office = offices[i];
    const distance = calculateDistance(
      userLocation.latitude,
      userLocation.longitude,
      office.coordinates.latitude,
      office.coordinates.longitude
    );

    if (distance < minDistance) {
      minDistance = distance;
      closestOffice = office;
    }
  }

  return { office: closestOffice, distance: minDistance };
}

/**
 * Check if user is within any office geofence
 */
export function isWithinAnyOfficeGeofence(
  userLocation: LocationData,
  offices: OfficeLocation[]
): { isWithin: boolean; office?: OfficeLocation; distance?: number } {
  for (const office of offices) {
    if (!office.isActive) continue;

    const validation = validateOfficeGeofence(userLocation, office);
    if (validation.isValid) {
      return {
        isWithin: true,
        office,
        distance: validation.distance,
      };
    }
  }

  return { isWithin: false };
}

/**
 * Format distance for display
 */
export function formatDistance(distanceInMeters: number): string {
  if (distanceInMeters < 1000) {
    return `${Math.round(distanceInMeters)}m`;
  } else {
    return `${(distanceInMeters / 1000).toFixed(1)}km`;
  }
}

/**
 * Get GPS options optimized for office check-in
 */
export function getOfficeGPSOptions(): PositionOptions {
  return {
    enableHighAccuracy: OFFICE_CHECKIN_CONFIG.HIGH_ACCURACY_GPS,
    timeout: OFFICE_CHECKIN_CONFIG.GPS_TIMEOUT,
    maximumAge: OFFICE_CHECKIN_CONFIG.GPS_MAX_AGE,
  };
}

/**
 * Validate GPS accuracy for office check-in
 */
export function validateGPSAccuracy(accuracy: number): {
  isAcceptable: boolean;
  message?: string;
} {
  if (accuracy <= OFFICE_CHECKIN_CONFIG.GPS_ACCURACY_THRESHOLD) {
    return { isAcceptable: true };
  }

  return {
    isAcceptable: false,
    message: `GPS accuracy is ${Math.round(accuracy)}m. For office check-in, accuracy should be better than ${OFFICE_CHECKIN_CONFIG.GPS_ACCURACY_THRESHOLD}m. Please move to an area with better GPS signal.`,
  };
}

/**
 * Create location data from GeolocationPosition
 */
export function createLocationData(position: GeolocationPosition): LocationData {
  return {
    latitude: position.coords.latitude,
    longitude: position.coords.longitude,
    accuracy: position.coords.accuracy,
    timestamp: position.timestamp,
  };
}

/**
 * Office check-in validation pipeline
 */
export async function validateOfficeCheckIn(
  office: OfficeLocation,
  getCurrentLocation: () => Promise<LocationData>
): Promise<{
  isValid: boolean;
  location?: LocationData;
  distance?: number;
  errorMessage?: string;
  warningMessage?: string;
}> {
  try {
    // Get current location
    const location = await getCurrentLocation();

    // Validate GPS accuracy
    if (location.accuracy) {
      const accuracyValidation = validateGPSAccuracy(location.accuracy);
      if (!accuracyValidation.isAcceptable) {
        return {
          isValid: false,
          location,
          errorMessage: accuracyValidation.message,
        };
      }
    }

    // Validate geofence
    const geofenceValidation = validateOfficeGeofence(location, office);

    return {
      isValid: geofenceValidation.isValid,
      location,
      distance: geofenceValidation.distance,
      errorMessage: geofenceValidation.errorMessage,
      warningMessage: geofenceValidation.warningMessage,
    };
  } catch (error) {
    return {
      isValid: false,
      errorMessage: error instanceof Error ? error.message : OFFICE_CHECKIN_ERRORS.GPS_UNAVAILABLE,
    };
  }
}
