import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import {
  FileText,
  Download,
  Calendar,
  Users,
  School,
  GraduationCap,
  Award,
  Camera
} from 'lucide-react';
import { PageLayout, PageHeader } from '@/components/layout';
import { useReportData, useReportPhotos } from '@/hooks/useReportData';
import { toast } from 'sonner';
import PhotoGallery from '@/components/photos/PhotoGallery';

interface SimplifiedImpactReportsProps {
  schoolId?: string | null;
  dateRange: {
    start: Date;
    end: Date;
  };
  canViewAllData: boolean;
}

const SimplifiedImpactReports: React.FC<SimplifiedImpactReportsProps> = ({
  schoolId,
  dateRange,
  canViewAllData
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState<'weekly' | 'monthly' | 'quarterly'>('monthly');
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  // Fetch report data with timeout handling
  const { data: reportData, isLoading: isLoadingReport, error: reportError } = useReportData({
    period: selectedPeriod,
    month: selectedMonth,
    year: selectedYear
  });

  // Fetch photos for the gallery
  const { data: photos, isLoading: isLoadingPhotos } = useReportPhotos({
    month: selectedMonth,
    year: selectedYear
  });

  // Fallback data for when queries are slow or fail
  const fallbackData = {
    schools_onboarded: 0,
    students_oriented: 0,
    teacher_champions: 0,
    student_roundtables: 0,
    planned_activities: [
      {
        name: 'Leadership Training Sessions',
        venue: 'Various Schools',
        period: 'Monthly Schedule'
      }
    ],
    conducted_activities: [
      {
        planned: 'Data Loading',
        achieved: 'Please wait while we fetch the latest data...',
        progress: 'In Progress',
        observation: 'Loading report information'
      }
    ],
    student_comments: ['Loading student feedback...'],
    challenges: ['Loading challenges data...'],
    way_forward: ['Loading action items...']
  };

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const handleGenerateReport = async () => {
    if (isLoadingReport) return;

    try {
      toast.loading('Generating PDF report...', { id: 'pdf-generation' });

      const reportContent = document.querySelector('.report-content');
      if (!reportContent) {
        toast.error('Report content not found', { id: 'pdf-generation' });
        return;
      }

      // Create filename
      const reportTitle = `iLEAD_${selectedPeriod}_Report_${monthNames[selectedMonth - 1]}_${selectedYear}`;

      // Hide elements that should be excluded from PDF
      const excludeElements = reportContent.querySelectorAll('.pdf-exclude');
      excludeElements.forEach(el => {
        (el as HTMLElement).style.display = 'none';
      });

      // Configure html2canvas options for better quality
      const canvas = await html2canvas(reportContent as HTMLElement, {
        scale: 2, // Higher resolution
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: reportContent.scrollWidth,
        height: reportContent.scrollHeight,
      });

      // Restore hidden elements
      excludeElements.forEach(el => {
        (el as HTMLElement).style.display = '';
      });

      // Create PDF
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      // Calculate dimensions to fit A4
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const imgWidth = canvas.width;
      const imgHeight = canvas.height;
      const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
      const imgX = (pdfWidth - imgWidth * ratio) / 2;
      const imgY = 10; // Small top margin

      // Add the image to PDF
      pdf.addImage(imgData, 'PNG', imgX, imgY, imgWidth * ratio, imgHeight * ratio);

      // Save the PDF
      pdf.save(`${reportTitle}.pdf`);

      toast.success('PDF report downloaded successfully!', { id: 'pdf-generation' });

    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Failed to generate PDF. Please try again.', { id: 'pdf-generation' });
    }
  };

  // Use fallback data while loading to improve perceived performance
  const displayData = reportData || fallbackData;
  const isDataReady = !isLoadingReport && reportData;

  return (
    <PageLayout>
      <PageHeader
        title="iLEAD Activity Reports"
        description="Generate weekly, monthly, or quarterly activity reports"
        icon={FileText}
        actions={[
          {
            label: isLoadingReport ? 'Loading...' : 'Download PDF',
            onClick: handleGenerateReport,
            icon: Download,
            disabled: isLoadingReport
          }
        ]}
      />

      {/* Report Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Report Configuration</CardTitle>
          <CardDescription>
            Select the report period and timeframe
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Report Period</label>
              <Select value={selectedPeriod} onValueChange={(value: string) => setSelectedPeriod(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="weekly">Weekly Report</SelectItem>
                  <SelectItem value="monthly">Monthly Report</SelectItem>
                  <SelectItem value="quarterly">Quarterly Report</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Month</label>
              <Select value={selectedMonth.toString()} onValueChange={(value) => setSelectedMonth(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {monthNames.map((month, index) => (
                    <SelectItem key={index} value={(index + 1).toString()}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Year</label>
              <Select value={selectedYear.toString()} onValueChange={(value) => setSelectedYear(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="2025">2025</SelectItem>
                  <SelectItem value="2024">2024</SelectItem>
                  <SelectItem value="2023">2023</SelectItem>
                  <SelectItem value="2022">2022</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Content */}
      <div className="report-content space-y-6 print:space-y-4">
        {/* Header */}
        <Card className="print:shadow-none print:border-0">
          <CardContent className="pt-6">
            <div className="relative mb-6">
              {/* Logo in top-left corner */}
              <div className="absolute top-0 left-0 print:relative print:inline-block print:mb-4">
                <img
                  src="/ilead-logo.svg"
                  alt="iLEAD Logo"
                  className="w-16 h-16 print:w-12 print:h-12"
                />
              </div>

              {/* Centered content */}
              <div className="text-center">
                <h1 className="text-2xl font-bold text-ilead-green">iLEAD UGANDA</h1>
                <h2 className="text-xl font-semibold mt-2">
                  {selectedPeriod.toUpperCase()} ACTIVITY REPORT
                </h2>
                <p className="text-gray-600 mt-2">
                  Report for {monthNames[selectedMonth - 1]} {selectedYear}
                  {isLoadingReport && (
                    <span className="ml-2 inline-flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-ilead-green"></div>
                      <span className="ml-1 text-sm">Loading...</span>
                    </span>
                  )}
                </p>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-2">Introduction:</h3>
              <p className="text-gray-700">
                This report provides a comprehensive overview of iLead program activities, 
                achievements, and impact for the period of {monthNames[selectedMonth - 1]} {selectedYear}.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Table 1: Program Statistics */}
        <Card className="print:shadow-none print:border-0">
          <CardHeader>
            <CardTitle>Table 1: Program Statistics</CardTitle>
            <CardDescription>
              Total numbers in the iLead program by {selectedPeriod} ended
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 p-3 text-left">No. of schools onboarded</th>
                    <th className="border border-gray-300 p-3 text-left">No. of students oriented</th>
                    <th className="border border-gray-300 p-3 text-left">No. of teacher champions</th>
                    <th className="border border-gray-300 p-3 text-left">No. of students' roundtables</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="border border-gray-300 p-3">
                      {displayData.schools_onboarded}
                      {isLoadingReport && <span className="text-gray-400 ml-1">*</span>}
                    </td>
                    <td className="border border-gray-300 p-3">
                      {displayData.students_oriented}
                      {isLoadingReport && <span className="text-gray-400 ml-1">*</span>}
                    </td>
                    <td className="border border-gray-300 p-3">
                      {displayData.teacher_champions}
                      {isLoadingReport && <span className="text-gray-400 ml-1">*</span>}
                    </td>
                    <td className="border border-gray-300 p-3">
                      {displayData.student_roundtables}
                      {isLoadingReport && <span className="text-gray-400 ml-1">*</span>}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Table 2: Planned Activities */}
        <Card className="print:shadow-none print:border-0">
          <CardHeader>
            <CardTitle>Table 2: {monthNames[selectedMonth - 1]} Planned Activities</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 p-3 text-left">No</th>
                    <th className="border border-gray-300 p-3 text-left">Name of activity</th>
                    <th className="border border-gray-300 p-3 text-left">Venue & location</th>
                    <th className="border border-gray-300 p-3 text-left">Activity period</th>
                  </tr>
                </thead>
                <tbody>
                  {displayData.planned_activities?.map((activity, index) => (
                    <tr key={index}>
                      <td className="border border-gray-300 p-3">{index + 1}</td>
                      <td className="border border-gray-300 p-3">{activity.name}</td>
                      <td className="border border-gray-300 p-3">{activity.venue}</td>
                      <td className="border border-gray-300 p-3">{activity.period}</td>
                    </tr>
                  ))}
                  {isLoadingReport && (
                    <tr>
                      <td colSpan={4} className="border border-gray-300 p-3 text-center text-gray-500">
                        <div className="flex items-center justify-center space-x-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-ilead-green"></div>
                          <span>Loading planned activities...</span>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Table 3: Activities Conducted */}
        <Card className="print:shadow-none print:border-0">
          <CardHeader>
            <CardTitle>Table 3: Details of activities conducted in {monthNames[selectedMonth - 1]}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 p-3 text-left">No</th>
                    <th className="border border-gray-300 p-3 text-left">Planned Activities</th>
                    <th className="border border-gray-300 p-3 text-left">Achieved</th>
                    <th className="border border-gray-300 p-3 text-left">Implementation progress/Status</th>
                    <th className="border border-gray-300 p-3 text-left">Observation</th>
                  </tr>
                </thead>
                <tbody>
                  {displayData.conducted_activities?.map((activity, index) => (
                    <tr key={index}>
                      <td className="border border-gray-300 p-3">{index + 1}</td>
                      <td className="border border-gray-300 p-3">{activity.planned}</td>
                      <td className="border border-gray-300 p-3">{activity.achieved}</td>
                      <td className="border border-gray-300 p-3">
                        <Badge variant={activity.progress === 'Completed' ? 'default' : 'secondary'}>
                          {activity.progress}
                        </Badge>
                      </td>
                      <td className="border border-gray-300 p-3">{activity.observation}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Student Comments */}
        <Card className="print:shadow-none print:border-0">
          <CardHeader>
            <CardTitle>Comments from students after going through the iLead lesson</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {displayData.student_comments?.map((comment, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-ilead-green text-white rounded-full flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <p className="text-gray-700 italic">"{comment}"</p>
                </div>
              ))}
              {isLoadingReport && (
                <div className="text-center py-4 text-gray-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-ilead-green mx-auto mb-2"></div>
                  Loading student feedback...
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Challenges */}
        <Card className="print:shadow-none print:border-0">
          <CardHeader>
            <CardTitle>Challenges</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {displayData.challenges?.map((challenge, index) => (
                <li key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                  <p className="text-gray-700">{challenge}</p>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        {/* Way Forward */}
        <Card className="print:shadow-none print:border-0">
          <CardHeader>
            <CardTitle>Way Forward</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {displayData.way_forward?.map((item, index) => (
                <li key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <p className="text-gray-700">{item}</p>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        {/* Conclusion */}
        <Card className="print:shadow-none print:border-0">
          <CardHeader>
            <CardTitle>Conclusion</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">
              The {monthNames[selectedMonth - 1]} {selectedYear} activities have shown significant progress
              in the iLead program implementation. With {displayData.schools_onboarded} schools onboarded
              and {displayData.students_oriented} students oriented, the program continues to expand its
              reach and impact. The positive feedback from students and the successful completion of
              planned activities demonstrate the effectiveness of the leadership development approach.
              {displayData.unique_schools_reached ? ` During this period, we reached ${displayData.unique_schools_reached} unique schools with our activities.` : ''}
              {isLoadingReport && (
                <span className="block mt-2 text-sm text-gray-500 italic">
                  * Data is being updated with the latest information...
                </span>
              )}
            </p>
          </CardContent>
        </Card>

        {/* Photo Gallery - Hidden in PDF */}
        <div className="print:hidden pdf-exclude">
          {isLoadingPhotos ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Camera className="h-5 w-5" />
                  <span>Photo Gallery of {monthNames[selectedMonth - 1]} {selectedYear} Events</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-ilead-green mx-auto"></div>
                  <p className="mt-2 text-gray-600 text-sm">Loading photos...</p>
                </div>
              </CardContent>
            </Card>
          ) : photos && photos.length > 0 ? (
            <PhotoGallery
              photos={photos}
              title={`Photo Gallery of ${monthNames[selectedMonth - 1]} ${selectedYear} Events`}
              description="Photos from field activities and visits during this period"
              maxDisplayPhotos={6}
              showValidationStatus={true}
              allowDownload={true}
              gridCols={3}
              aspectRatio="video"
            />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Camera className="h-5 w-5" />
                  <span>Photo Gallery of {monthNames[selectedMonth - 1]} {selectedYear} Events</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {Array.from({ length: 6 }, (_, index) => (
                    <div key={index} className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                      <div className="text-center text-gray-500">
                        <Camera className="h-8 w-8 mx-auto mb-2" />
                        <p className="text-sm">Photo {index + 1}</p>
                      </div>
                    </div>
                  ))}
                </div>
                <p className="text-sm text-gray-500 mt-4">
                  * Photos will be automatically populated from field visit reports and activities
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </PageLayout>
  );
};

export default SimplifiedImpactReports;
