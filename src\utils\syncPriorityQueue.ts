/**
 * Sync Priority Queue Manager
 * Advanced queue management for offline sync operations with intelligent batching
 */

import type { SyncOperation, SyncResult, SyncBatch } from '@/workers/offlineSyncWorker';
import { toast } from 'sonner';

// ============================================================================
// TYPES
// ============================================================================

export interface QueueConfig {
  maxBatchSize: number;
  maxQueueSize: number;
  batchTimeout: number; // ms to wait before processing incomplete batch
  retryDelay: number; // base delay for retries
  maxRetries: number;
  priorityWeights: Record<SyncOperation['priority'], number>;
}

export interface QueueStats {
  totalOperations: number;
  pendingOperations: number;
  processingOperations: number;
  completedOperations: number;
  failedOperations: number;
  averageProcessingTime: number;
  successRate: number;
  queueUtilization: number;
}

export interface BatchingStrategy {
  type: 'TIME_BASED' | 'SIZE_BASED' | 'PRIORITY_BASED' | 'DEPENDENCY_BASED';
  config: Record<string, unknown>;
}

// ============================================================================
// PRIORITY QUEUE IMPLEMENTATION
// ============================================================================

class PriorityQueue<T> {
  private items: Array<{ item: T; priority: number; timestamp: number }> = [];

  enqueue(item: T, priority: number): void {
    const queueElement = { item, priority, timestamp: Date.now() };
    let inserted = false;

    // Insert based on priority, then timestamp for same priority
    for (let i = 0; i < this.items.length; i++) {
      const current = this.items[i];
      if (priority > current.priority || 
          (priority === current.priority && queueElement.timestamp < current.timestamp)) {
        this.items.splice(i, 0, queueElement);
        inserted = true;
        break;
      }
    }

    if (!inserted) {
      this.items.push(queueElement);
    }
  }

  dequeue(): T | undefined {
    return this.items.shift()?.item;
  }

  peek(): T | undefined {
    return this.items[0]?.item;
  }

  size(): number {
    return this.items.length;
  }

  isEmpty(): boolean {
    return this.items.length === 0;
  }

  clear(): void {
    this.items = [];
  }

  toArray(): T[] {
    return this.items.map(item => item.item);
  }

  removeById(predicate: (item: T) => boolean): T | undefined {
    const index = this.items.findIndex(queueItem => predicate(queueItem.item));
    if (index !== -1) {
      return this.items.splice(index, 1)[0].item;
    }
    return undefined;
  }

  filter(predicate: (item: T) => boolean): T[] {
    return this.items.filter(queueItem => predicate(queueItem.item)).map(queueItem => queueItem.item);
  }
}

// ============================================================================
// SYNC PRIORITY QUEUE MANAGER
// ============================================================================

export class SyncPriorityQueueManager {
  private queue: PriorityQueue<SyncOperation>;
  private processingOperations: Map<string, SyncOperation>;
  private completedOperations: Map<string, SyncResult>;
  private failedOperations: Map<string, SyncResult>;
  private batchQueue: SyncBatch[];
  private config: QueueConfig;
  private batchTimer: NodeJS.Timeout | null = null;
  private worker: Worker | null = null;
  private isProcessing = false;
  private stats: QueueStats;
  private listeners: Map<string, (event: unknown) => void> = new Map();

  constructor(config?: Partial<QueueConfig>) {
    this.queue = new PriorityQueue<SyncOperation>();
    this.processingOperations = new Map();
    this.completedOperations = new Map();
    this.failedOperations = new Map();
    this.batchQueue = [];
    
    this.config = {
      maxBatchSize: 10,
      maxQueueSize: 1000,
      batchTimeout: 5000, // 5 seconds
      retryDelay: 1000, // 1 second
      maxRetries: 3,
      priorityWeights: {
        CRITICAL: 100,
        HIGH: 75,
        MEDIUM: 50,
        LOW: 25,
      },
      ...config,
    };

    this.stats = this.initializeStats();
    this.initializeWorker();
  }

  /**
   * Initialize the sync worker
   */
  private initializeWorker(): void {
    try {
      this.worker = new Worker(
        new URL('../workers/offlineSyncWorker.ts', import.meta.url),
        { type: 'module' }
      );

      this.worker.onmessage = (event) => {
        this.handleWorkerMessage(event.data);
      };

      this.worker.onerror = (error) => {
        console.error('Sync worker error:', error);
        this.emit('error', { error: 'Sync worker failed' });
      };

      // Configure worker
      this.worker.postMessage({
        type: 'CONFIGURE',
        payload: {
          supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
          supabaseKey: import.meta.env.VITE_SUPABASE_ANON_KEY,
        }
      });

    } catch (error) {
      console.warn('Sync worker not available:', error);
      this.worker = null;
    }
  }

  /**
   * Handle messages from worker
   */
  private handleWorkerMessage(data: { type: string; payload: unknown }): void {
    const { type, payload } = data;

    switch (type) {
      case 'SYNC_RESULT':
        this.handleSyncResult(payload);
        break;
      case 'SYNC_PROGRESS':
        this.handleSyncProgress(payload);
        break;
      case 'BATCH_COMPLETE':
        this.handleBatchComplete(payload);
        break;
      case 'ERROR':
        this.handleWorkerError(payload);
        break;
      case 'HEALTH_RESPONSE':
        console.log('Sync worker health:', payload);
        break;
    }
  }

  /**
   * Add operation to queue
   */
  addOperation(operation: Omit<SyncOperation, 'id' | 'timestamp' | 'retryCount'>): string {
    if (this.queue.size() >= this.config.maxQueueSize) {
      throw new Error('Sync queue is full');
    }

    const syncOperation: SyncOperation = {
      ...operation,
      id: this.generateOperationId(),
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: operation.maxRetries || this.config.maxRetries,
    };

    const priority = this.config.priorityWeights[operation.priority];
    this.queue.enqueue(syncOperation, priority);

    this.updateStats();
    this.emit('operationAdded', { operation: syncOperation });

    // Start processing if not already running
    if (!this.isProcessing) {
      this.startProcessing();
    }

    return syncOperation.id;
  }

  /**
   * Start processing queue
   */
  private async startProcessing(): Promise<void> {
    if (this.isProcessing || this.queue.isEmpty() || !this.worker) {
      return;
    }

    this.isProcessing = true;
    this.emit('processingStarted', {});

    try {
      while (!this.queue.isEmpty()) {
        const batch = this.createOptimalBatch();
        if (batch.operations.length > 0) {
          await this.processBatch(batch);
        } else {
          break; // No operations ready to process
        }
      }
    } catch (error) {
      console.error('Error processing sync queue:', error);
      this.emit('error', { error });
    } finally {
      this.isProcessing = false;
      this.emit('processingStopped', {});
    }
  }

  /**
   * Create optimal batch from queue
   */
  private createOptimalBatch(): SyncBatch {
    const operations: SyncOperation[] = [];
    const maxBatchSize = this.config.maxBatchSize;
    const processedDependencies = new Set<string>();

    // Add completed operations to dependencies
    this.completedOperations.forEach((_, id) => {
      processedDependencies.add(id);
    });

    // Collect operations that can be processed (dependencies satisfied)
    const availableOperations: SyncOperation[] = [];
    const tempQueue: SyncOperation[] = [];

    while (!this.queue.isEmpty()) {
      const operation = this.queue.dequeue()!;

      // Check if dependencies are satisfied
      const dependenciesSatisfied = !operation.dependencies ||
        operation.dependencies.every(dep => processedDependencies.has(dep));

      if (dependenciesSatisfied) {
        availableOperations.push(operation);
      } else {
        tempQueue.push(operation);
      }
    }

    // Put back operations that can't be processed yet
    tempQueue.forEach(op => {
      this.queue.enqueue(op, this.config.priorityWeights[op.priority]);
    });

    // Optimize batch composition for better performance
    const optimizedOperations = this.optimizeBatchComposition(availableOperations, maxBatchSize);

    // Add selected operations to processing and put back the rest
    optimizedOperations.forEach(op => {
      operations.push(op);
      this.processingOperations.set(op.id, op);
    });

    availableOperations.forEach(op => {
      if (!optimizedOperations.includes(op)) {
        this.queue.enqueue(op, this.config.priorityWeights[op.priority]);
      }
    });

    return {
      id: this.generateBatchId(),
      operations,
      priority: operations.length > 0 ? 
        Math.max(...operations.map(op => this.config.priorityWeights[op.priority])) : 0,
      estimatedDuration: this.estimateBatchDuration(operations),
      dependencies: [],
    };
  }

  /**
   * Optimize batch composition for better performance
   */
  private optimizeBatchComposition(operations: SyncOperation[], maxBatchSize: number): SyncOperation[] {
    if (operations.length <= maxBatchSize) {
      return operations;
    }

    // Group operations by table and operation type for better batching
    const groups = new Map<string, SyncOperation[]>();

    operations.forEach(op => {
      const groupKey = `${op.table}_${op.operation}`;
      if (!groups.has(groupKey)) {
        groups.set(groupKey, []);
      }
      groups.get(groupKey)!.push(op);
    });

    // Sort groups by priority and efficiency
    const sortedGroups = Array.from(groups.entries()).sort((a, b) => {
      const aPriority = Math.max(...a[1].map(op => this.config.priorityWeights[op.priority]));
      const bPriority = Math.max(...b[1].map(op => this.config.priorityWeights[op.priority]));

      if (aPriority !== bPriority) {
        return bPriority - aPriority; // Higher priority first
      }

      // Prefer larger groups for better batching efficiency
      return b[1].length - a[1].length;
    });

    // Select operations for optimal batch
    const selectedOperations: SyncOperation[] = [];

    for (const [, groupOps] of sortedGroups) {
      // Sort operations within group by priority
      const sortedOps = groupOps.sort((a, b) =>
        this.config.priorityWeights[b.priority] - this.config.priorityWeights[a.priority]
      );

      for (const op of sortedOps) {
        if (selectedOperations.length >= maxBatchSize) break;
        selectedOperations.push(op);
      }

      if (selectedOperations.length >= maxBatchSize) break;
    }

    return selectedOperations;
  }

  /**
   * Estimate batch processing duration based on operation types and complexity
   */
  private estimateBatchDuration(operations: SyncOperation[]): number {
    const baseTime = 500; // Base time per operation in ms
    const operationMultipliers = {
      'CREATE': 1.2, // Creates are typically slower
      'UPDATE': 1.0, // Updates are baseline
      'DELETE': 0.6, // Deletes are typically faster
    };

    const tableComplexity = {
      'field_reports': 1.5, // Complex table with many relationships
      'book_distributions': 1.3,
      'tasks': 1.0,
      'profiles': 0.8,
      'schools': 0.9,
    };

    return operations.reduce((total, op) => {
      const operationMultiplier = operationMultipliers[op.operation] || 1.0;
      const tableMultiplier = tableComplexity[op.table as keyof typeof tableComplexity] || 1.0;
      const priorityMultiplier = op.priority === 'CRITICAL' ? 0.8 : 1.0; // Critical ops get priority processing

      return total + (baseTime * operationMultiplier * tableMultiplier * priorityMultiplier);
    }, 0);
  }

  /**
   * Process a batch of operations
   */
  private async processBatch(batch: SyncBatch): Promise<void> {
    if (!this.worker) {
      throw new Error('Sync worker not available');
    }

    this.emit('batchStarted', { batch });

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Batch processing timeout'));
      }, 30000); // 30 second timeout

      const handleBatchComplete = (payload: { batchId: string }) => {
        if (payload.batchId === batch.id) {
          clearTimeout(timeout);
          this.handleBatchComplete(payload);
          resolve();
        }
      };

      // Temporarily listen for this specific batch
      this.listeners.set(`batch_${batch.id}`, handleBatchComplete);

      this.worker!.postMessage({
        type: 'SYNC_BATCH',
        payload: batch,
      });
    });
  }

  /**
   * Handle sync result from worker
   */
  private handleSyncResult(result: SyncResult): void {
    const operation = this.processingOperations.get(result.id);
    if (!operation) return;

    this.processingOperations.delete(result.id);

    if (result.success) {
      this.completedOperations.set(result.id, result);
      this.emit('operationCompleted', { result });
    } else {
      // Handle retry logic
      if (operation.retryCount < operation.maxRetries) {
        operation.retryCount++;
        const delay = this.config.retryDelay * Math.pow(2, operation.retryCount);
        
        setTimeout(() => {
          const priority = this.config.priorityWeights[operation.priority];
          this.queue.enqueue(operation, priority);
          this.emit('operationRetried', { operation, delay });
        }, delay);
      } else {
        this.failedOperations.set(result.id, result);
        this.emit('operationFailed', { result });
      }
    }

    this.updateStats();
  }

  /**
   * Handle sync progress from worker
   */
  private handleSyncProgress(progress: unknown): void {
    this.emit('operationProgress', progress);
  }

  /**
   * Handle batch completion
   */
  private handleBatchComplete(payload: { batchId: string; results: SyncResult[]; summary: unknown }): void {
    const { batchId, results, summary } = payload;
    
    // Clean up batch listener
    this.listeners.delete(`batch_${batchId}`);
    
    this.emit('batchCompleted', { batchId, results, summary });
    
    // Continue processing if there are more operations
    if (!this.queue.isEmpty()) {
      this.startProcessing();
    }
  }

  /**
   * Handle worker errors
   */
  private handleWorkerError(error: unknown): void {
    console.error('Sync worker error:', error);
    this.emit('error', error);
  }

  /**
   * Cancel operation
   */
  cancelOperation(operationId: string): boolean {
    // Try to remove from queue
    const removed = this.queue.removeById(op => op.id === operationId);
    if (removed) {
      this.emit('operationCancelled', { operationId });
      this.updateStats();
      return true;
    }

    // Check if it's currently processing
    if (this.processingOperations.has(operationId)) {
      // Can't cancel operations that are already processing
      return false;
    }

    return false;
  }

  /**
   * Retry failed operations
   */
  retryFailedOperations(): void {
    const failedOps = Array.from(this.failedOperations.values());
    this.failedOperations.clear();

    failedOps.forEach(result => {
      const operation = { ...result.operation };
      operation.retryCount = 0; // Reset retry count
      const priority = this.config.priorityWeights[operation.priority];
      this.queue.enqueue(operation, priority);
    });

    this.updateStats();
    this.emit('failedOperationsRetried', { count: failedOps.length });

    if (!this.isProcessing) {
      this.startProcessing();
    }
  }

  /**
   * Clear completed operations
   */
  clearCompleted(): void {
    const count = this.completedOperations.size;
    this.completedOperations.clear();
    this.updateStats();
    this.emit('completedOperationsCleared', { count });
  }

  /**
   * Get queue statistics
   */
  getStats(): QueueStats {
    this.updateStats();
    return { ...this.stats };
  }

  /**
   * Get operation status
   */
  getOperationStatus(operationId: string): 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'NOT_FOUND' {
    if (this.queue.filter(op => op.id === operationId).length > 0) {
      return 'PENDING';
    }
    if (this.processingOperations.has(operationId)) {
      return 'PROCESSING';
    }
    if (this.completedOperations.has(operationId)) {
      return 'COMPLETED';
    }
    if (this.failedOperations.has(operationId)) {
      return 'FAILED';
    }
    return 'NOT_FOUND';
  }

  /**
   * Event handling
   */
  on(event: string, callback: (data: unknown) => void): void {
    this.listeners.set(event, callback);
  }

  off(event: string): void {
    this.listeners.delete(event);
  }

  private emit(event: string, data: unknown): void {
    const callback = this.listeners.get(event);
    if (callback) {
      callback(data);
    }
  }

  /**
   * Utility methods
   */
  private generateOperationId(): string {
    return `sync_op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateBatchId(): string {
    return `sync_batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initializeStats(): QueueStats {
    return {
      totalOperations: 0,
      pendingOperations: 0,
      processingOperations: 0,
      completedOperations: 0,
      failedOperations: 0,
      averageProcessingTime: 0,
      successRate: 0,
      queueUtilization: 0,
    };
  }

  private updateStats(): void {
    const total = this.queue.size() + this.processingOperations.size + 
                  this.completedOperations.size + this.failedOperations.size;
    
    const completed = this.completedOperations.size;
    const failed = this.failedOperations.size;
    const totalProcessed = completed + failed;

    this.stats = {
      totalOperations: total,
      pendingOperations: this.queue.size(),
      processingOperations: this.processingOperations.size,
      completedOperations: completed,
      failedOperations: failed,
      averageProcessingTime: this.calculateAverageProcessingTime(),
      successRate: totalProcessed > 0 ? (completed / totalProcessed) * 100 : 0,
      queueUtilization: (this.queue.size() / this.config.maxQueueSize) * 100,
    };
  }

  private calculateAverageProcessingTime(): number {
    const completedOps = Array.from(this.completedOperations.values());
    if (completedOps.length === 0) return 0;
    
    const totalTime = completedOps.reduce((sum, op) => sum + op.duration, 0);
    return totalTime / completedOps.length;
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
    }
    
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
    
    this.queue.clear();
    this.processingOperations.clear();
    this.listeners.clear();
  }
}

// Export singleton instance
export const syncQueue = new SyncPriorityQueueManager();
