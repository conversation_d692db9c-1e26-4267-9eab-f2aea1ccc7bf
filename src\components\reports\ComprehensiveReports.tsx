import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  FileText,
  Users,
  Award,
  Download,
  Calendar
} from 'lucide-react';
import { PageLayout, PageHeader } from '@/components/layout';

// Import existing report components
import AttendanceReportsHub from '@/components/attendance/reports/AttendanceReportsHub';
import ImpactReports from '@/components/impact/analytics/ImpactReports';

interface ComprehensiveReportsProps {
  defaultTab?: string;
}

const ComprehensiveReports: React.FC<ComprehensiveReportsProps> = ({
  defaultTab = 'impact'
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab);

  const reportCategories = [
    {
      id: 'impact',
      title: 'Impact Reports',
      description: 'Student outcomes, school performance, and long-term impact',
      icon: Award,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      id: 'attendance',
      title: 'Attendance Reports',
      description: 'Student attendance, session reports, and participation analytics',
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    }
  ];

  const quickActions = [
    {
      label: 'Export All Data',
      icon: Download,
      action: () => console.log('Export all data'),
    },
    {
      label: 'Schedule Reports',
      icon: Calendar,
      action: () => console.log('Schedule reports'),
    },
    {
      label: 'Generate Summary',
      icon: FileText,
      action: () => console.log('Generate summary'),
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'impact':
        return (
          <ImpactReports
            schoolId={null}
            dateRange={{ start: new Date(new Date().getFullYear(), 0, 1), end: new Date() }}
            canViewAllData={true}
          />
        );

      case 'attendance':
        return <AttendanceReportsHub />;

      default:
        return <ImpactReports
          schoolId={null}
          dateRange={{ start: new Date(new Date().getFullYear(), 0, 1), end: new Date() }}
          canViewAllData={true}
        />;
    }
  };

  return (
    <PageLayout>
      <PageHeader
        title="Reports"
        description="Comprehensive reporting across all application modules"
        icon={FileText}
      />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          {reportCategories.map((category) => (
            <TabsTrigger key={category.id} value={category.id} className="text-sm">
              {category.title}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {renderTabContent()}
        </TabsContent>
      </Tabs>
    </PageLayout>
  );
};

export default ComprehensiveReports;
