/**
 * Attendance Summary Statistics Component
 * Displays key metrics and statistics for attendance data
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  Users, 
  School, 
  Building, 
  CheckCircle, 
  AlertCircle,
  BarChart3,
  MapPin,
  Loader2
} from 'lucide-react';
import {
  AttendanceSummaryStats,
  AttendanceFilters,
} from '@/hooks/useAttendanceTracking';

interface AttendanceSummaryStatsProps {
  stats: AttendanceSummaryStats;
  isLoading?: boolean;
  filters: AttendanceFilters;
  className?: string;
}

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ComponentType<{ className?: string }>;
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'gray';
  badge?: {
    label: string;
    variant?: 'default' | 'secondary' | 'outline';
  };
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon: Icon,
  color = 'blue',
  badge,
}) => {
  const colorClasses = {
    blue: 'bg-blue-50 border-blue-200 text-blue-800',
    green: 'bg-green-50 border-green-200 text-green-800',
    purple: 'bg-purple-50 border-purple-200 text-purple-800',
    orange: 'bg-orange-50 border-orange-200 text-orange-800',
    red: 'bg-red-50 border-red-200 text-red-800',
    gray: 'bg-gray-50 border-gray-200 text-gray-800',
  };

  const iconColorClasses = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    purple: 'text-purple-600',
    orange: 'text-orange-600',
    red: 'text-red-600',
    gray: 'text-gray-600',
  };

  return (
    <Card className={`${colorClasses[color]} border-2`}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg bg-white/50 ${iconColorClasses[color]}`}>
              <Icon className="h-5 w-5" />
            </div>
            <div>
              <p className="text-sm font-medium opacity-80">{title}</p>
              <p className="text-2xl font-bold">{value}</p>
              {subtitle && (
                <p className="text-xs opacity-70 mt-1">{subtitle}</p>
              )}
            </div>
          </div>
          {badge && (
            <Badge variant={badge.variant || 'secondary'} className="text-xs">
              {badge.label}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

const AttendanceSummaryStatsComponent: React.FC<AttendanceSummaryStatsProps> = ({
  stats,
  isLoading = false,
  filters,
  className = '',
}) => {
  const formatHours = (hours: number) => {
    if (hours === 0) return '0h';
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    return `${hours.toFixed(1)}h`;
  };

  const formatDuration = (minutes: number) => {
    if (minutes === 0) return '0m';
    if (minutes < 60) return `${Math.round(minutes)}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = Math.round(minutes % 60);
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  const getFilterDescription = () => {
    const parts = [];
    if (filters.startDate || filters.endDate) {
      if (filters.startDate && filters.endDate) {
        parts.push(`${filters.startDate} to ${filters.endDate}`);
      } else if (filters.startDate) {
        parts.push(`from ${filters.startDate}`);
      } else {
        parts.push(`until ${filters.endDate}`);
      }
    }
    if (filters.staffId) {
      parts.push('filtered by staff');
    }
    if (filters.checkInType) {
      parts.push(`${filters.checkInType} check-ins only`);
    }
    return parts.length > 0 ? parts.join(', ') : 'all records';
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2 text-gray-500">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span>Loading statistics...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Attendance Summary
          </CardTitle>
          <CardDescription>
            Statistics for {getFilterDescription()}
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Main Statistics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total Records"
          value={stats.total_records.toLocaleString()}
          subtitle="Attendance entries"
          icon={Clock}
          color="blue"
        />

        <StatCard
          title="Staff Members"
          value={stats.total_staff}
          subtitle="Unique staff"
          icon={Users}
          color="green"
        />

        <StatCard
          title="Total Hours"
          value={formatHours(stats.total_hours)}
          subtitle="Time tracked"
          icon={Clock}
          color="purple"
        />

        <StatCard
          title="Avg Duration"
          value={formatDuration(stats.average_duration_minutes)}
          subtitle="Per session"
          icon={BarChart3}
          color="orange"
        />
      </div>

      {/* Check-in Type Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <StatCard
          title="School Visits"
          value={stats.school_checkins.toLocaleString()}
          subtitle={`${((stats.school_checkins / Math.max(stats.total_records, 1)) * 100).toFixed(1)}% of total`}
          icon={School}
          color="blue"
          badge={{
            label: 'Field Work',
            variant: 'outline',
          }}
        />

        <StatCard
          title="Office Check-ins"
          value={stats.office_checkins.toLocaleString()}
          subtitle={`${((stats.office_checkins / Math.max(stats.total_records, 1)) * 100).toFixed(1)}% of total`}
          icon={Building}
          color="purple"
          badge={{
            label: 'Administrative',
            variant: 'outline',
          }}
        />
      </div>

      {/* Status Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <StatCard
          title="Completed Sessions"
          value={stats.completed_sessions.toLocaleString()}
          subtitle={`${((stats.completed_sessions / Math.max(stats.total_records, 1)) * 100).toFixed(1)}% completion rate`}
          icon={CheckCircle}
          color="green"
        />

        <StatCard
          title="Active Sessions"
          value={stats.active_sessions.toLocaleString()}
          subtitle="Currently checked in"
          icon={AlertCircle}
          color="orange"
        />
      </div>



      {/* No Data State */}
      {stats.total_records === 0 && (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center text-gray-500">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No data available</h3>
              <p className="text-sm">
                No attendance records found for the selected filters.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AttendanceSummaryStatsComponent;
