// Quick test script for user creation Edge Functions
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function testUserCreation() {
  console.log('🧪 Testing User Creation Edge Functions...\n');

  try {
    // First, authenticate as an admin user
    console.log('1. Authenticating...');
    
    // You'll need to replace with actual admin credentials
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>', // Replace with actual admin email
      password: 'your-password' // Replace with actual password
    });

    if (authError) {
      console.error('❌ Authentication failed:', authError.message);
      console.log('\n📝 To test user creation:');
      console.log('1. Update the email/password in scripts/test-user-creation.js');
      console.log('2. Or test manually in the browser at http://localhost:5173');
      return;
    }

    console.log('✅ Authenticated successfully as:', authData.user.email);

    // Test the create-user function
    console.log('\n2. Testing create-user function...');
    
    const testUser = {
      email: `test-${Date.now()}@example.com`,
      name: 'Test User',
      role: 'field_staff',
      phone: '+256700000000'
    };

    console.log('📤 Sending request to create-user function...');
    const { data: result, error } = await supabase.functions.invoke('create-user', {
      body: testUser
    });

    if (error) {
      console.error('❌ Function call failed:', error);
    } else if (result.success) {
      console.log('✅ User created successfully!');
      console.log('   Email:', result.user.email);
      console.log('   Name:', result.user.name);
      console.log('   Role:', result.user.role);
      console.log('   Temp Password:', result.user.tempPassword);
    } else {
      console.error('❌ User creation failed:', result.error);
    }

    console.log('\n🎉 Test completed!');
    console.log('\n📝 Next steps:');
    console.log('1. Try creating a user in the browser at http://localhost:5173');
    console.log('2. Go to Staff Management > Create User');
    console.log('3. Fill out the form and submit');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure the development server is running: npm run dev');
    console.log('2. Check that Edge Functions are deployed');
    console.log('3. Verify your Supabase credentials in .env');
  }
}

// Run the test
testUserCreation().catch(console.error);
