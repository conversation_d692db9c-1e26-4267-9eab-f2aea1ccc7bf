import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/types/database.types';
import { useAuth } from '@/hooks/useAuth';

export type FieldActivityType = Database['public']['Enums']['field_activity_type'];

export interface ActivityDistributionResult {
  byType: Record<string, number>;
  total: number;
}

export const useActivityDistribution = (daysBack: number = 30) => {
  const { profile } = useAuth();

  return useQuery<ActivityDistributionResult>({
    queryKey: ['field-activity-distribution', daysBack, profile?.role],
    queryFn: async () => {
      const fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - daysBack);
      const from = fromDate.toISOString().slice(0, 10);

      // Fetch only the minimal columns needed and aggregate on the client
      const { data, error } = await supabase
        .from('enhanced_field_reports')
        .select('activity_type, report_date')
        .gte('report_date', from);

      if (error) {
        throw error;
      }

      const byType: Record<string, number> = {};
      for (const row of (data || []) as Array<{ activity_type: FieldActivityType }>) {
        const type = row.activity_type;
        byType[type] = (byType[type] || 0) + 1;
      }

      const total = Object.values(byType).reduce((sum, n) => sum + n, 0);
      return { byType, total };
    },
    enabled: !!profile?.role,
    staleTime: 60 * 1000,
  });
};

