import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  MapPin,
  Clock,
  Users,
  FileText,
  RefreshCw
} from 'lucide-react';
import { ActivitySummary } from '@/hooks/dashboard/useDashboardMetrics';

interface RealTimeActivityMonitorProps {
  activitySummary: ActivitySummary;
  isLoading?: boolean;
  onRefresh?: () => void;
}

interface ActivityCardProps {
  title: string;
  value: number;
  icon: React.ElementType;
  color: string;
  subtitle?: string;
}

const ActivityCard: React.FC<ActivityCardProps> = ({
  title,
  value,
  icon: Icon,
  color,
  subtitle
}) => (
  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
    <div className={`p-2 rounded-full ${color}`}>
      <Icon className="h-4 w-4 text-white" />
    </div>
    <div>
      <p className="text-2xl font-bold text-gray-900">{value}</p>
      <p className="text-sm text-gray-600">{title}</p>
      {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
    </div>
  </div>
);



export const RealTimeActivityMonitor: React.FC<RealTimeActivityMonitorProps> = ({
  activitySummary,
  isLoading,
  onRefresh
}) => {
  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Today's Activities</span>
              <div className="h-8 w-8 bg-gray-200 rounded animate-pulse" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-100 rounded-lg animate-pulse" />
              ))}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Live Staff Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-100 rounded-lg animate-pulse" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const todayActivities = [
    {
      title: 'Check-ins',
      value: activitySummary.todayActivities.checkIns,
      icon: MapPin,
      color: 'bg-green-500',
    },
    {
      title: 'Sessions',
      value: activitySummary.todayActivities.sessions,
      icon: Users,
      color: 'bg-blue-500',
    },
    {
      title: 'Reports',
      value: activitySummary.todayActivities.reports,
      icon: FileText,
      color: 'bg-purple-500',
    },
    {
      title: 'School Visits',
      value: activitySummary.todayActivities.visits,
      icon: Clock,
      color: 'bg-orange-500',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Today's Activities */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Today's Activities</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={onRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {todayActivities.map((activity, index) => (
              <ActivityCard key={index} {...activity} />
            ))}
          </div>
        </CardContent>
      </Card>


    </div>
  );
};
