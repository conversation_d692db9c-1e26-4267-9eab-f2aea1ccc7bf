# iLead Field Track - Deployment Guide

## 🚀 Production Deployment

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase project with proper configuration
- Deployment platform (Vercel, Netlify, etc.)

### Environment Variables Setup

#### Required Environment Variables
```bash
# Supabase Configuration (Required)
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here
VITE_SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Application Configuration
VITE_APP_NAME=iLead Field Track
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=production
```

#### Optional Environment Variables
```bash
# Feature Flags
VITE_ENABLE_DEBUG_MODE=false
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true

# Performance Settings
VITE_API_TIMEOUT=30000
VITE_MAX_FILE_SIZE=10485760
VITE_MAX_PHOTO_SIZE=5242880

# GPS Configuration
VITE_GPS_TIMEOUT=15000
VITE_GPS_MAX_AGE=300000
VITE_GPS_HIGH_ACCURACY=true

# Offline Sync
VITE_SYNC_INTERVAL=300000
VITE_MAX_OFFLINE_STORAGE=104857600
VITE_RETRY_ATTEMPTS=3
```

### Deployment Platforms

#### Vercel Deployment
1. **Connect Repository**
   ```bash
   # Install Vercel CLI
   npm i -g vercel
   
   # Deploy
   vercel --prod
   ```

2. **Set Environment Variables**
   - Go to Vercel Dashboard > Project > Settings > Environment Variables
   - Add all required environment variables
   - Set `VITE_APP_ENVIRONMENT=production`

#### Netlify Deployment
1. **Build Settings**
   ```bash
   # Build command
   npm run build
   
   # Publish directory
   dist
   ```

2. **Environment Variables**
   - Go to Netlify Dashboard > Site Settings > Environment Variables
   - Add all required environment variables

#### Manual Deployment
```bash
# 1. Install dependencies
npm install

# 2. Set environment variables
cp .env.production .env

# 3. Build for production
npm run build

# 4. Deploy dist/ folder to your hosting provider
```

### Security Checklist

#### ✅ Environment Variables
- [ ] Service role key is set via deployment platform (not in code)
- [ ] All sensitive keys are properly configured
- [ ] Debug mode is disabled in production
- [ ] Environment is set to 'production'

#### ✅ Supabase Configuration
- [ ] Row Level Security (RLS) is enabled on all tables
- [ ] Proper authentication policies are in place
- [ ] Service role key is restricted to necessary operations
- [ ] Database backups are configured

#### ✅ Application Security
- [ ] HTTPS is enforced
- [ ] Content Security Policy is configured
- [ ] Error reporting doesn't expose sensitive data
- [ ] File upload limits are properly set

### Performance Optimization

#### Build Optimization
```bash
# Analyze bundle size
npm run build -- --analyze

# Check for unused dependencies
npx depcheck
```

#### Runtime Performance
- GPS polling is optimized for battery usage
- Offline sync is configured with appropriate intervals
- Image compression is enabled for photo uploads
- Database queries use proper indexing

### Monitoring and Maintenance

#### Health Checks
- Monitor application errors via error reporting
- Track user engagement through analytics
- Monitor database performance
- Check offline sync success rates

#### Regular Maintenance
- Update dependencies monthly
- Review and rotate API keys quarterly
- Monitor storage usage and cleanup old data
- Review user feedback and bug reports

### Troubleshooting

#### Common Issues

**1. "Admin operations not available" Error**
```bash
# Solution: Set the service role key
VITE_SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

**2. Build Failures**
```bash
# Check environment variables
npm run build:check

# Validate configuration
npm run validate:config
```

**3. Authentication Issues**
- Verify Supabase URL and anon key
- Check RLS policies
- Ensure proper user roles are configured

#### Support
For deployment issues:
1. Check the browser console for errors
2. Verify all environment variables are set
3. Test with a fresh Supabase project
4. Review the application logs

### Post-Deployment Verification

#### Functional Tests
- [ ] User authentication works
- [ ] User creation by admins/program officers
- [ ] Role-based access control functions
- [ ] GPS check-in/check-out works
- [ ] Offline sync operates correctly
- [ ] Photo uploads function properly
- [ ] Reports generate successfully

#### Performance Tests
- [ ] Page load times < 3 seconds
- [ ] GPS acquisition < 15 seconds
- [ ] Photo uploads complete successfully
- [ ] Offline mode works without internet
- [ ] Sync resumes when connection restored

---

## 📱 Mobile Deployment (PWA)

The application is configured as a Progressive Web App (PWA) and can be installed on mobile devices.

### PWA Features
- Offline functionality
- GPS location services
- Photo capture and upload
- Push notifications (if configured)
- App-like experience on mobile

### Installation
Users can install the app by:
1. Opening the web app in their mobile browser
2. Tapping "Add to Home Screen" or "Install App"
3. Following the browser prompts

---

*Last updated: January 2025*
