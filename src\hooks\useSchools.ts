import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { usePaginatedQuery } from './usePaginatedQuery';

export interface School {
  id: string;
  name: string;
  code?: string;
  school_type?: string;
  registration_status?: string;
  district?: string;
  sub_county?: string;
  student_count?: number;
  teacher_count?: number;
  contact_phone?: string;
  email?: string;
}

// Legacy hook for backward compatibility (limited to 100 schools)
export const useSchools = () => {
  return useQuery({
    queryKey: ['schools'],
    queryFn: async () => {
      console.log('Fetching schools for attendance components...');

      // Try RPC function first, fallback to direct table query
      try {
        const { data: rpcData, error: rpcError } = await supabase
          .rpc('get_schools_with_divisions');

        if (!rpcError && rpcData) {
          console.log('✅ Schools fetched via RPC successfully:', rpcData?.length || 0, 'schools');
          return (rpcData || []) as School[];
        }
      } catch (rpcError) {
        console.warn('RPC function failed, falling back to direct query:', rpcError);
      }

      // Fallback to direct table query with limit for performance
      const { data, error } = await supabase
        .from('schools')
        .select(`
          id,
          name,
          code,
          school_type,
          registration_status,
          district,
          sub_county,
          contact_phone,
          email
        `)
        .in('registration_status', ['active', 'pending'])
        .order('name')
        .limit(100); // Limit to prevent performance issues

      if (error) {
        console.error('Error fetching schools:', error);
        throw error;
      }

      console.log('✅ Schools fetched via direct query successfully:', data?.length || 0, 'schools');
      return (data || []) as School[];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// New paginated hook for better performance
export const usePaginatedSchools = (options: {
  pageSize?: number;
  searchTerm?: string;
  registrationStatus?: string[];
  schoolType?: string[];
  district?: string;
} = {}) => {
  const {
    pageSize = 20,
    searchTerm,
    registrationStatus = ['active', 'pending'],
    schoolType,
    district,
  } = options;

  const filters: Record<string, unknown> = {
    registration_status: registrationStatus,
  };

  if (schoolType && schoolType.length > 0) {
    filters.school_type = schoolType;
  }

  if (district) {
    filters.district = district;
  }

  return usePaginatedQuery<School>({
    table: 'schools',
    select: `
      id,
      name,
      code,
      school_type,
      registration_status,
      district,
      sub_county,
      contact_phone,
      email
    `,
    filters,
    orderBy: [{ column: 'name', ascending: true }],
    searchColumn: 'name',
    pagination: {
      pageSize,
      prefetchNextPage: true,
    },
    queryKey: ['schools-paginated'],
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
