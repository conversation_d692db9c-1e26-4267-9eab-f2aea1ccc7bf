import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, MessageSquare, User, School, MoreHorizontal, Trash2 } from 'lucide-react';
import TaskStatusBadge from './TaskStatusBadge';
import TaskPriorityBadge from './TaskPriorityBadge';
import { Database } from '@/integrations/supabase/types';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

type Task = {
  id: string;
  title: string;
  description: string | null;
  priority: Database['public']['Enums']['task_priority'];
  status: Database['public']['Enums']['task_status'];
  due_date: string | null;
  assigned_to: string | null;
  assigned_to_name: string | null;
  created_by: string;
  created_by_name: string;
  school_id: string | null;
  school_name: string | null;
  created_at: string;
  updated_at: string;
  comment_count: number;
};

interface TaskCardProps {
  task: Task;
  currentUserId?: string;
  onViewDetails: (taskId: string) => void;
  onUpdateStatus: (taskId: string, status: Database['public']['Enums']['task_status']) => void;
  onEdit?: (taskId: string) => void;
  onDelete?: (taskId: string) => void;
  canDelete?: boolean;
  className?: string;
}

const TaskCard: React.FC<TaskCardProps> = ({
  task,
  currentUserId,
  onViewDetails,
  onUpdateStatus,
  onEdit,
  onDelete,
  canDelete = false,
  className
}) => {
  const formatDate = (dateString: string | null) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const isOverdue = task.due_date && new Date(task.due_date) < new Date() && task.status !== 'completed';
  const canEdit = currentUserId === task.created_by || currentUserId === task.assigned_to;

  const handleStatusChange = (newStatus: Database['public']['Enums']['task_status']) => {
    onUpdateStatus(task.id, newStatus);
  };

  const handleViewDetails = () => {
    onViewDetails(task.id);
  };

  const handleDelete = () => {
    if (onDelete && window.confirm(`Are you sure you want to delete the task "${task.title}"? This action cannot be undone.`)) {
      onDelete(task.id);
    }
  };

  return (
    <Card className={`hover:shadow-md transition-shadow cursor-pointer ${className || ''} ${isOverdue ? 'border-red-200 bg-red-50' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0" onClick={handleViewDetails}>
            <h3 className="font-semibold text-gray-900 truncate hover:text-ilead-green">{task.title}</h3>
            <div className="flex items-center gap-2 mt-1">
              <TaskPriorityBadge priority={task.priority} />
              <TaskStatusBadge status={task.status} />
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-10 w-10 p-0 touch-manipulation">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleViewDetails}>
                View Details
              </DropdownMenuItem>
              {canEdit && task.status !== 'completed' && (
                <>
                  {task.status === 'pending' && (
                    <DropdownMenuItem onClick={() => handleStatusChange('in_progress')}>
                      Start Task
                    </DropdownMenuItem>
                  )}
                  {task.status === 'in_progress' && (
                    <DropdownMenuItem onClick={() => handleStatusChange('completed')}>
                      Mark Complete
                    </DropdownMenuItem>
                  )}
                  {onEdit && (
                    <DropdownMenuItem onClick={() => onEdit(task.id)}>
                      Edit Task
                    </DropdownMenuItem>
                  )}
                </>
              )}
              {canDelete && onDelete && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={handleDelete}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Task
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pt-0" onClick={handleViewDetails}>
        {task.description && (
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {task.description}
          </p>
        )}
        
        <div className="space-y-2 text-sm text-gray-500">
          {task.assigned_to_name && (
            <div className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span>Assigned to: {task.assigned_to_name}</span>
            </div>
          )}
          
          {task.school_name && (
            <div className="flex items-center gap-2">
              <School className="h-4 w-4" />
              <span>{task.school_name}</span>
            </div>
          )}
          
          {task.due_date && (
            <div className={`flex items-center gap-2 ${isOverdue ? 'text-red-600 font-medium' : ''}`}>
              <Calendar className="h-4 w-4" />
              <span>Due: {formatDate(task.due_date)}</span>
              {isOverdue && <span className="text-red-600">(Overdue)</span>}
            </div>
          )}
          
          {task.comment_count > 0 && (
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              <span>{task.comment_count} comment{task.comment_count !== 1 ? 's' : ''}</span>
            </div>
          )}
        </div>
        
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-gray-400">
            <span>Created by {task.created_by_name}</span>
            <span>{formatDate(task.created_at)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TaskCard;
