/**
 * Subscription Performance Monitor Hook
 * Monitors real-time subscription health and performance metrics
 */

import { useState, useEffect, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { realtimeSubscriptionManager, type ConnectionPoolStats, type SubscriptionHealth } from '@/services/realtimeSubscriptionManager';

// ============================================================================
// TYPES
// ============================================================================

export interface SubscriptionMetrics {
  connectionStats: ConnectionPoolStats;
  healthStatus: SubscriptionHealth[];
  cleanupStats: {
    totalSubscriptions: number;
    totalCallbacks: number;
    emptySubscriptions: number;
    staleConnections: number;
    lastCleanup: number;
  };
  performanceAlerts: PerformanceAlert[];
}

export interface PerformanceAlert {
  id: string;
  type: 'warning' | 'error' | 'critical';
  message: string;
  timestamp: number;
  metric: string;
  value: number;
  threshold: number;
}

export interface SubscriptionPerformanceConfig {
  latencyThreshold: number; // ms
  errorRateThreshold: number; // percentage
  connectionTimeoutThreshold: number; // ms
  monitoringInterval: number; // ms
}

// ============================================================================
// SUBSCRIPTION MONITOR HOOK
// ============================================================================

export const useSubscriptionMonitor = (config?: Partial<SubscriptionPerformanceConfig>) => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [performanceHistory, setPerformanceHistory] = useState<{
    timestamp: number;
    latency: number;
    errorRate: number;
    connectionCount: number;
  }[]>([]);

  const defaultConfig: SubscriptionPerformanceConfig = {
    latencyThreshold: 500, // 500ms
    errorRateThreshold: 5, // 5%
    connectionTimeoutThreshold: 30000, // 30 seconds
    monitoringInterval: 10000, // 10 seconds
    ...config,
  };

  // ============================================================================
  // METRICS QUERY
  // ============================================================================

  const {
    data: metrics,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['subscription-metrics'],
    queryFn: async (): Promise<SubscriptionMetrics> => {
      const connectionStats = realtimeSubscriptionManager.getConnectionStats();
      const healthStatus = realtimeSubscriptionManager.getHealthStatus();
      const cleanupStats = realtimeSubscriptionManager.getCleanupStats();

      // Generate performance alerts based on current metrics
      const performanceAlerts = generatePerformanceAlerts(connectionStats, healthStatus, defaultConfig);

      return {
        connectionStats,
        healthStatus,
        cleanupStats,
        performanceAlerts,
      };
    },
    enabled: isMonitoring,
    refetchInterval: defaultConfig.monitoringInterval,
    staleTime: defaultConfig.monitoringInterval / 2,
  });

  // ============================================================================
  // ALERT GENERATION
  // ============================================================================

  const generatePerformanceAlerts = useCallback((
    connectionStats: ConnectionPoolStats,
    healthStatus: SubscriptionHealth[],
    config: SubscriptionPerformanceConfig
  ): PerformanceAlert[] => {
    const alerts: PerformanceAlert[] = [];
    const now = Date.now();

    // Check average latency
    if (connectionStats.averageLatency > config.latencyThreshold) {
      alerts.push({
        id: `latency_${now}`,
        type: connectionStats.averageLatency > config.latencyThreshold * 2 ? 'critical' : 'warning',
        message: `High subscription latency detected: ${connectionStats.averageLatency.toFixed(1)}ms`,
        timestamp: now,
        metric: 'latency',
        value: connectionStats.averageLatency,
        threshold: config.latencyThreshold,
      });
    }

    // Check error rate
    if (connectionStats.errorRate > config.errorRateThreshold) {
      alerts.push({
        id: `error_rate_${now}`,
        type: connectionStats.errorRate > config.errorRateThreshold * 2 ? 'critical' : 'warning',
        message: `High subscription error rate: ${connectionStats.errorRate.toFixed(1)}%`,
        timestamp: now,
        metric: 'errorRate',
        value: connectionStats.errorRate,
        threshold: config.errorRateThreshold,
      });
    }

    // Check connection health
    const disconnectedConnections = healthStatus.filter(health => !health.isConnected);
    if (disconnectedConnections.length > 0) {
      alerts.push({
        id: `disconnected_${now}`,
        type: 'error',
        message: `${disconnectedConnections.length} subscription connection(s) disconnected`,
        timestamp: now,
        metric: 'connectionHealth',
        value: disconnectedConnections.length,
        threshold: 0,
      });
    }

    // Check for stale connections
    const staleConnections = healthStatus.filter(health => 
      (now - health.lastHeartbeat) > config.connectionTimeoutThreshold
    );
    if (staleConnections.length > 0) {
      alerts.push({
        id: `stale_${now}`,
        type: 'warning',
        message: `${staleConnections.length} stale subscription connection(s) detected`,
        timestamp: now,
        metric: 'staleConnections',
        value: staleConnections.length,
        threshold: 0,
      });
    }

    return alerts;
  }, []);

  // ============================================================================
  // PERFORMANCE TRACKING
  // ============================================================================

  const updatePerformanceHistory = useCallback(() => {
    if (!metrics) return;

    const newEntry = {
      timestamp: Date.now(),
      latency: metrics.connectionStats.averageLatency,
      errorRate: metrics.connectionStats.errorRate,
      connectionCount: metrics.connectionStats.activeConnections,
    };

    setPerformanceHistory(prev => {
      const updated = [...prev, newEntry];
      // Keep only last 100 entries (about 16 minutes at 10-second intervals)
      return updated.slice(-100);
    });
  }, [metrics]);

  // ============================================================================
  // ALERT MANAGEMENT
  // ============================================================================

  const addAlert = useCallback((alert: PerformanceAlert) => {
    setAlerts(prev => {
      // Check if similar alert already exists (within last 5 minutes)
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
      const existingSimilar = prev.find(existing => 
        existing.metric === alert.metric && 
        existing.timestamp > fiveMinutesAgo
      );

      if (existingSimilar) {
        return prev; // Don't add duplicate alerts
      }

      return [alert, ...prev].slice(0, 50); // Keep only last 50 alerts
    });
  }, []);

  const clearAlert = useCallback((alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  }, []);

  const clearAllAlerts = useCallback(() => {
    setAlerts([]);
  }, []);

  // ============================================================================
  // MONITORING CONTROLS
  // ============================================================================

  const startMonitoring = useCallback(() => {
    setIsMonitoring(true);
    console.log('📊 Subscription performance monitoring started');
  }, []);

  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false);
    console.log('📊 Subscription performance monitoring stopped');
  }, []);

  const forceCleanup = useCallback(() => {
    realtimeSubscriptionManager.forceCleanup();
    refetch();
  }, [refetch]);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    if (metrics?.performanceAlerts) {
      metrics.performanceAlerts.forEach(alert => {
        addAlert(alert);
      });
    }
  }, [metrics?.performanceAlerts, addAlert]);

  useEffect(() => {
    updatePerformanceHistory();
  }, [updatePerformanceHistory]);

  // Auto-start monitoring when hook is used
  useEffect(() => {
    startMonitoring();
    return () => {
      stopMonitoring();
    };
  }, [startMonitoring, stopMonitoring]);

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  const healthSummary = {
    overall: metrics ? (
      metrics.connectionStats.activeConnections > 0 && 
      metrics.connectionStats.errorRate < defaultConfig.errorRateThreshold &&
      metrics.connectionStats.averageLatency < defaultConfig.latencyThreshold
        ? 'healthy' 
        : metrics.connectionStats.errorRate > defaultConfig.errorRateThreshold * 2 ||
          metrics.connectionStats.averageLatency > defaultConfig.latencyThreshold * 2
        ? 'critical'
        : 'warning'
    ) : 'unknown',
    activeAlerts: alerts.filter(alert => Date.now() - alert.timestamp < 5 * 60 * 1000).length,
    criticalAlerts: alerts.filter(alert => 
      alert.type === 'critical' && Date.now() - alert.timestamp < 5 * 60 * 1000
    ).length,
  };

  const performanceTrends = {
    latencyTrend: performanceHistory.length > 1 ? 
      performanceHistory[performanceHistory.length - 1].latency - performanceHistory[performanceHistory.length - 2].latency : 0,
    errorRateTrend: performanceHistory.length > 1 ?
      performanceHistory[performanceHistory.length - 1].errorRate - performanceHistory[performanceHistory.length - 2].errorRate : 0,
    connectionTrend: performanceHistory.length > 1 ?
      performanceHistory[performanceHistory.length - 1].connectionCount - performanceHistory[performanceHistory.length - 2].connectionCount : 0,
  };

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const formatLatency = (ms: number): string => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatErrorRate = (rate: number): string => {
    return `${rate.toFixed(1)}%`;
  };

  const getHealthColor = (health: string): string => {
    switch (health) {
      case 'healthy': return 'text-green-600 bg-green-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      case 'critical': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getAlertColor = (type: PerformanceAlert['type']): string => {
    switch (type) {
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      case 'error': return 'text-orange-600 bg-orange-50';
      case 'critical': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================

  return {
    // Data
    metrics,
    alerts,
    performanceHistory,
    healthSummary,
    performanceTrends,
    config: defaultConfig,
    
    // Loading states
    isLoading,
    error,
    isMonitoring,
    
    // Actions
    startMonitoring,
    stopMonitoring,
    forceCleanup,
    refetch,
    
    // Alert management
    clearAlert,
    clearAllAlerts,
    
    // Utilities
    formatLatency,
    formatErrorRate,
    getHealthColor,
    getAlertColor,
  };
};
