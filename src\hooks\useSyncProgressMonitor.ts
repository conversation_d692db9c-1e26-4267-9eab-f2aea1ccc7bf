/**
 * Sync Progress Monitor Hook
 * Real-time monitoring and tracking of offline sync operations
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { syncPriorityQueue } from '@/utils/syncPriorityQueue';
import type { SyncOperation, SyncResult } from '@/workers/offlineSyncWorker';

// ============================================================================
// TYPES
// ============================================================================

export interface SyncProgressState {
  isActive: boolean;
  currentBatch: {
    id: string;
    operations: SyncOperation[];
    progress: number;
    startTime: number;
    estimatedCompletion: number;
  } | null;
  queueStats: {
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    totalOperations: number;
  };
  performance: {
    averageOperationTime: number;
    successRate: number;
    throughput: number; // operations per minute
    queueUtilization: number;
  };
  recentActivity: SyncActivityEvent[];
}

export interface SyncActivityEvent {
  id: string;
  type: 'OPERATION_STARTED' | 'OPERATION_COMPLETED' | 'OPERATION_FAILED' | 'BATCH_STARTED' | 'BATCH_COMPLETED' | 'QUEUE_EMPTY';
  timestamp: number;
  operation?: SyncOperation;
  result?: SyncResult;
  batchId?: string;
  message: string;
  duration?: number;
}

export interface SyncProgressMetrics {
  totalSyncTime: number;
  operationsPerSecond: number;
  errorRate: number;
  retryRate: number;
  averageBatchSize: number;
  peakQueueSize: number;
}

// ============================================================================
// SYNC PROGRESS MONITOR HOOK
// ============================================================================

export const useSyncProgressMonitor = () => {
  const [progressState, setProgressState] = useState<SyncProgressState>({
    isActive: false,
    currentBatch: null,
    queueStats: {
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      totalOperations: 0,
    },
    performance: {
      averageOperationTime: 0,
      successRate: 100,
      throughput: 0,
      queueUtilization: 0,
    },
    recentActivity: [],
  });

  const [metrics, setMetrics] = useState<SyncProgressMetrics>({
    totalSyncTime: 0,
    operationsPerSecond: 0,
    errorRate: 0,
    retryRate: 0,
    averageBatchSize: 0,
    peakQueueSize: 0,
  });

  const activityLogRef = useRef<SyncActivityEvent[]>([]);
  const metricsRef = useRef({
    sessionStartTime: Date.now(),
    totalOperations: 0,
    totalErrors: 0,
    totalRetries: 0,
    batchSizes: [] as number[],
    peakQueueSize: 0,
    operationTimes: [] as number[],
  });

  // ============================================================================
  // QUEUE STATS QUERY
  // ============================================================================

  const {
    data: queueStats,
    refetch: refetchQueueStats,
  } = useQuery({
    queryKey: ['sync-queue-stats'],
    queryFn: () => syncPriorityQueue.getStats(),
    refetchInterval: 1000, // Update every second
    enabled: progressState.isActive,
  });

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const addActivityEvent = useCallback((event: Omit<SyncActivityEvent, 'id' | 'timestamp'>) => {
    const activityEvent: SyncActivityEvent = {
      ...event,
      id: `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
    };

    activityLogRef.current = [activityEvent, ...activityLogRef.current].slice(0, 100); // Keep last 100 events

    setProgressState(prev => ({
      ...prev,
      recentActivity: activityLogRef.current,
    }));
  }, []);

  const handleOperationStarted = useCallback((data: { operation: SyncOperation }) => {
    addActivityEvent({
      type: 'OPERATION_STARTED',
      operation: data.operation,
      message: `Started ${data.operation.operation} on ${data.operation.table}`,
    });

    metricsRef.current.totalOperations++;
  }, [addActivityEvent]);

  const handleOperationCompleted = useCallback((data: { operation: SyncOperation; result: SyncResult; duration: number }) => {
    addActivityEvent({
      type: 'OPERATION_COMPLETED',
      operation: data.operation,
      result: data.result,
      duration: data.duration,
      message: `Completed ${data.operation.operation} on ${data.operation.table} in ${data.duration}ms`,
    });

    metricsRef.current.operationTimes.push(data.duration);
    
    // Keep only last 100 operation times for performance calculation
    if (metricsRef.current.operationTimes.length > 100) {
      metricsRef.current.operationTimes = metricsRef.current.operationTimes.slice(-100);
    }
  }, [addActivityEvent]);

  const handleOperationFailed = useCallback((data: { operation: SyncOperation; error: string }) => {
    addActivityEvent({
      type: 'OPERATION_FAILED',
      operation: data.operation,
      message: `Failed ${data.operation.operation} on ${data.operation.table}: ${data.error}`,
    });

    metricsRef.current.totalErrors++;
  }, [addActivityEvent]);

  const handleBatchStarted = useCallback((data: { batch: { id: string; operations: SyncOperation[] } }) => {
    const estimatedDuration = data.batch.operations.length * 1000; // Rough estimate
    
    setProgressState(prev => ({
      ...prev,
      currentBatch: {
        id: data.batch.id,
        operations: data.batch.operations,
        progress: 0,
        startTime: Date.now(),
        estimatedCompletion: Date.now() + estimatedDuration,
      },
    }));

    addActivityEvent({
      type: 'BATCH_STARTED',
      batchId: data.batch.id,
      message: `Started batch with ${data.batch.operations.length} operations`,
    });

    metricsRef.current.batchSizes.push(data.batch.operations.length);
  }, [addActivityEvent]);

  const handleBatchCompleted = useCallback((data: { batchId: string; results: SyncResult[]; summary: { total: number; successful: number; failed: number } }) => {
    const duration = progressState.currentBatch ? Date.now() - progressState.currentBatch.startTime : 0;
    
    setProgressState(prev => ({
      ...prev,
      currentBatch: null,
    }));

    addActivityEvent({
      type: 'BATCH_COMPLETED',
      batchId: data.batchId,
      duration,
      message: `Completed batch: ${data.summary.successful}/${data.summary.total} successful`,
    });
  }, [addActivityEvent, progressState.currentBatch]);

  const handleQueueEmpty = useCallback(() => {
    setProgressState(prev => ({
      ...prev,
      isActive: false,
      currentBatch: null,
    }));

    addActivityEvent({
      type: 'QUEUE_EMPTY',
      message: 'All sync operations completed',
    });
  }, [addActivityEvent]);

  // ============================================================================
  // METRICS CALCULATION
  // ============================================================================

  const updateMetrics = useCallback(() => {
    const now = Date.now();
    const sessionDuration = now - metricsRef.current.sessionStartTime;
    const totalOps = metricsRef.current.totalOperations;
    
    const newMetrics: SyncProgressMetrics = {
      totalSyncTime: sessionDuration,
      operationsPerSecond: totalOps > 0 ? (totalOps / (sessionDuration / 1000)) : 0,
      errorRate: totalOps > 0 ? (metricsRef.current.totalErrors / totalOps) * 100 : 0,
      retryRate: totalOps > 0 ? (metricsRef.current.totalRetries / totalOps) * 100 : 0,
      averageBatchSize: metricsRef.current.batchSizes.length > 0 ? 
        metricsRef.current.batchSizes.reduce((a, b) => a + b, 0) / metricsRef.current.batchSizes.length : 0,
      peakQueueSize: metricsRef.current.peakQueueSize,
    };

    setMetrics(newMetrics);
  }, []);

  const updatePerformanceStats = useCallback(() => {
    if (!queueStats) return;

    const averageOperationTime = metricsRef.current.operationTimes.length > 0 ?
      metricsRef.current.operationTimes.reduce((a, b) => a + b, 0) / metricsRef.current.operationTimes.length : 0;

    const successRate = queueStats.totalOperations > 0 ?
      ((queueStats.totalOperations - queueStats.failedOperations) / queueStats.totalOperations) * 100 : 100;

    const throughput = queueStats.averageProcessingTime > 0 ?
      (60000 / queueStats.averageProcessingTime) : 0; // operations per minute

    setProgressState(prev => ({
      ...prev,
      queueStats: {
        pending: queueStats.pendingOperations,
        processing: queueStats.processingOperations,
        completed: queueStats.completedOperations,
        failed: queueStats.failedOperations,
        totalOperations: queueStats.totalOperations,
      },
      performance: {
        averageOperationTime,
        successRate,
        throughput,
        queueUtilization: queueStats.queueUtilization,
      },
    }));

    // Update peak queue size
    const currentQueueSize = queueStats.pendingOperations + queueStats.processingOperations;
    if (currentQueueSize > metricsRef.current.peakQueueSize) {
      metricsRef.current.peakQueueSize = currentQueueSize;
    }
  }, [queueStats]);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    // Set up event listeners
    syncPriorityQueue.on('operationStarted', handleOperationStarted);
    syncPriorityQueue.on('operationCompleted', handleOperationCompleted);
    syncPriorityQueue.on('operationFailed', handleOperationFailed);
    syncPriorityQueue.on('batchStarted', handleBatchStarted);
    syncPriorityQueue.on('batchCompleted', handleBatchCompleted);
    syncPriorityQueue.on('queueEmpty', handleQueueEmpty);

    return () => {
      // Clean up event listeners
      syncPriorityQueue.off('operationStarted', handleOperationStarted);
      syncPriorityQueue.off('operationCompleted', handleOperationCompleted);
      syncPriorityQueue.off('operationFailed', handleOperationFailed);
      syncPriorityQueue.off('batchStarted', handleBatchStarted);
      syncPriorityQueue.off('batchCompleted', handleBatchCompleted);
      syncPriorityQueue.off('queueEmpty', handleQueueEmpty);
    };
  }, [
    handleOperationStarted,
    handleOperationCompleted,
    handleOperationFailed,
    handleBatchStarted,
    handleBatchCompleted,
    handleQueueEmpty,
  ]);

  useEffect(() => {
    updatePerformanceStats();
  }, [updatePerformanceStats]);

  useEffect(() => {
    const interval = setInterval(updateMetrics, 5000); // Update metrics every 5 seconds
    return () => clearInterval(interval);
  }, [updateMetrics]);

  // ============================================================================
  // CONTROL METHODS
  // ============================================================================

  const startMonitoring = useCallback(() => {
    setProgressState(prev => ({ ...prev, isActive: true }));
    metricsRef.current.sessionStartTime = Date.now();
    refetchQueueStats();
  }, [refetchQueueStats]);

  const stopMonitoring = useCallback(() => {
    setProgressState(prev => ({ ...prev, isActive: false, currentBatch: null }));
  }, []);

  const resetMetrics = useCallback(() => {
    metricsRef.current = {
      sessionStartTime: Date.now(),
      totalOperations: 0,
      totalErrors: 0,
      totalRetries: 0,
      batchSizes: [],
      peakQueueSize: 0,
      operationTimes: [],
    };
    
    activityLogRef.current = [];
    
    setProgressState(prev => ({
      ...prev,
      recentActivity: [],
    }));
    
    setMetrics({
      totalSyncTime: 0,
      operationsPerSecond: 0,
      errorRate: 0,
      retryRate: 0,
      averageBatchSize: 0,
      peakQueueSize: 0,
    });
  }, []);

  const clearActivityLog = useCallback(() => {
    activityLogRef.current = [];
    setProgressState(prev => ({
      ...prev,
      recentActivity: [],
    }));
  }, []);

  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================

  return {
    // State
    progressState,
    metrics,
    
    // Control methods
    startMonitoring,
    stopMonitoring,
    resetMetrics,
    clearActivityLog,
    
    // Utility methods
    formatDuration: (ms: number) => {
      if (ms < 1000) return `${ms}ms`;
      if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
      return `${(ms / 60000).toFixed(1)}m`;
    },
    
    formatThroughput: (opsPerMin: number) => {
      if (opsPerMin < 1) return `${(opsPerMin * 60).toFixed(1)} ops/min`;
      return `${opsPerMin.toFixed(1)} ops/min`;
    },
    
    getProgressPercentage: () => {
      if (!progressState.currentBatch) return 0;
      const elapsed = Date.now() - progressState.currentBatch.startTime;
      const estimated = progressState.currentBatch.estimatedCompletion - progressState.currentBatch.startTime;
      return Math.min(100, (elapsed / estimated) * 100);
    },
    
    isHealthy: () => {
      return progressState.performance.successRate > 90 && 
             progressState.performance.queueUtilization < 80 &&
             metrics.errorRate < 10;
    },
  };
};
