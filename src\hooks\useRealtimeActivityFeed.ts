/**
 * Real-time Activity Feed Hook
 * Provides real-time activity updates using WebSocket subscriptions
 */

import { useState, useEffect, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/useAuth';
import { realtimeSubscriptionManager } from '@/services/realtimeSubscriptionManager';
import { supabase } from '@/integrations/supabase/client';

// ============================================================================
// TYPES
// ============================================================================

export interface Activity {
  id: string;
  activity_type: 'task_created' | 'task_updated' | 'task_completed' | 'report_submitted' | 'distribution_logged' | 'school_added' | 'user_joined';
  user_id: string;
  user_name: string;
  entity_type: 'task' | 'field_report' | 'distribution' | 'school' | 'user';
  entity_id: string;
  description: string;
  metadata?: Record<string, unknown>;
  created_at: string;
  entity_details?: Record<string, unknown>;
}

export interface ActivityFeedStats {
  total: number;
  todayCount: number;
  weekCount: number;
  byType: Record<string, number>;
  byUser: Record<string, number>;
}

// ============================================================================
// REAL-TIME ACTIVITY FEED HOOK
// ============================================================================

export const useRealtimeActivityFeed = (limit: number = 50) => {
  const { user, profile } = useAuth();
  const queryClient = useQueryClient();
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [subscriptionId, setSubscriptionId] = useState<string | null>(null);
  const [liveActivities, setLiveActivities] = useState<Activity[]>([]);

  // ============================================================================
  // ACTIVITY FEED QUERY (INITIAL LOAD)
  // ============================================================================

  const {
    data: activities,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['activity-feed', limit],
    queryFn: async (): Promise<Activity[]> => {
      const { data, error } = await supabase
        .rpc('get_recent_activities', {
          p_limit: limit,
          p_offset: 0,
        });

      if (error) {
        console.error('Error fetching activities:', error);
        throw error;
      }

      return data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes (longer since we have real-time updates)
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchInterval: undefined, // Disable polling since we have real-time updates
  });

  // ============================================================================
  // ROLE-BASED FILTERING
  // ============================================================================

  const filterActivityByRole = useCallback((activity: Activity, userRole?: string): boolean => {
    if (!userRole) return true;

    switch (userRole) {
      case 'field_staff':
        // Field staff see activities related to their work
        return (
          activity.user_id === user?.id || // Their own activities
          activity.activity_type === 'task_created' || // New tasks (might be assigned to them)
          activity.activity_type === 'distribution_logged' || // Distribution activities
          activity.entity_type === 'school' // School-related activities
        );

      case 'program_officer':
        // Program officers see most activities except admin-specific ones
        return activity.activity_type !== 'user_joined';

      case 'admin':
        // Admins see all activities
        return true;

      default:
        return true;
    }
  }, [user?.id]);

  // ============================================================================
  // REAL-TIME SUBSCRIPTION HANDLERS
  // ============================================================================

  const handleNewActivity = useCallback((payload: Record<string, unknown>) => {
    console.log('🔔 New activity received:', payload);

    const newActivity = payload.new as Activity;

    // Filter activities based on user role and permissions
    const shouldShow = filterActivityByRole(newActivity, profile?.role);
    if (!shouldShow) return;
    
    // Add to live activities (temporary list for real-time updates)
    setLiveActivities(prev => {
      // Check if activity already exists
      const exists = prev.some(activity => activity.id === newActivity.id);
      if (exists) return prev;
      
      return [newActivity, ...prev].slice(0, 10); // Keep only latest 10 live activities
    });
    
    // Update main activities cache
    queryClient.setQueryData(['activity-feed', limit], (oldData: Activity[] = []) => {
      // Check if activity already exists
      const exists = oldData.some(activity => activity.id === newActivity.id);
      if (exists) return oldData;
      
      return [newActivity, ...oldData].slice(0, limit);
    });

  }, [profile?.role, limit, queryClient, filterActivityByRole]);


  // ============================================================================
  // SUBSCRIPTION MANAGEMENT
  // ============================================================================

  const setupRealtimeSubscription = useCallback(() => {
    if (isSubscribed) return;

    try {
      console.log('🔔 Setting up real-time activity feed subscription');
      
      const subscriptionId = realtimeSubscriptionManager.subscribe(
        {
          table: 'activities',
          event: 'INSERT',
          priority: 'medium',
        },
        handleNewActivity
      );
      
      setSubscriptionId(subscriptionId);
      setIsSubscribed(true);
      
      console.log('🔔 Real-time activity feed subscription established');
      
    } catch (error) {
      console.error('Failed to setup real-time activity feed subscription:', error);
    }
  }, [isSubscribed, handleNewActivity]);

  const cleanupSubscription = useCallback(() => {
    if (subscriptionId) {
      realtimeSubscriptionManager.unsubscribe(subscriptionId);
      setSubscriptionId(null);
      setIsSubscribed(false);
      setLiveActivities([]);
      
      console.log('🔔 Real-time activity feed subscription cleaned up');
    }
  }, [subscriptionId]);

  // ============================================================================
  // ACTIVITY ACTIONS
  // ============================================================================

  const markActivitiesAsViewed = useCallback(() => {
    // Clear live activities (they've been viewed)
    setLiveActivities([]);
  }, []);

  const refreshFeed = useCallback(async () => {
    try {
      await refetch();
      setLiveActivities([]); // Clear live activities after refresh
    } catch (error) {
      console.error('Failed to refresh activity feed:', error);
    }
  }, [refetch]);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    if (user && !isSubscribed) {
      setupRealtimeSubscription();
    }

    return () => {
      cleanupSubscription();
    };
  }, [user, setupRealtimeSubscription, cleanupSubscription, isSubscribed]);

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  // Combine activities and live activities, removing duplicates
  const allActivities = [...liveActivities, ...(activities || [])].reduce((acc, activity) => {
    if (!acc.some(a => a.id === activity.id)) {
      acc.push(activity);
    }
    return acc;
  }, [] as Activity[]);

  const activityStats: ActivityFeedStats = {
    total: allActivities.length,
    todayCount: allActivities.filter(activity => {
      const today = new Date();
      const activityDate = new Date(activity.created_at);
      return activityDate.toDateString() === today.toDateString();
    }).length,
    weekCount: allActivities.filter(activity => {
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return new Date(activity.created_at) >= weekAgo;
    }).length,
    byType: allActivities.reduce((acc, activity) => {
      acc[activity.activity_type] = (acc[activity.activity_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    byUser: allActivities.reduce((acc, activity) => {
      acc[activity.user_name] = (acc[activity.user_name] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
  };

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const getActivityIcon = (activityType: Activity['activity_type']): string => {
    switch (activityType) {
      case 'task_created':
        return '📋';
      case 'task_updated':
        return '✏️';
      case 'task_completed':
        return '✅';
      case 'report_submitted':
        return '📊';
      case 'distribution_logged':
        return '📚';
      case 'school_added':
        return '🏫';
      case 'user_joined':
        return '👤';
      default:
        return '🔔';
    }
  };

  const getActivityColor = (activityType: Activity['activity_type']): string => {
    switch (activityType) {
      case 'task_created':
        return 'text-blue-600 bg-blue-50';
      case 'task_updated':
        return 'text-orange-600 bg-orange-50';
      case 'task_completed':
        return 'text-green-600 bg-green-50';
      case 'report_submitted':
        return 'text-purple-600 bg-purple-50';
      case 'distribution_logged':
        return 'text-indigo-600 bg-indigo-50';
      case 'school_added':
        return 'text-teal-600 bg-teal-50';
      case 'user_joined':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const formatRelativeTime = (timestamp: string): string => {
    const now = new Date();
    const activityTime = new Date(timestamp);
    const diffMs = now.getTime() - activityTime.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return activityTime.toLocaleDateString();
  };

  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================

  return {
    // Data
    activities: allActivities,
    liveActivities,
    activityStats,
    
    // Loading states
    isLoading,
    error,
    
    // Real-time status
    isSubscribed,
    hasNewActivities: liveActivities.length > 0,
    
    // Actions
    markActivitiesAsViewed,
    refreshFeed,
    refetch,
    
    // Subscription management
    setupRealtimeSubscription,
    cleanupSubscription,
    
    // Utilities
    getActivityIcon,
    getActivityColor,
    formatRelativeTime,
  };
};
