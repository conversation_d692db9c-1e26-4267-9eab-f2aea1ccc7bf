# Role System Testing Plan

## Overview

This document outlines the comprehensive testing plan for the new user role system implementation in the iLead Uganda application.

## Testing Scope

### New Roles to Test
- Staff (new default role)
- Partner
- Accountant (with special book management access)
- Social Media Manager

### Existing Roles to Validate
- Admin (no changes expected)
- Program Officer (no changes expected)
- Field Staff (legacy role, should maintain existing functionality)

## Test Categories

### 1. Authentication and Profile Management

#### Test Cases
- [ ] New user registration defaults to 'staff' role
- [ ] Profile creation works for all new roles
- [ ] Role assignment during user creation
- [ ] Profile updates maintain role integrity
- [ ] Authentication flow works for all roles

#### Validation Queries
```sql
-- Check default role assignment
SELECT role FROM profiles WHERE created_at > NOW() - INTERVAL '1 day';

-- Verify all roles are present
SELECT DISTINCT role FROM profiles ORDER BY role;
```

### 2. Navigation and Route Access

#### Test Cases per Role

**Admin**
- [ ] Access to all navigation items
- [ ] Staff Management access
- [ ] Impact Analytics access
- [ ] System settings access

**Program Officer**
- [ ] Access to management features
- [ ] Limited staff management
- [ ] Book management access
- [ ] No system admin access

**Accountant**
- [ ] Field-level navigation
- [ ] **Special**: Book management access
- [ ] **Special**: Distribution management access
- [ ] No staff management access

**Staff/Partner/Social Media Manager**
- [ ] Field-level navigation only
- [ ] No book management access
- [ ] No staff management access
- [ ] Own data access only

**Field Staff (Legacy)**
- [ ] Maintains existing navigation
- [ ] No regression in functionality

### 3. Data Access Control

#### Own Data Access (Field-Level Roles)
```typescript
// Test data isolation
const testDataAccess = async (userId: string, role: string) => {
  // Should only see own field reports
  const reports = await getFieldReports(userId);
  
  // Should only see assigned tasks
  const tasks = await getTasks(userId);
  
  // Should only see own attendance
  const attendance = await getAttendance(userId);
};
```

#### Cross-User Data Access (Elevated Roles)
```typescript
// Test elevated access
const testElevatedAccess = async (userId: string, role: string) => {
  if (role === 'admin' || role === 'program_officer') {
    // Should see all data
    const allReports = await getAllFieldReports();
    const allTasks = await getAllTasks();
  }
};
```

### 4. Book Management Access

#### Accountant Special Access
- [ ] Can view books page
- [ ] Can create new books
- [ ] Can edit existing books
- [ ] Can delete books
- [ ] Can manage distributions
- [ ] Can view inventory

#### Other Field-Level Roles
- [ ] Cannot access books management
- [ ] Can view books (read-only)
- [ ] Proper error messages for restricted access

### 5. Component Access Control

#### Access Control Components
```typescript
// Test component rendering
<AdminOnly>Should render for admin only</AdminOnly>
<AdminProgramOfficer>Should render for admin/program officer</AdminProgramOfficer>
<BookManagementAccess>Should render for admin/program officer/accountant</BookManagementAccess>
<AllRoles>Should render for all authenticated users</AllRoles>
```

#### Role Checker Utilities
```typescript
// Test role checking functions
RoleChecker.isAdmin('admin') // true
RoleChecker.isAccountant('accountant') // true
RoleChecker.isFieldLevelRole('staff') // true
RoleChecker.canManageBooks('accountant') // true
RoleChecker.canManageBooks('staff') // false
```

### 6. Database Security (RLS Policies)

#### Test RLS Policy Enforcement
```sql
-- Test as different roles
SET ROLE authenticated;
SET request.jwt.claims TO '{"sub": "user-id", "role": "staff"}';

-- Should only see own data
SELECT * FROM field_reports;
SELECT * FROM tasks WHERE assigned_to = auth.uid();

-- Test book access for accountant
SET request.jwt.claims TO '{"sub": "accountant-id", "role": "accountant"}';
SELECT * FROM books; -- Should work
INSERT INTO books (...) VALUES (...); -- Should work

-- Test book access for staff
SET request.jwt.claims TO '{"sub": "staff-id", "role": "staff"}';
SELECT * FROM books; -- Should work (read-only)
INSERT INTO books (...) VALUES (...); -- Should fail
```

### 7. API Endpoint Security

#### Server-Side Validation
```typescript
// Test endpoint access validation
const testEndpointAccess = async () => {
  // Test book management endpoints
  await testEndpoint('books_create', 'accountant'); // Should pass
  await testEndpoint('books_create', 'staff'); // Should fail
  
  // Test field report endpoints
  await testEndpoint('field_reports_create', 'staff'); // Should pass
  await testEndpoint('field_reports_create', 'partner'); // Should pass
  
  // Test admin endpoints
  await testEndpoint('staff_management', 'admin'); // Should pass
  await testEndpoint('staff_management', 'staff'); // Should fail
};
```

## Automated Testing

### Unit Tests
```typescript
describe('Role System', () => {
  describe('RBAC Utilities', () => {
    test('should correctly identify role types', () => {
      expect(RoleChecker.isStaff('staff')).toBe(true);
      expect(RoleChecker.isPartner('partner')).toBe(true);
      expect(RoleChecker.isAccountant('accountant')).toBe(true);
      expect(RoleChecker.isSocialMediaManager('social_media_manager')).toBe(true);
    });
    
    test('should correctly check field-level roles', () => {
      expect(RoleChecker.isFieldLevelRole('staff')).toBe(true);
      expect(RoleChecker.isFieldLevelRole('admin')).toBe(false);
    });
    
    test('should correctly check book management permissions', () => {
      expect(RoleChecker.canManageBooks('accountant')).toBe(true);
      expect(RoleChecker.canManageBooks('staff')).toBe(false);
    });
  });
  
  describe('Access Control', () => {
    test('should enforce role-based access', () => {
      const adminUser = { id: '1', role: 'admin' };
      const staffUser = { id: '2', role: 'staff' };
      
      expect(checkAccess(adminUser, ACCESS_CONFIGS.ADMIN_ONLY)).toEqual({
        hasAccess: true
      });
      
      expect(checkAccess(staffUser, ACCESS_CONFIGS.ADMIN_ONLY)).toEqual({
        hasAccess: false,
        reason: expect.any(String)
      });
    });
  });
});
```

### Integration Tests
```typescript
describe('Role Integration', () => {
  test('should handle user creation with new roles', async () => {
    const newUser = await createUser({
      email: '<EMAIL>',
      name: 'Test User',
      role: 'staff'
    });
    
    expect(newUser.role).toBe('staff');
  });
  
  test('should enforce book management access', async () => {
    const accountant = await loginAs('accountant');
    const staff = await loginAs('staff');
    
    // Accountant should access books
    await expect(accountant.get('/api/books')).resolves.toBeDefined();
    await expect(accountant.post('/api/books', bookData)).resolves.toBeDefined();
    
    // Staff should not access book management
    await expect(staff.post('/api/books', bookData)).rejects.toThrow();
  });
});
```

## Manual Testing Checklist

### Pre-Testing Setup
- [ ] Deploy migrations to test environment
- [ ] Create test users for each role
- [ ] Prepare test data sets
- [ ] Set up monitoring for errors

### Role-Specific Testing

#### For Each Role (Staff, Partner, Accountant, Social Media Manager):
- [ ] Login successfully
- [ ] Navigate to dashboard
- [ ] Access field visits page
- [ ] Create field report
- [ ] View assigned tasks
- [ ] Access profile settings
- [ ] Verify restricted access to admin features

#### Accountant-Specific Testing:
- [ ] Access books management page
- [ ] Create new book entry
- [ ] Edit existing book
- [ ] Log book distribution
- [ ] View inventory reports

#### Regression Testing (Existing Roles):
- [ ] Admin: Full access maintained
- [ ] Program Officer: No functionality lost
- [ ] Field Staff: Legacy functionality preserved

### Error Handling
- [ ] Appropriate error messages for access denied
- [ ] Graceful handling of role changes
- [ ] Proper fallback for unknown roles

## Performance Testing

### Database Performance
- [ ] RLS policy performance with new roles
- [ ] Query performance with role-based filtering
- [ ] Index effectiveness for role-based queries

### Frontend Performance
- [ ] Navigation rendering with role checks
- [ ] Component access control performance
- [ ] Route protection overhead

## Security Testing

### Access Control Validation
- [ ] No privilege escalation possible
- [ ] Data isolation between field-level roles
- [ ] Proper session management for all roles

### SQL Injection Prevention
- [ ] Role-based queries are parameterized
- [ ] RLS policies prevent data leakage
- [ ] Server-side validation is secure

## Test Data Requirements

### Test Users
```sql
-- Create test users for each role
INSERT INTO profiles (id, name, role, email) VALUES
('admin-test', 'Admin Test', 'admin', '<EMAIL>'),
('po-test', 'PO Test', 'program_officer', '<EMAIL>'),
('staff-test', 'Staff Test', 'staff', '<EMAIL>'),
('partner-test', 'Partner Test', 'partner', '<EMAIL>'),
('accountant-test', 'Accountant Test', 'accountant', '<EMAIL>'),
('sm-test', 'SM Test', 'social_media_manager', '<EMAIL>'),
('fs-test', 'FS Test', 'field_staff', '<EMAIL>');
```

### Test Data
- Sample schools assigned to different users
- Field reports from different users
- Tasks assigned to various roles
- Book inventory for testing accountant access

## Success Criteria

### Functional Requirements
- [ ] All new roles can authenticate and access appropriate features
- [ ] Accountant role has book management access
- [ ] Field-level roles are properly isolated
- [ ] No regression in existing role functionality

### Security Requirements
- [ ] RLS policies enforce proper data access
- [ ] API endpoints validate role permissions
- [ ] No unauthorized access possible

### Performance Requirements
- [ ] No significant performance degradation
- [ ] Role checks execute efficiently
- [ ] Database queries remain optimized

### User Experience Requirements
- [ ] Clear error messages for access denied
- [ ] Intuitive navigation based on role
- [ ] Consistent behavior across the application
