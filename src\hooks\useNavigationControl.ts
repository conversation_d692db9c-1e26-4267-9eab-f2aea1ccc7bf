import { useMemo } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { 
  getNavigationForRole, 
  getNavigationSections, 
  canAccessRoute, 
  hasNavigationAccess,
  getNavigationItem,
  type NavigationItem,
  type NavigationSection
} from '@/config/navigation';
import { UserRole } from '@/utils/rbac';

/**
 * Centralized navigation control hook
 * Provides all navigation-related logic and access control
 */
export function useNavigationControl() {
  const { user, profile } = useAuth();

  const currentUserRole = useMemo(() => {
    return profile?.role as UserRole || null;
  }, [profile?.role]);

  /**
   * Get navigation items for current user
   */
  const navigationItems = useMemo(() => {
    if (!currentUserRole) return [];
    return getNavigationForRole(currentUserRole);
  }, [currentUserRole]);

  /**
   * Get navigation sections for current user
   */
  const navigationSections = useMemo(() => {
    if (!currentUserRole) return [];
    return getNavigationSections(currentUserRole);
  }, [currentUserRole]);

  /**
   * Check if current user can access a route
   */
  const canUserAccessRoute = useMemo(() => {
    return (route: string) => {
      if (!currentUserRole) return false;
      return canAccessRoute(currentUserRole, route);
    };
  }, [currentUserRole]);

  /**
   * Check if current user has access to a navigation feature
   */
  const hasUserNavigationAccess = useMemo(() => {
    return (featureId: string) => {
      if (!currentUserRole) return false;
      return hasNavigationAccess(currentUserRole, featureId);
    };
  }, [currentUserRole]);

  /**
   * Get a specific navigation item for current user
   */
  const getUserNavigationItem = useMemo(() => {
    return (itemId: string) => {
      if (!currentUserRole) return null;
      return getNavigationItem(currentUserRole, itemId);
    };
  }, [currentUserRole]);

  /**
   * Get allowed routes for current user
   */
  const allowedRoutes = useMemo(() => {
    if (!currentUserRole) return [];
    
    const routes: string[] = [];
    
    navigationItems.forEach(item => {
      routes.push(item.route);
      if (item.children) {
        item.children.forEach(child => {
          routes.push(child.route);
        });
      }
    });
    
    return routes;
  }, [navigationItems, currentUserRole]);

  /**
   * Get navigation breadcrumbs for a route
   */
  const getNavigationBreadcrumbs = useMemo(() => {
    return (route: string): NavigationItem[] => {
      if (!currentUserRole) return [];
      
      const breadcrumbs: NavigationItem[] = [];
      
      // Find the item and its parent
      for (const item of navigationItems) {
        if (item.route === route) {
          breadcrumbs.push(item);
          break;
        }
        
        if (item.children) {
          const child = item.children.find(c => c.route === route);
          if (child) {
            breadcrumbs.push(item, child);
            break;
          }
        }
      }
      
      return breadcrumbs;
    };
  }, [navigationItems, currentUserRole]);

  /**
   * Get navigation statistics for current user
   */
  const navigationStats = useMemo(() => {
    if (!currentUserRole) {
      return {
        totalItems: 0,
        accessibleItems: 0,
        sectionsCount: 0,
        hasAdminAccess: false,
        hasProgramOfficerAccess: false,
        hasFieldStaffAccess: false,
      };
    }

    return {
      totalItems: navigationItems.length,
      accessibleItems: navigationItems.length,
      sectionsCount: navigationSections.length,
      hasAdminAccess: currentUserRole === 'admin',
      hasProgramOfficerAccess: currentUserRole === 'admin' || currentUserRole === 'program_officer',
      hasFieldStaffAccess: true, // All roles have some level of access
    };
  }, [navigationItems, navigationSections, currentUserRole]);

  /**
   * Get role-specific navigation configuration
   */
  const roleBasedConfig = useMemo(() => {
    if (!currentUserRole) return null;

    const isFieldLevelRole = ['field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'].includes(currentUserRole);
    const isElevatedRole = ['admin', 'program_officer'].includes(currentUserRole);

    const config = {
      role: currentUserRole,
      canManageUsers: currentUserRole === 'admin' || currentUserRole === 'program_officer',
      canViewAllData: currentUserRole === 'admin' || currentUserRole === 'program_officer',
      canAccessImpact: currentUserRole === 'admin' || currentUserRole === 'program_officer',
      canManageBooks: currentUserRole === 'admin' || currentUserRole === 'program_officer' || currentUserRole === 'accountant',
      canViewReports: currentUserRole === 'admin',
      isFieldLevelRole,
      isElevatedRole,
      isProgramOfficerOrAbove: currentUserRole === 'admin' || currentUserRole === 'program_officer',
      isAdminOnly: currentUserRole === 'admin',
      isAccountant: currentUserRole === 'accountant',
    };

    return config;
  }, [currentUserRole]);

  /**
   * Validate route access and redirect if necessary
   */
  const validateRouteAccess = useMemo(() => {
    return (route: string): { isValid: boolean; redirectTo?: string; reason?: string } => {
      if (!currentUserRole) {
        return { 
          isValid: false, 
          redirectTo: 'dashboard', 
          reason: 'User not authenticated' 
        };
      }

      if (canUserAccessRoute(route)) {
        return { isValid: true };
      }

      // Determine appropriate redirect based on role
      let redirectTo = 'dashboard';
      const isFieldLevelRole = ['field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'].includes(currentUserRole);

      if (isFieldLevelRole) {
        redirectTo = 'field-visits';
      } else if (currentUserRole === 'program_officer') {
        redirectTo = 'dashboard';
      }

      return { 
        isValid: false, 
        redirectTo, 
        reason: `Access denied to route: ${route}` 
      };
    };
  }, [currentUserRole, canUserAccessRoute]);

  return {
    // User context
    currentUserRole,
    isAuthenticated: !!user && !!currentUserRole,

    // Navigation data
    navigationItems,
    navigationSections,
    allowedRoutes,

    // Access control functions
    canAccessRoute: canUserAccessRoute,
    hasNavigationAccess: hasUserNavigationAccess,
    getNavigationItem: getUserNavigationItem,
    getNavigationBreadcrumbs,
    validateRouteAccess,

    // Configuration and stats
    navigationStats,
    roleBasedConfig,

    // Utility functions
    isRouteAllowed: canUserAccessRoute,
    getAccessibleItems: () => navigationItems,
    getSectionsByRole: () => navigationSections,
  };
}

/**
 * Hook for simplified navigation access checks
 */
export function useNavigationAccess() {
  const { canAccessRoute, hasNavigationAccess, currentUserRole } = useNavigationControl();

  return {
    canAccessRoute,
    hasNavigationAccess,
    currentUserRole,
    // Quick access checks
    canAccessDashboard: () => canAccessRoute('dashboard'),
    canAccessFieldVisits: () => canAccessRoute('field-visits'),
    canAccessTasks: () => canAccessRoute('tasks'),
    canAccessSchools: () => canAccessRoute('schools'),
    canAccessBooks: () => canAccessRoute('books'),
    canAccessImpact: () => canAccessRoute('impact'),
    canAccessStaffManagement: () => canAccessRoute('staff-management'),
    canAccessSettings: () => canAccessRoute('settings'),
    canAccessHelp: () => canAccessRoute('help'),
  };
}
