-- Migration: Fix RLS Policies for Admin Operations
-- Description: Update RLS policies to allow service role operations for user management
-- Date: 2025-01-14

-- Drop existing policies that might be too restrictive
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Admin can manage all profiles" ON profiles;
DROP POLICY IF EXISTS "Program officers can view field staff profiles" ON profiles;

-- Create comprehensive RLS policies for profiles table

-- 1. Allow service role to bypass RLS for admin operations
-- This is handled automatically by Supabase when using service role

-- 2. Allow users to view their own profile
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT
    USING (auth.uid() = id);

-- 3. Allow users to update their own profile (limited fields)
CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE
    USING (auth.uid() = id)
    WITH CHECK (
        auth.uid() = id AND
        -- Users can only update these fields
        (OLD.role = NEW.role) AND  -- Cannot change their own role
        (OLD.id = NEW.id) AND      -- Cannot change their ID
        (OLD.created_at = NEW.created_at) -- Cannot change creation date
    );

-- 4. Allow admins to manage all profiles
CREATE POLICY "Admins can manage all profiles" ON profiles
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() AND p.role = 'admin'
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() AND p.role = 'admin'
        )
    );

-- 5. Allow program officers to view and manage field-level staff
CREATE POLICY "Program officers can manage field staff" ON profiles
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() AND p.role = 'program_officer'
        ) AND
        role IN ('staff', 'field_staff', 'partner', 'accountant', 'social_media_manager')
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() AND p.role = 'program_officer'
        ) AND
        role IN ('staff', 'field_staff', 'partner', 'accountant', 'social_media_manager')
    );

-- 6. Allow program officers to view admin and other program officer profiles (read-only)
CREATE POLICY "Program officers can view admin profiles" ON profiles
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() AND p.role = 'program_officer'
        ) AND
        role IN ('admin', 'program_officer')
    );

-- 7. Allow field staff to view other field staff in their division
CREATE POLICY "Field staff can view division colleagues" ON profiles
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() 
            AND p.role IN ('field_staff', 'staff')
            AND (p.division_id = profiles.division_id OR profiles.division_id IS NULL)
        )
    );

-- Ensure RLS is enabled on profiles table
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Update other related tables to ensure proper RLS

-- Schools table - ensure proper access
DROP POLICY IF EXISTS "Field staff can view assigned schools" ON schools;
DROP POLICY IF EXISTS "Admins and program officers can manage schools" ON schools;

CREATE POLICY "Users can view schools based on role" ON schools
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() 
            AND (
                p.role IN ('admin', 'program_officer') OR  -- Admins and POs see all
                (p.role IN ('field_staff', 'staff') AND (
                    -- Field staff see schools in their division or assigned to them
                    schools.division_id = p.division_id OR
                    EXISTS (
                        SELECT 1 FROM school_assignments sa
                        WHERE sa.school_id = schools.id AND sa.staff_id = p.id
                    )
                ))
            )
        )
    );

CREATE POLICY "Admins and program officers can manage schools" ON schools
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() AND p.role IN ('admin', 'program_officer')
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() AND p.role IN ('admin', 'program_officer')
        )
    );

-- Field reports table - ensure proper access
DROP POLICY IF EXISTS "Users can manage their own field reports" ON field_reports;
DROP POLICY IF EXISTS "Admins and program officers can view all reports" ON field_reports;

CREATE POLICY "Users can manage own field reports" ON field_reports
    FOR ALL
    USING (
        auth.uid() = staff_id OR
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() AND p.role IN ('admin', 'program_officer')
        )
    )
    WITH CHECK (
        auth.uid() = staff_id OR
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() AND p.role IN ('admin', 'program_officer')
        )
    );

-- Add comment for documentation
COMMENT ON MIGRATION '035_fix_rls_for_admin_operations' IS 'Updated RLS policies to properly support admin operations while maintaining security. Service role operations bypass RLS automatically, and user-level policies are properly configured for role-based access.';

-- Grant necessary permissions to service role (if needed)
-- Note: Service role already has full access, but we can be explicit
GRANT ALL ON profiles TO service_role;
GRANT ALL ON schools TO service_role;
GRANT ALL ON field_reports TO service_role;
