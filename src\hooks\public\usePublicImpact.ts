import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export type PublicImpactSummary = {
  students: number;
  schools: number;
  sessions: number;
  books: number;
  districts: number;
};

export function usePublicImpactSummary() {
  return useQuery<PublicImpactSummary | null>({
    queryKey: ['public-impact-summary'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_public_impact_summary');
      if (error) throw error;
      return (data && data.length > 0 ? data[0] : null) as PublicImpactSummary | null;
    },
    staleTime: 10 * 60 * 1000,
  });
}

export type PublicTrendPoint = {
  period_start: string; // ISO date string
  students: number;
  sessions: number;
};

export function usePublicTrends(period: 'weekly' | 'monthly' = 'weekly', periods = 12) {
  return useQuery<PublicTrendPoint[]>({
    queryKey: ['public-trends', period, periods],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_public_trends', { period, periods });
      if (error) throw error;
      return (data || []) as PublicTrendPoint[];
    },
    staleTime: 10 * 60 * 1000,
  });
}

export type PublicBooksSlice = { title: 'iLead' | 'iDo' | 'iChoose'; total: number };

export function usePublicBooksBreakdown() {
  return useQuery<PublicBooksSlice[]>({
    queryKey: ['public-books-breakdown'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_public_books_breakdown');
      if (error) throw error;
      return (data || []) as PublicBooksSlice[];
    },
    staleTime: 10 * 60 * 1000,
  });
}

export type PublicCoverageItem = { district: string; schools_count: number };

export function usePublicCoverageSummary() {
  return useQuery<PublicCoverageItem[]>({
    queryKey: ['public-coverage-summary'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_public_coverage_summary');
      if (error) throw error;
      return (data || []) as PublicCoverageItem[];
    },
    staleTime: 10 * 60 * 1000,
  });
}

