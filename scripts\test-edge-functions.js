// Test script for Edge Functions
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function testEdgeFunctions() {
  console.log('🧪 Testing Edge Functions...\n');

  try {
    // First, let's try to authenticate as an admin
    console.log('1. Authenticating as admin...');
    
    // You'll need to replace these with actual admin credentials
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>', // Replace with actual admin email
      password: 'your-admin-password' // Replace with actual admin password
    });

    if (authError) {
      console.error('❌ Authentication failed:', authError.message);
      console.log('Please update the credentials in scripts/test-edge-functions.js');
      return;
    }

    console.log('✅ Authenticated successfully');

    // Test create-user function
    console.log('\n2. Testing create-user function...');
    
    const testUser = {
      email: 'test-user-' + Date.now() + '@example.com',
      name: 'Test User',
      role: 'field_staff',
      phone: '+256700000000'
    };

    const { data: createResult, error: createError } = await supabase.functions.invoke('create-user', {
      body: testUser
    });

    if (createError) {
      console.error('❌ Create user function failed:', createError);
    } else if (createResult.success) {
      console.log('✅ User created successfully:', createResult.user.email);
      console.log('   Temporary password:', createResult.user.tempPassword);
    } else {
      console.error('❌ User creation failed:', createResult.error);
    }

    // Test bulk-create-users function
    console.log('\n3. Testing bulk-create-users function...');
    
    const testUsers = [
      {
        email: 'bulk-test-1-' + Date.now() + '@example.com',
        name: 'Bulk Test User 1',
        role: 'field_staff'
      },
      {
        email: 'bulk-test-2-' + Date.now() + '@example.com',
        name: 'Bulk Test User 2',
        role: 'staff'
      }
    ];

    const { data: bulkResult, error: bulkError } = await supabase.functions.invoke('bulk-create-users', {
      body: { users: testUsers }
    });

    if (bulkError) {
      console.error('❌ Bulk create function failed:', bulkError);
    } else {
      console.log('✅ Bulk creation completed');
      console.log('   Summary:', bulkResult.summary);
      console.log('   Successful:', bulkResult.results.filter(r => r.success).length);
      console.log('   Failed:', bulkResult.results.filter(r => !r.success).length);
    }

    // Test permission restrictions
    console.log('\n4. Testing permission restrictions...');
    
    const restrictedUser = {
      email: 'restricted-test-' + Date.now() + '@example.com',
      name: 'Restricted Test',
      role: 'admin' // This should fail if current user is not admin
    };

    const { data: restrictedResult, error: restrictedError } = await supabase.functions.invoke('create-user', {
      body: restrictedUser
    });

    if (restrictedError) {
      console.log('✅ Permission restriction working (expected error):', restrictedError.message);
    } else if (!restrictedResult.success) {
      console.log('✅ Permission restriction working:', restrictedResult.error);
    } else {
      console.log('⚠️  Permission restriction may not be working - admin user created');
    }

    console.log('\n🎉 Edge Function testing completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testEdgeFunctions().catch(console.error);
