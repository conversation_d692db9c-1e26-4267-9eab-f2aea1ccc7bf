-- Public-safe impact functions for guest landing page
-- Apply this in Supabase SQL Editor

-- Summary: totals (rounded)
create or replace function get_public_impact_summary()
returns table (
  students int,
  schools int,
  sessions int,
  books int,
  districts int
)
language sql
security definer
as $$
  with fr as (
    select coalesce(sum(total_students),0) as students,
           coalesce(count(id),0) as sessions
    from field_reports
  ), ss as (
    select count(*)::int as schools, count(distinct ad.district)::int as districts
    from schools s
    left join administrative_divisions ad on s.division_id = ad.id
    where s.registration_status = 'active'
  ), bd as (
    select coalesce(sum(bd.quantity),0)::int as books
    from book_distributions bd
    join book_inventory bi on bd.inventory_id = bi.id
    join books b on bi.book_id = b.id
    where bd.status in ('completed','in_progress')
      and lower(b.title) like '%ilead%'
  )
  select
    (round(fr.students/10.0)*10)::int as students,
    ss.schools,
    (round(fr.sessions/10.0)*10)::int as sessions,
    bd.books,
    ss.districts
  from fr, ss, bd;
$$;

-- Trends: last N periods by week/month
create or replace function get_public_trends(period text default 'weekly', periods int default 12)
returns table (
  period_start date,
  students int,
  sessions int
)
language sql
security definer
as $$
  with params as (
    select case when period = 'monthly' then 'month'::text else 'week'::text end as grp,
           greatest(1, least(52, periods)) as n
  ), agg as (
    select date_trunc((select grp from params), report_date)::date as period_start,
           sum(total_students)::int as students,
           count(id)::int as sessions
    from field_reports
    group by 1
    order by 1 desc
    limit (select n from params)
  )
  select period_start,
         (round(coalesce(students,0)/10.0)*10)::int as students,
         (round(coalesce(sessions,0)/10.0)*10)::int as sessions
  from agg
  order by period_start asc;
$$;

-- Books breakdown: iLead/iDo/iChoose only
create or replace function get_public_books_breakdown()
returns table (
  title text,
  total int
)
language sql
security definer
as $$
  with t as (
    select
      case when lower(b.title) like '%ilead%' then 'iLead'
           when lower(b.title) like '%ido%' then 'iDo'
           when lower(b.title) like '%ichoose%' then 'iChoose'
           else 'Other' end as title,
      sum(bd.quantity)::int as total
    from book_distributions bd
    join book_inventory bi on bd.inventory_id = bi.id
    join books b on bi.book_id = b.id
    where bd.status in ('completed','in_progress')
    group by 1
  )
  select title, total from t where title in ('iLead','iDo','iChoose')
  union all
  select 'iLead'::text, 0 where not exists (select 1 from t where title='iLead')
  union all
  select 'iDo'::text, 0 where not exists (select 1 from t where title='iDo')
  union all
  select 'iChoose'::text, 0 where not exists (select 1 from t where title='iChoose');
$$;

-- Coverage summary: top 5 districts + Other
create or replace function get_public_coverage_summary()
returns table (
  district text,
  schools_count int
)
language sql
security definer
as $$
  with base as (
    select coalesce(ad.district,'Unknown') as district, count(*)::int as cnt
    from schools s
    left join administrative_divisions ad on s.division_id = ad.id
    where s.registration_status = 'active'
    group by 1
  ), ranked as (
    select district, cnt, row_number() over (order by cnt desc) as rn
    from base
  ), top5 as (
    select district, cnt from ranked where rn <= 5
  ), other as (
    select 'Other'::text as district, coalesce(sum(cnt),0)::int as cnt
    from ranked where rn > 5 or cnt < 5
  )
  select district, cnt as schools_count from top5
  union all
  select district, cnt from other where cnt > 0;
$$;

-- Grants to anon role
grant execute on function get_public_impact_summary() to anon;
grant execute on function get_public_trends(text,int) to anon;
grant execute on function get_public_books_breakdown() to anon;
grant execute on function get_public_coverage_summary() to anon;
