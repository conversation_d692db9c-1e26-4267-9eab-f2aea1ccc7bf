
import { useState, useCallback, useRef } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';

// Use Supabase generated types for Profile
type Profile = Database['public']['Functions']['get_user_profile']['Returns'][0];
type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];

export const useProfile = () => {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [profileLoading, setProfileLoading] = useState(false);
  const processingRef = useRef<string | null>(null);
  const adminCheckRef = useRef<string | null>(null);

  const checkAndAssignAdminRole = useCallback(async (userId: string) => {
    // Prevent multiple admin checks for the same user
    if (adminCheckRef.current === userId) {
      return false;
    }

    try {
      adminCheckRef.current = userId;
      
      const { data, error } = await supabase.functions.invoke('assign-admin-role', {
        body: { userId }
      });

      if (error) {
        console.error('Error checking admin role:', error);
        return false;
      }
      
      return data?.updated || false;
    } catch (error) {
      console.error('Error calling assign-admin-role function:', error);
      return false;
    } finally {
      // Clear the ref after a delay to allow for potential retries
      setTimeout(() => {
        if (adminCheckRef.current === userId) {
          adminCheckRef.current = null;
        }
      }, 1000);
    }
  }, []);

  const createProfileWithUpsert = useCallback(async (user: User) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          name: user.user_metadata?.name || user.email || 'User',
          role: 'staff',
          country: 'Uganda'
        }, {
          onConflict: 'id',
          ignoreDuplicates: false
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating/updating profile:', error);
        return;
      }

      if (data) {
        setProfile(data);
      }
    } catch (error) {
      console.error('Error in createProfileWithUpsert:', error);
    }
  }, []);

  const fetchOrCreateProfile = useCallback(async (user: User) => {
    // Prevent multiple fetches for the same user
    if (processingRef.current === user.id) {
      return;
    }

    try {
      processingRef.current = user.id;
      setProfileLoading(true);

      // Check admin role first (but don't wait for profile refresh)
      checkAndAssignAdminRole(user.id);

      const { data, error } = await supabase
        .rpc('get_user_profile', { user_id: user.id });

      if (error) {
        console.error('Error fetching profile:', error);
        return;
      }

      if (data && data.length > 0) {
        setProfile(data[0]);
      } else {
        // Profile doesn't exist, create it
        await createProfileWithUpsert(user);
      }
    } catch (error) {
      console.error('Error in fetchOrCreateProfile:', error);
    } finally {
      setProfileLoading(false);
      processingRef.current = null;
    }
  }, [checkAndAssignAdminRole, createProfileWithUpsert]);

  const createProfile = useCallback(async (user: User) => {
    // Deprecated - use createProfileWithUpsert instead
    await createProfileWithUpsert(user);
  }, [createProfileWithUpsert]);

  return {
    profile,
    profileLoading,
    setProfile,
    fetchOrCreateProfile,
    createProfile,
    checkAndAssignAdminRole
  };
};
