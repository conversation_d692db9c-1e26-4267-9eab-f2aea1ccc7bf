// Authentication middleware for API routes
import { createClient } from '@supabase/supabase-js';
import type { Database } from '../../src/types/database.types';

const supabase = createClient<Database>(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_ANON_KEY!
);

export interface AuthenticatedRequest {
  user: {
    id: string;
    email: string;
    role: string;
  };
}

export async function authenticateRequest(authHeader: string): Promise<AuthenticatedRequest | null> {
  try {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.substring(7);
    
    // Verify JWT token
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return null;
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError || !profile) {
      return null;
    }

    return {
      user: {
        id: user.id,
        email: user.email!,
        role: profile.role
      }
    };

  } catch (error) {
    console.error('Authentication error:', error);
    return null;
  }
}

export function requireAuth(allowedRoles?: string[]) {
  return async (req: any, res: any, next: any) => {
    const authHeader = req.headers.authorization;
    const auth = await authenticateRequest(authHeader);

    if (!auth) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    if (allowedRoles && !allowedRoles.includes(auth.user.role)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    req.auth = auth;
    next();
  };
}
