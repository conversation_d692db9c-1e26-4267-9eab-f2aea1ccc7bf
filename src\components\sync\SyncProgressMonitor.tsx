/**
 * Sync Progress Monitor Component
 * Real-time visualization of offline sync operations and performance
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Activity, 
  BarChart3, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Zap,
  TrendingUp,
  RefreshCw,
  Pause,
  Play,
  RotateCcw,
  Trash2
} from 'lucide-react';
import { useSyncProgressMonitor, type SyncProgressState, type SyncProgressMetrics, type SyncActivityEvent } from '@/hooks/useSyncProgressMonitor';

// ============================================================================
// SYNC STATUS INDICATOR COMPONENT
// ============================================================================

const SyncStatusIndicator: React.FC<{
  isActive: boolean;
  isHealthy: boolean;
  currentBatch: SyncProgressState['currentBatch'];
}> = ({ isActive, isHealthy, currentBatch }) => {
  const getStatusColor = () => {
    if (!isActive) return 'bg-gray-500';
    if (!isHealthy) return 'bg-red-500';
    if (currentBatch) return 'bg-blue-500 animate-pulse';
    return 'bg-green-500';
  };

  const getStatusText = () => {
    if (!isActive) return 'Inactive';
    if (!isHealthy) return 'Issues Detected';
    if (currentBatch) return 'Syncing';
    return 'Ready';
  };

  return (
    <div className="flex items-center space-x-2">
      <div className={`w-3 h-3 rounded-full ${getStatusColor()}`} />
      <span className="text-sm font-medium">{getStatusText()}</span>
    </div>
  );
};

// ============================================================================
// QUEUE STATS COMPONENT
// ============================================================================

const QueueStatsCard: React.FC<{
  queueStats: SyncProgressState['queueStats'];
  performance: SyncProgressState['performance'];
}> = ({ queueStats, performance }) => {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-orange-600">{queueStats.pending}</p>
            </div>
            <Clock className="h-5 w-5 text-orange-500" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Processing</p>
              <p className="text-2xl font-bold text-blue-600">{queueStats.processing}</p>
            </div>
            <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">{queueStats.completed}</p>
            </div>
            <CheckCircle className="h-5 w-5 text-green-500" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Failed</p>
              <p className="text-2xl font-bold text-red-600">{queueStats.failed}</p>
            </div>
            <AlertCircle className="h-5 w-5 text-red-500" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// ============================================================================
// PERFORMANCE METRICS COMPONENT
// ============================================================================

const PerformanceMetrics: React.FC<{
  performance: SyncProgressState['performance'];
  metrics: SyncProgressMetrics;
  formatDuration: (ms: number) => string;
  formatThroughput: (opsPerMin: number) => string;
}> = ({ performance, metrics, formatDuration, formatThroughput }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <BarChart3 className="h-5 w-5" />
          <span>Performance Metrics</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600">Success Rate</p>
            <p className="text-lg font-bold text-green-600">{performance.successRate.toFixed(1)}%</p>
          </div>
          
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600">Avg Operation Time</p>
            <p className="text-lg font-bold text-blue-600">{formatDuration(performance.averageOperationTime)}</p>
          </div>
          
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600">Throughput</p>
            <p className="text-lg font-bold text-purple-600">{formatThroughput(performance.throughput)}</p>
          </div>
          
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600">Queue Utilization</p>
            <p className="text-lg font-bold text-orange-600">{performance.queueUtilization.toFixed(1)}%</p>
          </div>
          
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600">Error Rate</p>
            <p className="text-lg font-bold text-red-600">{metrics.errorRate.toFixed(1)}%</p>
          </div>
          
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600">Avg Batch Size</p>
            <p className="text-lg font-bold text-indigo-600">{metrics.averageBatchSize.toFixed(1)}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// ============================================================================
// CURRENT BATCH COMPONENT
// ============================================================================

const CurrentBatchCard: React.FC<{
  currentBatch: SyncProgressState['currentBatch'];
  getProgressPercentage: () => number;
  formatDuration: (ms: number) => string;
}> = ({ currentBatch, getProgressPercentage, formatDuration }) => {
  if (!currentBatch) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-gray-500">
          <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>No active batch</p>
        </CardContent>
      </Card>
    );
  }

  const progress = getProgressPercentage();
  const elapsed = Date.now() - currentBatch.startTime;
  const remaining = Math.max(0, currentBatch.estimatedCompletion - Date.now());

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Current Batch</span>
          <Badge variant="outline">{currentBatch.operations.length} operations</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>{progress.toFixed(1)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-600">Elapsed</p>
              <p className="font-medium">{formatDuration(elapsed)}</p>
            </div>
            <div>
              <p className="text-gray-600">Remaining</p>
              <p className="font-medium">{formatDuration(remaining)}</p>
            </div>
          </div>
          
          <div>
            <p className="text-sm text-gray-600 mb-2">Operations</p>
            <div className="space-y-1">
              {currentBatch.operations.slice(0, 3).map((op: { operation: string; table: string; priority: string }, index: number) => (
                <div key={index} className="flex justify-between text-xs">
                  <span>{op.operation} {op.table}</span>
                  <Badge variant="secondary" size="sm">{op.priority}</Badge>
                </div>
              ))}
              {currentBatch.operations.length > 3 && (
                <p className="text-xs text-gray-500">
                  +{currentBatch.operations.length - 3} more operations
                </p>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// ============================================================================
// ACTIVITY LOG COMPONENT
// ============================================================================

const ActivityLog: React.FC<{
  recentActivity: SyncActivityEvent[];
  formatDuration: (ms: number) => string;
}> = ({ recentActivity, formatDuration }) => {
  const getEventIcon = (type: string) => {
    switch (type) {
      case 'OPERATION_STARTED':
        return <Play className="h-4 w-4 text-blue-500" />;
      case 'OPERATION_COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'OPERATION_FAILED':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'BATCH_STARTED':
        return <Zap className="h-4 w-4 text-purple-500" />;
      case 'BATCH_COMPLETED':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-64">
          <div className="space-y-2">
            {recentActivity.length === 0 ? (
              <p className="text-center text-gray-500 py-4">No recent activity</p>
            ) : (
              recentActivity.map((event) => (
                <div key={event.id} className="flex items-start space-x-3 p-2 rounded-lg hover:bg-gray-50">
                  <div className="flex-shrink-0 mt-1">
                    {getEventIcon(event.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">{event.message}</p>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <span>{formatTimestamp(event.timestamp)}</span>
                      {event.duration && (
                        <span>• {formatDuration(event.duration)}</span>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

// ============================================================================
// MAIN SYNC PROGRESS MONITOR COMPONENT
// ============================================================================

const SyncProgressMonitor: React.FC = () => {
  const {
    progressState,
    metrics,
    startMonitoring,
    stopMonitoring,
    resetMetrics,
    clearActivityLog,
    formatDuration,
    formatThroughput,
    getProgressPercentage,
    isHealthy,
  } = useSyncProgressMonitor();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Sync Progress Monitor</h1>
          <div className="flex items-center space-x-4 mt-2">
            <SyncStatusIndicator
              isActive={progressState.isActive}
              isHealthy={isHealthy()}
              currentBatch={progressState.currentBatch}
            />
            <span className="text-sm text-gray-500">
              Total: {progressState.queueStats.totalOperations} operations
            </span>
          </div>
        </div>
        
        <div className="flex space-x-2">
          <Button
            onClick={progressState.isActive ? stopMonitoring : startMonitoring}
            variant={progressState.isActive ? "destructive" : "default"}
            size="sm"
          >
            {progressState.isActive ? (
              <>
                <Pause className="h-4 w-4 mr-2" />
                Stop
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Start
              </>
            )}
          </Button>
          
          <Button onClick={resetMetrics} variant="outline" size="sm">
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          
          <Button onClick={clearActivityLog} variant="outline" size="sm">
            <Trash2 className="h-4 w-4 mr-2" />
            Clear Log
          </Button>
        </div>
      </div>

      {/* Queue Stats */}
      <QueueStatsCard
        queueStats={progressState.queueStats}
        performance={progressState.performance}
      />

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <CurrentBatchCard
              currentBatch={progressState.currentBatch}
              getProgressPercentage={getProgressPercentage}
              formatDuration={formatDuration}
            />
            
            <PerformanceMetrics
              performance={progressState.performance}
              metrics={metrics}
              formatDuration={formatDuration}
              formatThroughput={formatThroughput}
            />
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <PerformanceMetrics
            performance={progressState.performance}
            metrics={metrics}
            formatDuration={formatDuration}
            formatThroughput={formatThroughput}
          />
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <ActivityLog
            recentActivity={progressState.recentActivity}
            formatDuration={formatDuration}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SyncProgressMonitor;
