/**
 * Check-in Type Selector Component
 * Allows users to choose between school visit and office check-in
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { School, Building, MapPin, Clock, Users } from 'lucide-react';
import { CheckInType, CHECK_IN_TYPES } from '@/constants/officeLocations';

interface CheckInTypeSelectorProps {
  selectedType: CheckInType;
  onTypeChange: (type: CheckInType) => void;
  disabled?: boolean;
  className?: string;
}

const CheckInTypeSelector: React.FC<CheckInTypeSelectorProps> = ({
  selectedType,
  onTypeChange,
  disabled = false,
  className = '',
}) => {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Check-in Type
        </CardTitle>
        <CardDescription>
          Choose the type of check-in you want to perform
        </CardDescription>
      </CardHeader>
      <CardContent>
        <RadioGroup
          value={selectedType}
          onValueChange={onTypeChange}
          disabled={disabled}
          className="space-y-4"
        >
          {/* School Visit Option */}
          <div className="flex items-start space-x-3">
            <RadioGroupItem 
              value={CHECK_IN_TYPES.SCHOOL} 
              id="school-checkin"
              className="mt-1"
            />
            <div className="flex-1">
              <Label 
                htmlFor="school-checkin" 
                className="flex items-center gap-2 cursor-pointer"
              >
                <School className="h-4 w-4 text-blue-600" />
                <span className="font-medium">School Visit</span>
                <Badge variant="outline" className="text-xs">
                  Field Work
                </Badge>
              </Label>
              <div className="mt-1 text-sm text-gray-600">
                Check in when visiting a school for field activities, training sessions, or assessments
              </div>
              <div className="mt-2 flex flex-wrap gap-2 text-xs text-gray-500">
                <div className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  Student interaction
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  Field reports required
                </div>
              </div>
            </div>
          </div>

          {/* Office Check-in Option */}
          <div className="flex items-start space-x-3">
            <RadioGroupItem 
              value={CHECK_IN_TYPES.OFFICE} 
              id="office-checkin"
              className="mt-1"
            />
            <div className="flex-1">
              <Label 
                htmlFor="office-checkin" 
                className="flex items-center gap-2 cursor-pointer"
              >
                <Building className="h-4 w-4 text-green-600" />
                <span className="font-medium">Office Check-in</span>
                <Badge variant="outline" className="text-xs">
                  Administrative
                </Badge>
              </Label>
              <div className="mt-1 text-sm text-gray-600">
                Check in when working from the office for administrative tasks, meetings, or planning
              </div>
              <div className="mt-2 flex flex-wrap gap-2 text-xs text-gray-500">
                <div className="flex items-center gap-1">
                  <Building className="h-3 w-3" />
                  Office-based work
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  No field reports
                </div>
              </div>
            </div>
          </div>
        </RadioGroup>

        {/* Additional Information */}
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="text-sm text-blue-800">
            <strong>Note:</strong> You can switch between check-in types throughout the day based on your activities. 
            Each check-in type has different requirements and tracking features.
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CheckInTypeSelector;
