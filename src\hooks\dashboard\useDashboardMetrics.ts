import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';

export interface DashboardMetrics {
  // Field Staff Performance
  fieldStaff: {
    totalStaff: number;
    activeStaff: number;
    checkedInToday: number;
    averageHoursPerDay: number;
    checkInComplianceRate: number;
    reportSubmissionRate: number;
  };
  
  // Program Reach & Impact
  programReach: {
    totalStudentsReached: number;
    maleStudents: number;
    femaleStudents: number;
    schoolsCovered: number;
    totalSchools: number;
    sessionCompletionRate: number;
    averageAttendancePerSession: number;
    bookDistributionRate: number;
    monthlySchoolsComparison: number; // Percentage change from last month
    weeklyStudentsComparison: number; // Percentage change from last week
    studentEngagementPercentage: number; // (students in sessions / total students in schools) * 100
    studentEngagementTrend: number; // Percentage change from last period
    totalStudentsInSchools: number; // Total students recorded across all schools
  };
  
  // Operational Efficiency
  operational: {
    taskCompletionRate: number;
    averageTaskCompletionTime: number;
    reportQualityScore: number;
    resourceUtilization: number;
    offlineSyncSuccessRate: number;
  };
  
  // Quality Indicators
  quality: {
    sessionAttendanceTrend: number;
    studentEngagementScore: number;
    followUpCompletionRate: number;
    challengeResolutionTime: number;
    feedbackSentiment: number;
  };
}

export interface ActivitySummary {
  todayActivities: {
    checkIns: number;
    sessions: number;
    reports: number;
    visits: number;
  };
  weeklyTrend: {
    date: string;
    activities: number;
    students: number;
    hours: number;
  }[];
  criticalAlerts: {
    id: string;
    type: 'error' | 'warning' | 'info';
    message: string;
    timestamp: string;
    priority: 'high' | 'medium' | 'low';
  }[];
}

export interface StaffPerformance {
  id: string;
  name: string;
  role: string;
  isCheckedIn: boolean;
  currentSchool?: string;
  todayHours: number;
  weeklyHours: number;
  studentsReached: number;
  schoolsVisited: number;
  reportsSubmitted: number;
  performanceScore: number;
  lastActivity: string;
}

export const useDashboardMetrics = () => {
  const { user, profile } = useAuth();

  return useQuery({
    queryKey: ['dashboard-metrics-optimized', profile?.id, profile?.role],
    queryFn: async (): Promise<DashboardMetrics> => {
      console.log('🔍 Fetching dashboard metrics...');

      // Try the optimized RPC function first
      try {
        // Temporarily disable RPC function to use more complete fallback queries
        // The RPC function is missing gender breakdown, student engagement data, and trend comparisons
        console.log('🔄 Using fallback queries for complete dashboard data');
        throw new Error('Using fallback for complete data');

        const { data, error } = await supabase.rpc('get_dashboard_metrics', {
          p_user_id: user?.id || null,
          p_user_role: profile?.role || null,
        });

        if (error) {
          console.warn('RPC function not available, falling back to direct queries:', error.message);
          throw error; // This will trigger the fallback
        }

        console.log('✅ Dashboard metrics fetched via RPC function');

        // Transform the JSON response to match our interface
        const metrics = data as Record<string, unknown>;

        return {
          fieldStaff: {
            totalStaff: metrics.field_staff.total_staff,
            activeStaff: metrics.field_staff.active_staff,
            checkedInToday: metrics.field_staff.checked_in_today,
            averageHoursPerDay: metrics.field_staff.average_hours_per_day,
            checkInComplianceRate: metrics.field_staff.check_in_compliance_rate,
            reportSubmissionRate: metrics.field_staff.report_submission_rate,
          },
          programReach: {
            totalStudentsReached: metrics.program_reach.total_students_reached,
            maleStudents: metrics.program_reach.male_students || 0,
            femaleStudents: metrics.program_reach.female_students || 0,
            schoolsCovered: metrics.program_reach.schools_covered,
            totalSchools: metrics.program_reach.total_schools,
            sessionCompletionRate: metrics.program_reach.session_completion_rate,
            averageAttendancePerSession: metrics.program_reach.average_attendance_per_session,
            bookDistributionRate: metrics.program_reach.book_distribution_rate,
            monthlySchoolsComparison: metrics.program_reach.monthly_schools_comparison || 0,
            weeklyStudentsComparison: metrics.program_reach.weekly_students_comparison || 0,
          },
          operational: {
            taskCompletionRate: metrics.operational.task_completion_rate,
            averageTaskCompletionTime: metrics.operational.average_task_completion_time,
            reportQualityScore: metrics.operational.report_quality_score,
            resourceUtilization: metrics.operational.resource_utilization,
            offlineSyncSuccessRate: metrics.operational.offline_sync_success_rate,
          },
          quality: {
            sessionAttendanceTrend: metrics.quality.session_attendance_trend || 0,
            studentEngagementScore: metrics.quality.student_engagement_score,
            followUpCompletionRate: metrics.quality.follow_up_completion_rate,
            challengeResolutionTime: metrics.quality.challenge_resolution_time,
            feedbackSentiment: metrics.quality.feedback_sentiment,
          },
        };
      } catch (error) {
        console.warn('Optimized dashboard function not available, using fallback queries:', error);

        // Fallback to original implementation
        // Fetch field staff data
        const { data: profiles } = await supabase
          .from('profiles')
          .select('*')
          .eq('role', 'field_staff');

        // Fetch today's attendance
        const today = new Date().toISOString().split('T')[0];
        const { data: todayAttendance } = await supabase
          .from('field_staff_attendance')
          .select('*')
          .eq('attendance_date', today);

        // Fetch attendance records for this week
        const weekStart = new Date();
        weekStart.setDate(weekStart.getDate() - weekStart.getDay());
        const { data: weeklyAttendance } = await supabase
          .from('field_staff_attendance')
          .select('*')
          .gte('attendance_date', weekStart.toISOString().split('T')[0]);

        // Fetch schools (only active schools for student count)
        const { data: schools } = await supabase
          .from('schools')
          .select('id, registration_status, student_count')
          .eq('registration_status', 'active');

        // Fetch ALL field reports for total student impact
        const { data: allReports } = await supabase
          .from('field_reports')
          .select('total_students, male_participants, female_participants, report_date');

        // Fetch field reports for this month (for other calculations)
        const monthStart = new Date();
        monthStart.setDate(1);
        const { data: monthlyReports } = await supabase
          .from('field_reports')
          .select('total_students, male_participants, female_participants, report_date')
          .gte('report_date', monthStart.toISOString().split('T')[0]);

        // Fetch last month's reports for comparison
        const lastMonthStart = new Date();
        lastMonthStart.setMonth(lastMonthStart.getMonth() - 1);
        lastMonthStart.setDate(1);
        const lastMonthEnd = new Date(monthStart);
        lastMonthEnd.setDate(0); // Last day of previous month
        const { data: lastMonthReports } = await supabase
          .from('field_reports')
          .select('total_students, male_participants, female_participants')
          .gte('report_date', lastMonthStart.toISOString().split('T')[0])
          .lte('report_date', lastMonthEnd.toISOString().split('T')[0]);

        // Fetch monthly attendance for schools reached calculation
        const { data: monthlyAttendance } = await supabase
          .from('field_staff_attendance')
          .select('school_id, attendance_date')
          .gte('attendance_date', monthStart.toISOString().split('T')[0]);

        // Fetch last month's attendance for comparison
        const { data: lastMonthAttendance } = await supabase
          .from('field_staff_attendance')
          .select('school_id')
          .gte('attendance_date', lastMonthStart.toISOString().split('T')[0])
          .lte('attendance_date', lastMonthEnd.toISOString().split('T')[0]);

        // Fetch last week's reports for weekly comparison
        const lastWeekStart = new Date();
        lastWeekStart.setDate(lastWeekStart.getDate() - 14); // Two weeks ago
        const lastWeekEnd = new Date();
        lastWeekEnd.setDate(lastWeekEnd.getDate() - 7); // One week ago
        const { data: lastWeekReports } = await supabase
          .from('field_reports')
          .select('total_students, male_participants, female_participants')
          .gte('report_date', lastWeekStart.toISOString().split('T')[0])
          .lte('report_date', lastWeekEnd.toISOString().split('T')[0]);

        // Fetch tasks
        const { data: tasks } = await supabase
          .from('tasks')
          .select('*');

        // Calculate metrics
        const totalStaff = profiles?.length || 0;
        const activeStaff = profiles?.filter(p => p.is_active !== false).length || 0;
        const checkedInToday = todayAttendance?.length || 0;

        // Calculate student metrics with gender breakdown from ALL reports (cumulative impact)
        const totalStudentsReached = allReports?.reduce((sum, report) =>
          sum + (report.total_students || 0), 0) || 0;
        const maleStudents = allReports?.reduce((sum, report) =>
          sum + (report.male_participants || 0), 0) || 0;
        const femaleStudents = allReports?.reduce((sum, report) =>
          sum + (report.female_participants || 0), 0) || 0;

        // Calculate schools reached from monthly check-ins (not just today)
        const schoolsCovered = new Set(monthlyAttendance?.map(a => a.school_id)).size;
        const totalSchools = schools?.length || 0;

        // Calculate student engagement percentage (students in sessions / total students in schools)
        const totalStudentsInSchools = schools?.reduce((sum, school) =>
          sum + (school.student_count || 0), 0) || 0;
        const studentEngagementPercentage = totalStudentsInSchools > 0
          ? (totalStudentsReached / totalStudentsInSchools) * 100
          : 0;







        // Calculate student engagement trend (compare current vs last month)
        const lastMonthStudentsReached = lastMonthReports?.reduce((sum, report) =>
          sum + (report.total_students || 0), 0) || 0;
        const lastMonthEngagementPercentage = totalStudentsInSchools > 0
          ? (lastMonthStudentsReached / totalStudentsInSchools) * 100
          : 0;
        const studentEngagementTrend = lastMonthEngagementPercentage > 0
          ? Math.round(((studentEngagementPercentage - lastMonthEngagementPercentage) / lastMonthEngagementPercentage) * 100)
          : 0;

        // Calculate comparisons
        const lastMonthStudents = lastMonthReports?.reduce((sum, report) =>
          sum + (report.total_students || 0), 0) || 0;
        const lastWeekStudents = lastWeekReports?.reduce((sum, report) =>
          sum + (report.total_students || 0), 0) || 0;
        const lastMonthSchools = new Set(lastMonthAttendance?.map(a => a.school_id)).size;

        // Calculate this week's new students for proper weekly comparison
        const thisWeekStart = new Date();
        thisWeekStart.setDate(thisWeekStart.getDate() - thisWeekStart.getDay()); // Start of this week
        const thisWeekStudents = allReports?.filter(report =>
          new Date(report.report_date) >= thisWeekStart
        ).reduce((sum, report) => sum + (report.total_students || 0), 0) || 0;

        const weeklyStudentsComparison = lastWeekStudents > 0
          ? ((thisWeekStudents - lastWeekStudents) / lastWeekStudents) * 100
          : 0;
        const monthlySchoolsComparison = lastMonthSchools > 0
          ? ((schoolsCovered - lastMonthSchools) / lastMonthSchools) * 100
          : 0;
        const completedTasks = tasks?.filter(t => t.status === 'completed').length || 0;
        const totalTasks = tasks?.length || 0;
        const taskCompletionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

        const totalHours = weeklyAttendance?.reduce((sum, att) =>
          sum + ((att.total_duration_minutes || 0) / 60), 0) || 0;
        const averageHoursPerDay = weeklyAttendance?.length ? totalHours / 7 : 0;

        console.log('✅ Dashboard metrics calculated via fallback queries');

        return {
          fieldStaff: {
            totalStaff,
            activeStaff,
            checkedInToday,
            averageHoursPerDay,
            checkInComplianceRate: totalStaff > 0 ? (checkedInToday / totalStaff) * 100 : 0,
            reportSubmissionRate: 85, // Placeholder - calculate from actual data
          },
          programReach: {
            totalStudentsReached,
            maleStudents,
            femaleStudents,
            schoolsCovered,
            totalSchools,
            sessionCompletionRate: 92, // Placeholder
            averageAttendancePerSession: monthlyReports?.length ? totalStudentsReached / monthlyReports.length : 0,
            bookDistributionRate: 78, // Placeholder
            monthlySchoolsComparison,
            weeklyStudentsComparison,
            studentEngagementPercentage,
            studentEngagementTrend,
            totalStudentsInSchools,
          },
          operational: {
            taskCompletionRate,
            averageTaskCompletionTime: 2.5, // Placeholder - days
            reportQualityScore: 88, // Placeholder
            resourceUtilization: 76, // Placeholder
            offlineSyncSuccessRate: 94, // Placeholder
          },
          quality: {
            sessionAttendanceTrend: 5.2, // Placeholder - percentage increase
            studentEngagementScore: 4.3, // Placeholder - out of 5
            followUpCompletionRate: 82, // Placeholder
            challengeResolutionTime: 1.8, // Placeholder - days
            feedbackSentiment: 4.1, // Placeholder - out of 5
          },
        };
      }
    },
    enabled: !!user && !!profile?.role,
    staleTime: 60000, // 1 minute
    refetchInterval: 300000, // 5 minutes
  });
};

export const useActivitySummary = () => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: ['activity-summary', profile?.id],
    queryFn: async (): Promise<ActivitySummary> => {
      const today = new Date().toISOString().split('T')[0];
      
      // Fetch today's activities
      const { data: todayAttendance } = await supabase
        .from('field_staff_attendance')
        .select('*')
        .eq('attendance_date', today);
      
      const { data: todayReports } = await supabase
        .from('field_reports')
        .select('*')
        .eq('report_date', today);
      
      // Fetch weekly trend data
      const weeklyTrend = [];
      for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        
        const { data: dayAttendance } = await supabase
          .from('field_staff_attendance')
          .select('*')
          .eq('attendance_date', dateStr);
        
        // Get field reports for this day
        const { data: dayReports } = await supabase
          .from('field_reports')
          .select('*')
          .eq('report_date', dateStr);

        weeklyTrend.push({
          date: dateStr,
          activities: (dayAttendance?.length || 0) + (dayReports?.length || 0),
          students: dayReports?.reduce((sum, report) => sum + (report.total_students || 0), 0) || 0,
          hours: dayAttendance?.reduce((sum, att) => sum + ((att.total_duration_minutes || 0) / 60), 0) || 0,
        });
      }
      
      // Generate sample critical alerts
      const criticalAlerts = [
        {
          id: '1',
          type: 'warning' as const,
          message: 'GPS verification failed for 2 staff members',
          timestamp: new Date().toISOString(),
          priority: 'medium' as const,
        },
        {
          id: '2',
          type: 'error' as const,
          message: '3 overdue field reports require attention',
          timestamp: new Date().toISOString(),
          priority: 'high' as const,
        },
      ];
      
      // Use optimized RPC function instead
      const { data: activityData, error: activityError } = await supabase.rpc('get_dashboard_activity_summary', {
        p_user_id: null, // Will use current user from auth context
        p_days_back: 7,
      });

      if (activityError) {
        console.error('❌ Error fetching activity summary:', activityError);
        // Fallback to basic data
        return {
          todayActivities: {
            checkIns: todayAttendance?.length || 0,
            sessions: todayReports?.reduce((sum, r) => sum + (r.round_table_sessions_count || 0), 0) || 0,
            reports: todayReports?.length || 0,
            visits: todayAttendance?.length || 0,
          },
          weeklyTrend,
          criticalAlerts,
        };
      }

      const summary = activityData as Record<string, unknown>;

      return {
        todayActivities: {
          checkIns: summary.today_summary.check_ins,
          sessions: summary.today_summary.check_ins, // Approximate sessions as check-ins
          reports: summary.today_summary.reports_submitted,
          visits: summary.today_summary.check_ins,
        },
        weeklyTrend: summary.weekly_trend || weeklyTrend,
        criticalAlerts,
      };
    },
    enabled: !!profile?.id,
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // 1 minute for real-time updates
  });
};

export const useStaffPerformance = () => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: ['staff-performance', profile?.id],
    queryFn: async (): Promise<StaffPerformance[]> => {
      const { data: fieldStaff } = await supabase
        .from('profiles')
        .select('*')
        .eq('role', 'field_staff');
      
      if (!fieldStaff) return [];
      
      const today = new Date().toISOString().split('T')[0];
      const weekStart = new Date();
      weekStart.setDate(weekStart.getDate() - weekStart.getDay());
      
      const staffPerformance = await Promise.all(
        fieldStaff.map(async (staff) => {
          // Check if checked in today
          const { data: todayAttendance } = await supabase
            .from('field_staff_attendance')
            .select('*, schools(name)')
            .eq('staff_id', staff.id)
            .eq('attendance_date', today)
            .is('check_out_time', null)
            .maybeSingle();
          
          // Get weekly attendance records
          const { data: weeklyAttendance } = await supabase
            .from('field_staff_attendance')
            .select('*')
            .eq('staff_id', staff.id)
            .gte('attendance_date', weekStart.toISOString().split('T')[0]);

          const weeklyHours = weeklyAttendance?.reduce((sum, att) => sum + ((att.total_duration_minutes || 0) / 60), 0) || 0;
          // Get field reports for this staff member this week
          const { data: weeklyReports } = await supabase
            .from('field_reports')
            .select('*')
            .eq('staff_id', staff.id)
            .gte('report_date', weekStart.toISOString().split('T')[0]);

          const studentsReached = weeklyReports?.reduce((sum, report) => sum + (report.total_students || 0), 0) || 0;
          const schoolsVisited = new Set(weeklyAttendance?.map(att => att.school_id) || []).size;
          
          return {
            id: staff.id,
            name: staff.full_name || 'Unknown',
            role: staff.role,
            isCheckedIn: !!todayAttendance,
            currentSchool: todayAttendance?.schools?.name,
            todayHours: 0, // Calculate from today's timesheet
            weeklyHours,
            studentsReached,
            schoolsVisited,
            reportsSubmitted: 0, // Calculate from field reports
            performanceScore: Math.min(100, (weeklyHours * 10) + (studentsReached * 0.1)), // Simple calculation
            lastActivity: staff.updated_at || staff.created_at,
          };
        })
      );
      
      return staffPerformance;
    },
    enabled: !!profile?.id && (profile?.role === 'admin' || profile?.role === 'program_officer'),
    staleTime: 120000, // 2 minutes
    refetchInterval: 300000, // 5 minutes
  });
};

// Book Distribution Metrics Hook
export interface BookDistributionMetrics {
  totalDistributions: number;
  totalBooksDistributed: number;
  distributionsThisMonth: number;
  booksThisMonth: number;
  activeDistributions: number;
  schoolsServed: number;
  totalBooksInStock: number; // Total books available in inventory
  topBooks: Array<{
    book_title: string;
    total_distributed: number;
  }>;
}

export const useBookDistributionMetrics = () => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['book-distribution-metrics'],
    queryFn: async (): Promise<BookDistributionMetrics> => {
      console.log('🔍 Fetching book distribution metrics...');

      // Fetch distribution statistics
      const { data, error } = await supabase
        .rpc('get_distribution_statistics');

      if (error) {
        console.error('Error fetching distribution statistics:', error);
        throw error;
      }

      // Fetch total book stock from inventory
      const { data: bookStock, error: stockError } = await supabase
        .from('book_inventory')
        .select('total_quantity');

      if (stockError) {
        console.error('Error fetching book stock:', stockError);
      }

      const totalBooksInStock = bookStock?.reduce((sum, book) => sum + (book.total_quantity || 0), 0) || 0;

      const result = data?.[0];
      if (!result) {
        return {
          totalDistributions: 0,
          totalBooksDistributed: 0,
          distributionsThisMonth: 0,
          booksThisMonth: 0,
          activeDistributions: 0,
          schoolsServed: 0,
          totalBooksInStock,
          topBooks: []
        };
      }

      return {
        totalDistributions: result.total_distributions || 0,
        totalBooksDistributed: result.total_books_distributed || 0,
        distributionsThisMonth: result.distributions_this_month || 0,
        booksThisMonth: result.books_this_month || 0,
        activeDistributions: result.active_distributions || 0,
        schoolsServed: result.schools_served || 0,
        totalBooksInStock,
        topBooks: Array.isArray(result.top_books) ? result.top_books : []
      };
    },
    enabled: !!profile,
    staleTime: 300000, // 5 minutes
    refetchInterval: 300000, // 5 minutes
  });
};
