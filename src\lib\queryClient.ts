
import { QueryClient } from '@tanstack/react-query';
import { QueryCacheManager, CACHE_TIMES } from '@/utils/queryCache';

// Create a client with enhanced caching options
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: CACHE_TIMES.DYNAMIC.staleTime, // 2 minutes default
      gcTime: CACHE_TIMES.DYNAMIC.gcTime, // 10 minutes default
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
      refetchIntervalInBackground: false,
      retry: 2,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
      retryDelay: 1000,
    },
  },
});

// Initialize cache manager (lazy initialization to avoid circular dependencies)
let cacheManagerInstance: QueryCacheManager | null = null;
export const getCacheManager = () => {
  if (!cacheManagerInstance) {
    cacheManagerInstance = new QueryCacheManager(queryClient);
  }
  return cacheManagerInstance;
};

// Enhanced query client with performance monitoring and cache management
export const createOptimizedQueryClient = () => {
  const cacheManager = getCacheManager();

  // Set up periodic cache cleanup
  setInterval(() => {
    cacheManager.cleanupStaleCache();
  }, 10 * 60 * 1000); // Every 10 minutes

  // Log cache statistics in development
  if (process.env.NODE_ENV === 'development') {
    setInterval(() => {
      const stats = cacheManager.getCacheStats();
      console.log('📊 Query Cache Stats:', stats);
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  return queryClient;
};

// Utility functions for cache management
export const warmUserCache = (userId: string, userRole: string) => {
  return getCacheManager().warmCache(userId, userRole);
};

export const invalidateQueriesForEntity = (entityType: string, immediate = false) => {
  const patterns = {
    school: [['schools'], ['schools-paginated'], ['dashboard']],
    staff: [['staff-members'], ['staff-paginated'], ['dashboard']],
    task: [['tasks'], ['my-tasks'], ['managed-tasks'], ['dashboard']],
    field_report: [['field-reports'], ['dashboard']],
    attendance: [['field-staff-attendance'], ['attendance'], ['dashboard']],
    notification: [['notifications'], ['unread-notification-count']],
  };

  const pattern = patterns[entityType as keyof typeof patterns];
  if (pattern) {
    getCacheManager().invalidateQueries(pattern, immediate);
  }
};

export const getCacheStats = () => {
  return getCacheManager().getCacheStats();
};

export const refreshAllQueries = () => {
  return getCacheManager().refreshAllActiveQueries();
};

// Query key factories for consistency across the app
export const queryKeys = {
  all: ['queries'] as const,
  tasks: {
    all: ['tasks'] as const,
    recent: (limit: number) => ['tasks', 'recent', limit] as const,
    list: (params: {
      userId?: string;
      statusFilter?: string;
      assignedFilter?: string;
      includeComments?: boolean;
      limit?: number;
      profileId?: string;
      profileRole?: string;
    }) => ['tasks', 'list', params] as const,
    detail: (id: string) => ['tasks', 'detail', id] as const,
  },
  activities: {
    all: ['activities'] as const,
    recent: (limit: number) => ['activities', 'recent', limit] as const,
    user: (userId: string, limit: number) => ['activities', 'user', userId, limit] as const,
    stats: (days: number) => ['activities', 'stats', days] as const,
  },
  schools: {
    all: ['schools'] as const,
    list: ['schools', 'list'] as const,
    divisions: ['schools', 'divisions'] as const,
  },
  distributions: {
    all: ['distributions'] as const,
    list: ['distributions', 'list'] as const,
  },
  inventory: {
    all: ['inventory'] as const,
    books: ['inventory', 'books'] as const,
  },
  profiles: {
    all: ['profiles'] as const,
    current: ['profiles', 'current'] as const,
    detail: (id: string) => ['profiles', 'detail', id] as const,
  },
};

// Mutation key factories
export const mutationKeys = {
  tasks: {
    create: ['tasks', 'create'] as const,
    update: ['tasks', 'update'] as const,
    updateStatus: ['tasks', 'updateStatus'] as const,
    delete: ['tasks', 'delete'] as const,
  },
  schools: {
    create: ['schools', 'create'] as const,
    update: ['schools', 'update'] as const,
  },
  distributions: {
    create: ['distributions', 'create'] as const,
  },
  activities: {
    log: ['activities', 'log'] as const,
  },
};
