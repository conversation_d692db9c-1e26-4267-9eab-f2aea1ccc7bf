/**
 * Office Check-in Testing Utilities
 * Provides mock data and testing helpers for office check-in functionality
 */

import { OfficeLocation, MAIN_OFFICE } from '@/constants/officeLocations';
import { LocationData } from '@/utils/officeGeofencing';

/**
 * Mock GPS positions for testing
 */
export const MOCK_LOCATIONS = {
  // Inside main office (within 1km)
  INSIDE_OFFICE: {
    latitude: 0.32581475334630144,
    longitude: 32.57564046659227,
    accuracy: 10,
    timestamp: Date.now(),
  } as LocationData,

  // Near office boundary (950m away)
  NEAR_OFFICE_BOUNDARY: {
    latitude: 0.3349,
    longitude: 32.5756,
    accuracy: 15,
    timestamp: Date.now(),
  } as LocationData,

  // Outside office geofence (1.5km away)
  OUTSIDE_OFFICE: {
    latitude: 0.3400,
    longitude: 32.5900,
    accuracy: 20,
    timestamp: Date.now(),
  } as LocationData,

  // Poor GPS accuracy
  POOR_ACCURACY: {
    latitude: 0.32581475334630144,
    longitude: 32.57564046659227,
    accuracy: 150,
    timestamp: Date.now(),
  } as LocationData,

  // Different city (Kampala)
  DIFFERENT_CITY: {
    latitude: 0.3476,
    longitude: 32.5825,
    accuracy: 10,
    timestamp: Date.now(),
  } as LocationData,
};

/**
 * Mock office locations for testing
 */
export const MOCK_OFFICES: OfficeLocation[] = [
  MAIN_OFFICE,
  {
    id: 'test-office-2',
    name: 'Test Office 2',
    coordinates: {
      latitude: 0.3500,
      longitude: 32.6000,
    },
    geofenceRadius: 500,
    address: 'Test Office 2 Address',
    description: 'Secondary test office',
    isActive: true,
  },
  {
    id: 'inactive-office',
    name: 'Inactive Office',
    coordinates: {
      latitude: 0.3600,
      longitude: 32.6100,
    },
    geofenceRadius: 1000,
    address: 'Inactive Office Address',
    description: 'Inactive test office',
    isActive: false,
  },
];

/**
 * Mock check-in data for testing
 */
export const MOCK_CHECKIN_DATA = {
  OFFICE_CHECKIN: {
    office_id: MAIN_OFFICE.id,
    check_in_type: 'office' as const,
    latitude: MOCK_LOCATIONS.INSIDE_OFFICE.latitude,
    longitude: MOCK_LOCATIONS.INSIDE_OFFICE.longitude,
    accuracy: MOCK_LOCATIONS.INSIDE_OFFICE.accuracy,
    verification_method: 'gps',
    device_info: { test: true },
    network_info: { online: true },
    offline_sync: false,
  },

  SCHOOL_CHECKIN: {
    school_id: 'test-school-id',
    check_in_type: 'school' as const,
    latitude: 0.3000,
    longitude: 32.5000,
    accuracy: 10,
    verification_method: 'gps',
    device_info: { test: true },
    network_info: { online: true },
    offline_sync: false,
  },
};

/**
 * Mock GPS error scenarios
 */
export const MOCK_GPS_ERRORS = {
  PERMISSION_DENIED: {
    code: 1,
    message: 'User denied the request for Geolocation.',
  },
  POSITION_UNAVAILABLE: {
    code: 2,
    message: 'Location information is unavailable.',
  },
  TIMEOUT: {
    code: 3,
    message: 'The request to get user location timed out.',
  },
};

/**
 * Test scenarios for office check-in validation
 */
export const TEST_SCENARIOS = {
  VALID_OFFICE_CHECKIN: {
    name: 'Valid office check-in',
    location: MOCK_LOCATIONS.INSIDE_OFFICE,
    office: MAIN_OFFICE,
    expectedResult: { isValid: true },
  },
  
  OUTSIDE_GEOFENCE: {
    name: 'Outside office geofence',
    location: MOCK_LOCATIONS.OUTSIDE_OFFICE,
    office: MAIN_OFFICE,
    expectedResult: { isValid: false },
  },
  
  POOR_GPS_ACCURACY: {
    name: 'Poor GPS accuracy',
    location: MOCK_LOCATIONS.POOR_ACCURACY,
    office: MAIN_OFFICE,
    expectedResult: { isValid: true, hasWarning: true },
  },
  
  BOUNDARY_CASE: {
    name: 'Near office boundary',
    location: MOCK_LOCATIONS.NEAR_OFFICE_BOUNDARY,
    office: MAIN_OFFICE,
    expectedResult: { isValid: true },
  },
};

/**
 * Mock geolocation API for testing
 */
export class MockGeolocation {
  private static instance: MockGeolocation;
  private mockPosition: LocationData | null = null;
  private mockError: GeolocationPositionError | null = null;
  private delay: number = 100;

  static getInstance(): MockGeolocation {
    if (!MockGeolocation.instance) {
      MockGeolocation.instance = new MockGeolocation();
    }
    return MockGeolocation.instance;
  }

  setMockPosition(position: LocationData): void {
    this.mockPosition = position;
    this.mockError = null;
  }

  setMockError(error: Partial<GeolocationPositionError>): void {
    this.mockError = {
      code: error.code || 1,
      message: error.message || 'Mock GPS error',
      PERMISSION_DENIED: 1,
      POSITION_UNAVAILABLE: 2,
      TIMEOUT: 3,
    } as GeolocationPositionError;
    this.mockPosition = null;
  }

  setDelay(delay: number): void {
    this.delay = delay;
  }

  getCurrentPosition(
    success: PositionCallback,
    error?: PositionErrorCallback,
    options?: PositionOptions
  ): void {
    setTimeout(() => {
      if (this.mockError && error) {
        error(this.mockError);
      } else if (this.mockPosition && success) {
        const mockGeolocationPosition: GeolocationPosition = {
          coords: {
            latitude: this.mockPosition.latitude,
            longitude: this.mockPosition.longitude,
            accuracy: this.mockPosition.accuracy || 10,
            altitude: null,
            altitudeAccuracy: null,
            heading: null,
            speed: null,
          },
          timestamp: this.mockPosition.timestamp || Date.now(),
        };
        success(mockGeolocationPosition);
      }
    }, this.delay);
  }

  watchPosition(): number {
    // Mock implementation - return a fake watch ID
    return 1;
  }

  clearWatch(): void {
    // Mock implementation
  }
}

/**
 * Setup mock geolocation for testing
 */
export function setupMockGeolocation(): MockGeolocation {
  const mockGeo = MockGeolocation.getInstance();
  
  // Replace the global navigator.geolocation
  Object.defineProperty(global.navigator, 'geolocation', {
    value: mockGeo,
    writable: true,
  });

  return mockGeo;
}

/**
 * Test helper to validate office check-in scenarios
 */
export function createOfficeCheckInTest(scenario: typeof TEST_SCENARIOS[keyof typeof TEST_SCENARIOS]) {
  return {
    ...scenario,
    setup: () => {
      const mockGeo = setupMockGeolocation();
      mockGeo.setMockPosition(scenario.location);
      return mockGeo;
    },
    cleanup: () => {
      // Reset mock
      const mockGeo = MockGeolocation.getInstance();
      mockGeo.setMockPosition(MOCK_LOCATIONS.INSIDE_OFFICE);
    },
  };
}
