import React, { useState, useMemo } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, BookOpen, Search, Filter, Package, AlertTriangle, Edit, Trash2, Eye } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useBookOperations, BookWithInventory } from '@/hooks/useBookOperations';
import { formatBookLanguage, formatBookCondition, BookLanguage, BookCondition } from '@/types/book';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import AddBookModal from './books/AddBookModal';
import EditBookModal from './books/EditBookModal';
import InventoryUpdateModal from './books/InventoryUpdateModal';
import LowStockAlert from './books/LowStockAlert';

import { useAuth } from '@/hooks/useAuth';

interface BookFilters {
  search: string;
  language?: BookLanguage;
  condition?: BookCondition;
  low_stock?: boolean;
}

const BookManagement = () => {
  const { profile } = useAuth();
  const { books, lowStockBooks, isLoadingBooks, canManageBooks, deleteBook, isDeletingBook, booksError, refreshBooks } = useBookOperations();
  const { toast } = useToast();

  // State management - ALL HOOKS MUST BE CALLED BEFORE ANY CONDITIONAL RETURNS
  const [selectedBook, setSelectedBook] = useState<BookWithInventory | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showInventoryModal, setShowInventoryModal] = useState(false);
  const [filters, setFilters] = useState<BookFilters>({
    search: '',
    low_stock: false,
  });

  // Filter books based on current filters - MUST BE CALLED BEFORE ANY CONDITIONAL RETURNS
  const filteredBooks = useMemo(() => {
    if (!books) return [];

    return books.filter(book => {
      // Search filter
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        const matchesSearch =
          book.title.toLowerCase().includes(searchTerm) ||
          book.author.toLowerCase().includes(searchTerm) ||
          book.isbn?.toLowerCase().includes(searchTerm) ||
          book.publisher?.toLowerCase().includes(searchTerm);
        if (!matchesSearch) return false;
      }



      // Language filter
      if (filters.language && book.language !== filters.language) return false;

      // Condition filter
      if (filters.condition && book.condition !== filters.condition) return false;

      // Low stock filter
      if (filters.low_stock && book.available_quantity > book.minimum_threshold) return false;

      return true;
    });
  }, [books, filters]);

  // Event handlers - MUST BE DEFINED BEFORE ANY CONDITIONAL RETURNS
  const handleViewDetails = (book: BookWithInventory) => {
    setSelectedBook(book);
    setShowDetails(true);
  };

  const handleEditBook = (book: BookWithInventory) => {
    setSelectedBook(book);
    setShowEditModal(true);
  };

  const handleUpdateInventory = (book: BookWithInventory) => {
    setSelectedBook(book);
    setShowInventoryModal(true);
  };

  const handleDeleteBook = async (book: BookWithInventory) => {
    if (confirm(`Are you sure you want to delete "${book.title}"? This action cannot be undone.`)) {
      try {
        await deleteBook(book.id);
        toast({
          title: "Success",
          description: "Book deleted successfully",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete book",
          variant: "destructive",
        });
      }
    }
  };

  const handleClearFilters = () => {
    setFilters({
      search: '',
      low_stock: false,
    });
  };

  const getStockStatusBadge = (book: BookWithInventory) => {
    if (book.available_quantity === 0) {
      return <Badge variant="destructive">Out of Stock</Badge>;
    } else if (book.available_quantity <= book.minimum_threshold) {
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Low Stock</Badge>;
    } else {
      return <Badge variant="default" className="bg-green-100 text-green-800">In Stock</Badge>;
    }
  };

  // NOW WE CAN SAFELY DO CONDITIONAL RETURNS AFTER ALL HOOKS ARE CALLED

  // Show loading state while authentication is being resolved
  if (canManageBooks === null) {
    return (
      <>
        <PageHeader
          title="Book Management"
          description="Manage books and inventory"
          icon={BookOpen}
        />
        <ContentCard>
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Verifying access permissions...</p>
          </div>
        </ContentCard>
      </>
    );
  }

  // Check permissions
  if (!canManageBooks) {
    return (
      <>
        <PageHeader
          title="Book Management"
          description="Manage books and inventory"
          icon={BookOpen}
        />
        <ContentCard>
          <div className="text-center py-12">
            <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
            <p className="text-gray-600">Only administrators, program officers, and accountants can access book management.</p>
          </div>
        </ContentCard>
      </>
    );
  }

  // Handle errors from the book operations hook
  if (booksError) {
    console.error('BookManagement: Error from useBookOperations:', booksError);
    return (
      <>
        <PageHeader
          title="Book Management"
          description="Manage books and inventory"
          icon={BookOpen}
        />
        <ContentCard>
          <div className="text-center py-12">
            <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Books</h2>
            <p className="text-gray-600 mb-4">
              There was an error loading the book inventory. Please try refreshing the page.
            </p>
            <p className="text-sm text-red-600">
              Error: {booksError.message}
            </p>
            <div className="flex justify-center space-x-2 mt-4">
              <Button
                onClick={refreshBooks}
                className="bg-ilead-green hover:bg-ilead-dark-green text-white"
              >
                Retry
              </Button>
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
              >
                Refresh Page
              </Button>
            </div>
          </div>
        </ContentCard>
      </>
    );
  }

  if (isLoadingBooks) {
    return (
      <>
        <PageHeader
          title="Book Management"
          description="Manage books and inventory"
          icon={BookOpen}
        />
        <ContentCard>
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-ilead-green"></div>
            <span className="ml-2 text-gray-600">Loading books...</span>
          </div>
        </ContentCard>
      </>
    );
  }

  return (
    <>
      <PageHeader
        title="Book Management"
        description={`Manage books and inventory (${filteredBooks.length} books)${lowStockBooks.length > 0 ? ` • ${lowStockBooks.length} book(s) with low stock` : ''}`}
        icon={BookOpen}
        actions={[
          {
            label: "Add Book",
            onClick: () => setShowAddModal(true),
            icon: Plus,
            variant: "default"
          }
        ]}
      />

      {/* Low Stock Warning */}
      {lowStockBooks.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center text-yellow-800">
            <AlertTriangle className="h-5 w-5 mr-2" />
            <span className="font-medium">
              {lowStockBooks.length} book(s) have low stock and need attention
            </span>
          </div>
        </div>
      )}

      {/* Filters */}
      <ContentCard
        title="Filters"
        icon={Filter}
      >
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by title, author, ISBN..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-10"
                />
              </div>
            </div>


              <div className="space-y-2">
                <label className="text-sm font-medium">Show Low Stock Only</label>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={filters.low_stock}
                    onChange={(e) => setFilters(prev => ({ ...prev, low_stock: e.target.checked }))}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Low stock items only</span>
                </div>
              </div>
          </div>
          <div className="flex justify-end">
            <Button variant="outline" onClick={handleClearFilters}>
              Clear Filters
            </Button>
          </div>
        </div>
      </ContentCard>



      {/* Low Stock Alert */}
      {lowStockBooks.length > 0 && (
        <ContentCard noPadding>
          <LowStockAlert
            lowStockBooks={lowStockBooks}
            onViewBook={handleViewDetails}
            onEditBook={handleEditBook}
            onUpdateInventory={handleUpdateInventory}
          />
        </ContentCard>
      )}

      {/* Books Grid */}
      <ContentCard
        title="Books Inventory"
        description={`${filteredBooks.length} books found`}
        noPadding
      >
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredBooks.map((book) => (
            <Card key={book.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-lg line-clamp-2">{book.title}</CardTitle>
                    <CardDescription className="mt-1">by {book.author}</CardDescription>
                  </div>
                  {getStockStatusBadge(book)}
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-2 text-sm">

                  <div>
                    <span className="font-medium">Language:</span>
                    <p className="text-gray-600">{formatBookLanguage(book.language)}</p>
                  </div>
                  <div>
                    <span className="font-medium">Condition:</span>
                    <p className="text-gray-600">{formatBookCondition(book.condition)}</p>
                  </div>
                </div>

                {book.isbn && (
                  <div className="text-sm">
                    <span className="font-medium">ISBN:</span>
                    <p className="text-gray-600">{book.isbn}</p>
                  </div>
                )}

                <div className="grid grid-cols-3 gap-2 text-sm">
                  <div className="text-center p-2 bg-blue-50 rounded">
                    <p className="font-medium text-blue-600">{book.total_quantity}</p>
                    <p className="text-xs text-gray-600">Total</p>
                  </div>
                  <div className="text-center p-2 bg-green-50 rounded">
                    <p className="font-medium text-green-600">{book.available_quantity}</p>
                    <p className="text-xs text-gray-600">Available</p>
                  </div>
                  <div className="text-center p-2 bg-orange-50 rounded">
                    <p className="font-medium text-orange-600">{book.distributed_quantity}</p>
                    <p className="text-xs text-gray-600">Distributed</p>
                  </div>
                </div>

                {book.cost_per_unit && (
                  <div className="text-sm">
                    <span className="font-medium">Cost per unit:</span>
                    <p className="text-gray-600">UGX {book.cost_per_unit.toLocaleString()}</p>
                  </div>
                )}

                <div className="flex justify-between items-center pt-2">
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewDetails(book)}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditBook(book)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleUpdateInventory(book)}
                      className="text-ilead-green hover:text-ilead-dark-green hover:bg-green-50"
                    >
                      <Package className="h-4 w-4 mr-1" />
                      Inventory
                    </Button>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteBook(book)}
                    disabled={isDeletingBook}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
          </div>

          {filteredBooks.length === 0 && (
            <div className="text-center py-12">
              <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">No books found</h2>
              <p className="text-gray-600 mb-4">
                {filters.search || filters.language || filters.condition || filters.low_stock
                  ? 'Try adjusting your filters to see more results.'
                  : 'Get started by adding your first book to the inventory.'}
              </p>
              {(!filters.search && !filters.language && !filters.condition && !filters.low_stock) && (
                <Button
                  onClick={() => setShowAddModal(true)}
                  className="bg-ilead-green hover:bg-ilead-dark-green text-white"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Your First Book
                </Button>
              )}
            </div>
          )}
        </div>
      </ContentCard>

      {/* Book Details Dialog */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          {selectedBook && (
            <div className="space-y-6">
              <div className="flex justify-between items-start">
                <div>
                  <h2 className="text-2xl font-bold">{selectedBook.title}</h2>
                  <p className="text-lg text-gray-600">by {selectedBook.author}</p>
                </div>
                {getStockStatusBadge(selectedBook)}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold mb-2">Book Information</h3>
                  <div className="space-y-2 text-sm">

                    <div><span className="font-medium">Language:</span> {formatBookLanguage(selectedBook.language)}</div>
                    {selectedBook.isbn && <div><span className="font-medium">ISBN:</span> {selectedBook.isbn}</div>}
                    {selectedBook.publication_year && <div><span className="font-medium">Publication Year:</span> {selectedBook.publication_year}</div>}
                    {selectedBook.publisher && <div><span className="font-medium">Publisher:</span> {selectedBook.publisher}</div>}
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Inventory Details</h3>
                  <div className="space-y-2 text-sm">
                    <div><span className="font-medium">Total Quantity:</span> {selectedBook.total_quantity}</div>
                    <div><span className="font-medium">Available:</span> {selectedBook.available_quantity}</div>
                    <div><span className="font-medium">Distributed:</span> {selectedBook.distributed_quantity}</div>
                    <div><span className="font-medium">Damaged:</span> {selectedBook.damaged_quantity}</div>
                    <div><span className="font-medium">Lost:</span> {selectedBook.lost_quantity}</div>
                    <div><span className="font-medium">Condition:</span> {formatBookCondition(selectedBook.condition)}</div>
                    <div><span className="font-medium">Min. Threshold:</span> {selectedBook.minimum_threshold}</div>
                    {selectedBook.storage_location && <div><span className="font-medium">Storage:</span> {selectedBook.storage_location}</div>}
                    {selectedBook.cost_per_unit && <div><span className="font-medium">Cost per Unit:</span> UGX {selectedBook.cost_per_unit.toLocaleString()}</div>}
                  </div>
                </div>
              </div>

              {selectedBook.description && (
                <div>
                  <h3 className="font-semibold mb-2">Description</h3>
                  <p className="text-sm text-gray-600">{selectedBook.description}</p>
                </div>
              )}

              {selectedBook.inventory_notes && (
                <div>
                  <h3 className="font-semibold mb-2">Notes</h3>
                  <p className="text-sm text-gray-600">{selectedBook.inventory_notes}</p>
                </div>
              )}

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowDetails(false)}>
                  Close
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowDetails(false);
                    handleUpdateInventory(selectedBook);
                  }}
                  className="text-ilead-green hover:text-ilead-dark-green hover:bg-green-50"
                >
                  <Package className="h-4 w-4 mr-2" />
                  Update Inventory
                </Button>
                <Button
                  onClick={() => {
                    setShowDetails(false);
                    handleEditBook(selectedBook);
                  }}
                  className="bg-ilead-green hover:bg-ilead-dark-green text-white"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Book
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Add Book Modal */}
      <AddBookModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
      />

      {/* Edit Book Modal */}
      <EditBookModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        book={selectedBook}
      />

      {/* Inventory Update Modal */}
      <InventoryUpdateModal
        isOpen={showInventoryModal}
        onClose={() => setShowInventoryModal(false)}
        book={selectedBook}
        onUpdate={refreshBooks}
      />
    </>
  );
};

export default BookManagement;
