/**
 * Office Locations Hook
 * Manages office location data and provides utilities for office check-ins
 */

import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { OfficeLocation, MAIN_OFFICE } from '@/constants/officeLocations';

export interface OfficeLocationData {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  geofence_radius_meters: number;
  address?: string;
  description?: string;
  is_active: boolean;
}

/**
 * Hook to fetch office locations from the database
 */
export function useOfficeLocations() {
  return useQuery({
    queryKey: ['office-locations'],
    queryFn: async (): Promise<OfficeLocation[]> => {
      const { data, error } = await supabase.rpc('get_office_locations');

      if (error) {
        console.error('Error fetching office locations:', error);
        // Fallback to hardcoded main office if database query fails
        return [MAIN_OFFICE];
      }

      if (!data || data.length === 0) {
        // Fallback to hardcoded main office if no data
        return [MAIN_OFFICE];
      }

      // Transform database data to OfficeLocation format
      return data.map((office: OfficeLocationData) => ({
        id: office.id,
        name: office.name,
        coordinates: {
          latitude: office.latitude,
          longitude: office.longitude,
        },
        geofenceRadius: office.geofence_radius_meters,
        address: office.address,
        description: office.description,
        isActive: office.is_active,
      }));
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    retry: 1,
    // Always provide fallback data
    placeholderData: [MAIN_OFFICE],
  });
}

/**
 * Hook to get a specific office location by ID
 */
export function useOfficeLocation(officeId: string) {
  const { data: offices, ...query } = useOfficeLocations();
  
  const office = offices?.find(o => o.id === officeId);
  
  return {
    ...query,
    data: office,
  };
}

/**
 * Hook to get active office locations only
 */
export function useActiveOfficeLocations() {
  const { data: offices, ...query } = useOfficeLocations();
  
  const activeOffices = offices?.filter(office => office.isActive) || [];
  
  return {
    ...query,
    data: activeOffices,
  };
}

/**
 * Get main office location (fallback-safe)
 */
export function getMainOffice(): OfficeLocation {
  return MAIN_OFFICE;
}

/**
 * Utility to check if office locations are available
 */
export function useOfficeLocationsAvailable() {
  const { data: offices, isLoading, error } = useOfficeLocations();
  
  return {
    available: !isLoading && !error && offices && offices.length > 0,
    count: offices?.length || 0,
    isLoading,
    error,
  };
}
