/**
 * Attendance Filters Component
 * Provides filtering controls for attendance tracking including date range, staff, and check-in type
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Filter, 
  X, 
  Calendar, 
  User, 
  Building, 
  School,
  RotateCcw,
  Search
} from 'lucide-react';
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns';
import {
  AttendanceFilters,
  useStaffForAttendanceFilter,
} from '@/hooks/useAttendanceTracking';

interface AttendanceFiltersProps {
  filters: AttendanceFilters;
  onFiltersChange: (filters: AttendanceFilters) => void;
  className?: string;
}

const AttendanceFiltersComponent: React.FC<AttendanceFiltersProps> = ({
  filters,
  onFiltersChange,
  className = '',
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { data: staffOptions = [], isLoading: staffLoading } = useStaffForAttendanceFilter();

  const handleFilterChange = (key: keyof AttendanceFilters, value: string | undefined) => {
    onFiltersChange({
      ...filters,
      [key]: value || undefined,
    });
  };

  const clearFilters = () => {
    onFiltersChange({});
    setIsExpanded(false);
  };

  const setDateRange = (range: 'today' | 'week' | 'month' | 'custom') => {
    const today = new Date();
    let startDate: string | undefined;
    let endDate: string | undefined;

    switch (range) {
      case 'today':
        startDate = format(today, 'yyyy-MM-dd');
        endDate = format(today, 'yyyy-MM-dd');
        break;
      case 'week':
        startDate = format(subDays(today, 7), 'yyyy-MM-dd');
        endDate = format(today, 'yyyy-MM-dd');
        break;
      case 'month':
        startDate = format(startOfMonth(today), 'yyyy-MM-dd');
        endDate = format(endOfMonth(today), 'yyyy-MM-dd');
        break;
      case 'custom':
        // Keep existing dates or clear them
        break;
    }

    onFiltersChange({
      ...filters,
      startDate,
      endDate,
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.startDate || filters.endDate) count++;
    if (filters.staffId) count++;
    if (filters.checkInType) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            <CardTitle>Filters</CardTitle>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount} active
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            {activeFiltersCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-gray-500 hover:text-gray-700"
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                Clear
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </div>
        <CardDescription>
          Filter attendance records by date range, staff member, and check-in type
        </CardDescription>
      </CardHeader>

      {(isExpanded || activeFiltersCount > 0) && (
        <CardContent className="space-y-6">
          {/* Quick Date Range Buttons */}
          <div>
            <Label className="text-sm font-medium mb-2 block">Quick Date Ranges</Label>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setDateRange('today')}
                className="text-xs"
              >
                Today
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setDateRange('week')}
                className="text-xs"
              >
                Last 7 Days
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setDateRange('month')}
                className="text-xs"
              >
                This Month
              </Button>
            </div>
          </div>

          {/* Custom Date Range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="start-date" className="flex items-center gap-2 text-sm font-medium mb-2">
                <Calendar className="h-4 w-4" />
                Start Date
              </Label>
              <Input
                id="start-date"
                type="date"
                value={filters.startDate || ''}
                onChange={(e) => handleFilterChange('startDate', e.target.value)}
                className="w-full"
              />
            </div>
            <div>
              <Label htmlFor="end-date" className="flex items-center gap-2 text-sm font-medium mb-2">
                <Calendar className="h-4 w-4" />
                End Date
              </Label>
              <Input
                id="end-date"
                type="date"
                value={filters.endDate || ''}
                onChange={(e) => handleFilterChange('endDate', e.target.value)}
                className="w-full"
              />
            </div>
          </div>

          {/* Staff Filter */}
          <div>
            <Label className="flex items-center gap-2 text-sm font-medium mb-2">
              <User className="h-4 w-4" />
              Staff Member
            </Label>
            <Select
              value={filters.staffId || 'all'}
              onValueChange={(value) => handleFilterChange('staffId', value === 'all' ? undefined : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All staff members" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All staff members</SelectItem>
                {staffLoading ? (
                  <SelectItem value="loading" disabled>Loading staff...</SelectItem>
                ) : (
                  staffOptions.map((staff) => (
                    <SelectItem key={staff.id} value={staff.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{staff.full_name}</span>
                        <div className="flex items-center gap-2 ml-4">
                          <Badge variant="outline" className="text-xs">
                            {staff.role}
                          </Badge>
                          {staff.total_checkins > 0 && (
                            <Badge variant="secondary" className="text-xs">
                              {staff.total_checkins} check-ins
                            </Badge>
                          )}
                        </div>
                      </div>
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Check-in Type Filter */}
          <div>
            <Label className="flex items-center gap-2 text-sm font-medium mb-2">
              <Building className="h-4 w-4" />
              Check-in Type
            </Label>
            <Select
              value={filters.checkInType || 'all'}
              onValueChange={(value) => handleFilterChange('checkInType', value === 'all' ? undefined : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All check-in types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All check-in types</SelectItem>
                <SelectItem value="school">
                  <div className="flex items-center gap-2">
                    <School className="h-4 w-4" />
                    School Visits
                  </div>
                </SelectItem>
                <SelectItem value="office">
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4" />
                    Office Check-ins
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Active Filters Summary */}
          {activeFiltersCount > 0 && (
            <div className="pt-4 border-t">
              <Label className="text-sm font-medium mb-2 block">Active Filters</Label>
              <div className="flex flex-wrap gap-2">
                {(filters.startDate || filters.endDate) && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {filters.startDate && filters.endDate
                      ? `${format(new Date(filters.startDate), 'MMM dd')} - ${format(new Date(filters.endDate), 'MMM dd')}`
                      : filters.startDate
                      ? `From ${format(new Date(filters.startDate), 'MMM dd')}`
                      : `Until ${format(new Date(filters.endDate!), 'MMM dd')}`
                    }
                    <X
                      className="h-3 w-3 cursor-pointer hover:text-red-500"
                      onClick={() => {
                        onFiltersChange({
                          ...filters,
                          startDate: undefined,
                          endDate: undefined,
                        });
                      }}
                    />
                  </Badge>
                )}
                {filters.staffId && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    {staffOptions.find(s => s.id === filters.staffId)?.full_name || 'Selected Staff'}
                    <X
                      className="h-3 w-3 cursor-pointer hover:text-red-500"
                      onClick={() => handleFilterChange('staffId', undefined)}
                    />
                  </Badge>
                )}
                {filters.checkInType && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    {filters.checkInType === 'school' ? (
                      <School className="h-3 w-3" />
                    ) : (
                      <Building className="h-3 w-3" />
                    )}
                    {filters.checkInType === 'school' ? 'School Visits' : 'Office Check-ins'}
                    <X
                      className="h-3 w-3 cursor-pointer hover:text-red-500"
                      onClick={() => handleFilterChange('checkInType', undefined)}
                    />
                  </Badge>
                )}
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};

export default AttendanceFiltersComponent;
