/**
 * Storage Analytics Dashboard
 * Comprehensive photo storage management and monitoring interface
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  HardDrive, 
  Archive, 
  Zap, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp,
  BarChart3,
  Settings,
  RefreshCw,
  Trash2,
  Compress,
  Download
} from 'lucide-react';
import { usePhotoStorageManager, type TieredStorageStats } from '@/hooks/usePhotoStorageManager';
import { type StorageUsageInfo, type StorageWarning } from '@/utils/photoStorageCleanup';
import { toast } from 'sonner';

// ============================================================================
// STORAGE OVERVIEW COMPONENT
// ============================================================================

const StorageOverview: React.FC<{
  storageUsage: StorageUsageInfo | undefined;
  storageWarning: StorageWarning | undefined;
  formatBytes: (bytes: number) => string;
  formatPercentage: (value: number) => string;
}> = ({ storageUsage, storageWarning, formatBytes, formatPercentage }) => {
  if (!storageUsage) return null;

  const getUtilizationColor = (percentage: number) => {
    if (percentage >= 95) return 'bg-red-500';
    if (percentage >= 90) return 'bg-orange-500';
    if (percentage >= 80) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getWarningIcon = (level: string) => {
    switch (level) {
      case 'critical':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'high':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      case 'medium':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      default:
        return <CheckCircle className="h-5 w-5 text-green-500" />;
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {/* Total Storage Usage */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Storage</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatBytes(storageUsage.totalSizeBytes)}
              </p>
              <p className="text-sm text-gray-500">
                of {formatBytes(storageUsage.maxSizeBytes)}
              </p>
            </div>
            <div className="p-2 rounded-lg bg-blue-50">
              <HardDrive className="h-5 w-5 text-blue-600" />
            </div>
          </div>
          <div className="mt-3">
            <Progress 
              value={storageUsage.utilizationPercentage} 
              className="h-2"
              indicatorClassName={getUtilizationColor(storageUsage.utilizationPercentage)}
            />
            <p className="text-xs text-gray-500 mt-1">
              {formatPercentage(storageUsage.utilizationPercentage)} utilized
            </p>
          </div>
        </CardContent>
      </Card>

      {/* File Count */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Files</p>
              <p className="text-2xl font-bold text-gray-900">
                {storageUsage.fileCount.toLocaleString()}
              </p>
              <p className="text-sm text-gray-500">photos stored</p>
            </div>
            <div className="p-2 rounded-lg bg-purple-50">
              <BarChart3 className="h-5 w-5 text-purple-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Remaining Space */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Remaining</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatBytes(storageUsage.remainingBytes)}
              </p>
              <p className="text-sm text-gray-500">available space</p>
            </div>
            <div className="p-2 rounded-lg bg-green-50">
              <TrendingUp className="h-5 w-5 text-green-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Storage Health */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Health Status</p>
              <p className="text-lg font-bold text-gray-900 capitalize">
                {storageWarning?.warningLevel || 'Normal'}
              </p>
              <p className="text-sm text-gray-500">storage health</p>
            </div>
            <div className="p-2 rounded-lg bg-gray-50">
              {getWarningIcon(storageWarning?.warningLevel)}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// ============================================================================
// TIERED STORAGE BREAKDOWN COMPONENT
// ============================================================================

const TieredStorageBreakdown: React.FC<{
  tieredStats: TieredStorageStats | null;
  formatBytes: (bytes: number) => string;
}> = ({ tieredStats, formatBytes }) => {
  if (!tieredStats) return null;

  const tiers = [
    {
      name: 'Recent Photos',
      key: 'recent',
      description: 'Full resolution (last 30 days)',
      icon: <HardDrive className="h-5 w-5 text-blue-500" />,
      color: 'bg-blue-500',
      data: tieredStats.recent,
    },
    {
      name: 'Compressed Photos',
      key: 'compressed',
      description: 'Compressed (30+ days old)',
      icon: <Compress className="h-5 w-5 text-orange-500" />,
      color: 'bg-orange-500',
      data: tieredStats.compressed,
    },
    {
      name: 'Archived Photos',
      key: 'archived',
      description: 'Long-term storage (6+ months)',
      icon: <Archive className="h-5 w-5 text-green-500" />,
      color: 'bg-green-500',
      data: tieredStats.archived,
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Storage Tier Breakdown</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {tiers.map((tier) => (
            <div key={tier.key} className="flex items-center justify-between p-3 rounded-lg border">
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-lg bg-gray-50">
                  {tier.icon}
                </div>
                <div>
                  <p className="font-medium text-gray-900">{tier.name}</p>
                  <p className="text-sm text-gray-500">{tier.description}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium text-gray-900">
                  {formatBytes(tier.data.sizeBytes)}
                </p>
                <p className="text-sm text-gray-500">
                  {tier.data.count} files ({tier.data.percentage.toFixed(1)}%)
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// ============================================================================
// CLEANUP ACTIONS COMPONENT
// ============================================================================

const CleanupActions: React.FC<{
  onRoutineCleanup: () => void;
  onAggressiveCleanup: () => void;
  onEmergencyCleanup: () => void;
  isPerformingCleanup: boolean;
  cleanupStats: {
    totalPhotos: number;
    recentPhotos: number;
    compressedPhotos: number;
    archivedPhotos: number;
    eligibleForCompression: number;
    eligibleForArchiving: number;
  } | undefined;
}> = ({
  onRoutineCleanup,
  onAggressiveCleanup,
  onEmergencyCleanup,
  isPerformingCleanup,
  cleanupStats
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Storage Cleanup Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Cleanup Statistics */}
          {cleanupStats && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="text-center">
                <p className="text-lg font-bold text-gray-900">{cleanupStats.eligibleForCompression}</p>
                <p className="text-sm text-gray-600">Ready for compression</p>
              </div>
              <div className="text-center">
                <p className="text-lg font-bold text-gray-900">{cleanupStats.eligibleForArchiving}</p>
                <p className="text-sm text-gray-600">Ready for archiving</p>
              </div>
              <div className="text-center">
                <p className="text-lg font-bold text-gray-900">{cleanupStats.compressedPhotos}</p>
                <p className="text-sm text-gray-600">Already compressed</p>
              </div>
              <div className="text-center">
                <p className="text-lg font-bold text-gray-900">{cleanupStats.archivedPhotos}</p>
                <p className="text-sm text-gray-600">Already archived</p>
              </div>
            </div>
          )}

          {/* Cleanup Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              onClick={onRoutineCleanup}
              disabled={isPerformingCleanup}
              variant="outline"
              className="flex items-center space-x-2"
            >
              <Compress className="h-4 w-4" />
              <span>Routine Cleanup</span>
            </Button>

            <Button
              onClick={onAggressiveCleanup}
              disabled={isPerformingCleanup}
              variant="outline"
              className="flex items-center space-x-2"
            >
              <Archive className="h-4 w-4" />
              <span>Aggressive Cleanup</span>
            </Button>

            <Button
              onClick={onEmergencyCleanup}
              disabled={isPerformingCleanup}
              variant="destructive"
              className="flex items-center space-x-2"
            >
              <AlertTriangle className="h-4 w-4" />
              <span>Emergency Cleanup</span>
            </Button>
          </div>

          <div className="text-sm text-gray-600 space-y-1">
            <p><strong>Routine:</strong> Compress photos older than 30 days</p>
            <p><strong>Aggressive:</strong> Compress + archive photos older than 6 months</p>
            <p><strong>Emergency:</strong> Immediate space recovery with potential data archiving</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// ============================================================================
// MAIN STORAGE ANALYTICS DASHBOARD COMPONENT
// ============================================================================

const StorageAnalyticsDashboard: React.FC = () => {
  const [selectedBucket, setSelectedBucket] = useState('field-report-photos');
  
  const {
    storageUsage,
    storageWarning,
    cleanupStats,
    tieredStorageStats,
    isLoading,
    error,
    refetchUsage,
    refetchStats,
    performRoutineCleanup,
    performAggressiveCleanup,
    performEmergencyCleanup,
    isPerformingCleanup,
    formatBytes,
    formatPercentage,
  } = usePhotoStorageManager(selectedBucket);

  const handleRefresh = () => {
    refetchUsage();
    refetchStats();
    toast.success('Storage data refreshed');
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Storage Analytics</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error Loading Storage Data</AlertTitle>
          <AlertDescription>
            Failed to load storage analytics: {error.message}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Storage Analytics</h1>
          <p className="text-gray-600">Monitor and manage photo storage lifecycle</p>
        </div>
        <div className="flex space-x-2">
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="sm"
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Storage Warning Alert */}
      {storageWarning && storageWarning.shouldCleanup && (
        <Alert variant={storageWarning.warningLevel === 'critical' ? 'destructive' : 'default'}>
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Storage {storageWarning.warningLevel.toUpperCase()}</AlertTitle>
          <AlertDescription>
            {storageWarning.message}
            {storageWarning.recommendedActions.length > 0 && (
              <div className="mt-2">
                <p className="font-medium">Recommended actions:</p>
                <ul className="list-disc list-inside">
                  {storageWarning.recommendedActions.map((action, index) => (
                    <li key={index} className="text-sm">{action.replace(/_/g, ' ')}</li>
                  ))}
                </ul>
              </div>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Storage Overview */}
      <StorageOverview
        storageUsage={storageUsage}
        storageWarning={storageWarning}
        formatBytes={formatBytes}
        formatPercentage={formatPercentage}
      />

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="cleanup">Cleanup</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <TieredStorageBreakdown
              tieredStats={tieredStorageStats}
              formatBytes={formatBytes}
            />
            
            {/* Additional analytics could go here */}
            <Card>
              <CardHeader>
                <CardTitle>Storage Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4" />
                  <p>Storage trend charts coming soon</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="cleanup" className="space-y-4">
          <CleanupActions
            onRoutineCleanup={performRoutineCleanup}
            onAggressiveCleanup={performAggressiveCleanup}
            onEmergencyCleanup={performEmergencyCleanup}
            isPerformingCleanup={isPerformingCleanup}
            cleanupStats={cleanupStats}
          />
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Storage Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <Settings className="h-12 w-12 mx-auto mb-4" />
                <p>Storage settings configuration coming soon</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default StorageAnalyticsDashboard;
