# Social Media Manager Field Report Structure

## Overview
This document outlines the specialized field report structure for Social Media Managers who document field activities for communications and social media purposes.

## Report Structure

### 📋 **Basic Information**
- **Report Type**: `social_media_documentation`
- **Activity Date**: Date of field activity
- **Location**: School/venue name and district
- **Duration**: Start and end time of documentation
- **Primary Activity**: Type of activity being documented

### 📱 **Content Documentation**

#### **1. Media Assets**
```typescript
interface MediaAsset {
  type: 'photo' | 'video' | 'audio';
  title: string;
  description: string;
  google_drive_link: string;
  file_format: string; // e.g., 'JPG', 'MP4', 'MP3'
  estimated_size: string; // e.g., '2.5MB', '15MB'
  capture_timestamp: string;
  location_context: string; // Where/when this was captured
  usage_rights: 'full' | 'limited' | 'permission_required';
  featured: boolean; // Mark as featured content
}
```

#### **2. Story Elements**
```typescript
interface StoryElement {
  story_type: 'success_story' | 'challenge_overcome' | 'student_spotlight' | 'teacher_feature' | 'community_impact';
  headline: string;
  narrative: string; // 2-3 paragraph story
  key_quotes: string[]; // Direct quotes from participants
  participants: {
    name: string;
    role: 'student' | 'teacher' | 'parent' | 'community_member';
    consent_obtained: boolean;
    quote?: string;
  }[];
  social_media_angle: string; // How this story can be used for social media
}
```

#### **3. Activity Documentation**
```typescript
interface ActivityDocumentation {
  activity_name: string;
  activity_type: 'leadership_training' | 'school_visit' | 'community_engagement' | 'assessment' | 'event';
  participants_count: {
    total: number;
    male: number;
    female: number;
    age_groups: {
      '10-14': number;
      '15-18': number;
      '19+': number;
    };
  };
  key_moments: string[]; // Significant moments worth highlighting
  learning_outcomes: string[]; // What participants learned/achieved
  visual_highlights: string[]; // Descriptions of best visual moments captured
}
```

### 🎯 **Social Media Planning**

#### **4. Content Strategy**
```typescript
interface ContentStrategy {
  primary_platforms: ('facebook' | 'instagram' | 'twitter' | 'linkedin' | 'youtube')[];
  content_themes: string[]; // e.g., 'youth empowerment', 'education impact'
  hashtag_suggestions: string[];
  posting_schedule: {
    immediate: string[]; // Content for immediate posting
    weekly: string[]; // Content for this week
    monthly: string[]; // Content for monthly campaigns
  };
  cross_promotion_opportunities: string[];
}
```

#### **5. Impact Messaging**
```typescript
interface ImpactMessaging {
  quantitative_impact: {
    students_reached: number;
    sessions_conducted: number;
    skills_developed: string[];
    books_distributed?: number;
  };
  qualitative_impact: {
    behavior_changes: string[];
    confidence_improvements: string[];
    leadership_demonstrations: string[];
    community_feedback: string[];
  };
  success_metrics: string[];
  call_to_action: string; // What action we want audience to take
}
```

### 📊 **Technical Specifications**

#### **6. Media Management**
```typescript
interface MediaManagement {
  google_drive_folder: {
    main_folder_link: string;
    photos_subfolder: string;
    videos_subfolder: string;
    raw_footage_subfolder: string;
  };
  file_organization: {
    naming_convention: string; // e.g., "YYYYMMDD_SchoolName_ActivityType_001"
    quality_levels: {
      high_res: string; // For print/professional use
      web_optimized: string; // For website use
      social_media: string; // For social platforms
    };
  };
  backup_locations: string[]; // Additional backup drive links
}
```

#### **7. Compliance & Permissions**
```typescript
interface CompliancePermissions {
  photo_consent_forms: {
    students_consent: boolean;
    parents_consent: boolean;
    teachers_consent: boolean;
    consent_form_links: string[]; // Links to signed consent forms
  };
  usage_permissions: {
    internal_use: boolean;
    social_media_use: boolean;
    website_use: boolean;
    print_materials: boolean;
    donor_reports: boolean;
  };
  privacy_considerations: string[];
  data_retention_period: string;
}
```

## Database Schema Extension

### New Table: `social_media_reports`
```sql
CREATE TABLE social_media_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    field_report_id UUID REFERENCES field_reports(id),
    staff_id UUID REFERENCES profiles(id) NOT NULL,
    school_id UUID REFERENCES schools(id),
    
    -- Basic Information
    activity_date DATE NOT NULL,
    activity_duration INTERVAL,
    primary_activity VARCHAR(100),
    
    -- Media Assets (JSONB array)
    media_assets JSONB DEFAULT '[]',
    
    -- Story Elements (JSONB array)
    story_elements JSONB DEFAULT '[]',
    
    -- Activity Documentation
    activity_documentation JSONB DEFAULT '{}',
    
    -- Social Media Strategy
    content_strategy JSONB DEFAULT '{}',
    
    -- Impact Messaging
    impact_messaging JSONB DEFAULT '{}',
    
    -- Media Management
    media_management JSONB DEFAULT '{}',
    
    -- Compliance
    compliance_permissions JSONB DEFAULT '{}',
    
    -- Metadata
    status VARCHAR(20) DEFAULT 'draft',
    submitted_at TIMESTAMP WITH TIME ZONE,
    reviewed_by UUID REFERENCES profiles(id),
    review_comments TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Form Implementation Strategy

### 1. **Multi-Step Form**
- Step 1: Basic Information & Activity Details
- Step 2: Media Assets & Google Drive Links
- Step 3: Story Elements & Narratives
- Step 4: Social Media Strategy
- Step 5: Impact Messaging & Review

### 2. **Google Drive Integration**
- Link validation for Google Drive URLs
- Automatic folder structure suggestions
- Permission checking for shared links
- File type and size estimation

### 3. **Content Templates**
- Pre-defined story templates for common activities
- Hashtag libraries for different themes
- Impact messaging templates
- Social media post templates

## Implementation Files Created

### Database Migration
- `supabase/migrations/033_create_social_media_reports.sql`
- Creates `social_media_reports` table with JSONB fields
- Includes RLS policies for social media managers
- Adds submission function and activity logging

### React Components (To be created)
- `SocialMediaReportForm.tsx` - Main form component
- `MediaAssetManager.tsx` - Google Drive link management
- `StoryElementEditor.tsx` - Story creation interface
- `ContentStrategyPlanner.tsx` - Social media strategy planning
- `ImpactMessagingEditor.tsx` - Impact messaging interface

## Usage Workflow

1. **Field Documentation**: Social media manager accompanies field staff
2. **Content Capture**: Records media assets in Google Drive with organized folder structure
3. **Report Creation**: Fills out specialized social media report form
4. **Story Development**: Crafts narratives and identifies key messaging
5. **Strategy Planning**: Plans content distribution across platforms
6. **Review & Approval**: Submits for review by program officers
7. **Content Production**: Uses approved content for social media campaigns

This structure ensures comprehensive documentation while maintaining the flexibility needed for effective social media content creation and campaign planning.
