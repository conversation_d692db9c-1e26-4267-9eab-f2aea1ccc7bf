import { Database } from '@/integrations/supabase/types';

type UserRole = Database['public']['Enums']['user_role'];

interface StaffMember {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  division_id: string | null;
  division_name: string | null;
  phone: string | null;
  country: string;
  is_active: boolean;
  requires_password_change: boolean;
  last_password_change: string | null;
  invitation_accepted_at: string | null;
  created_at: string;
}

interface UserImportData {
  email: string;
  name: string;
  role: UserRole;
  division_id?: string;
  phone?: string;
}

interface ValidationError {
  row: number;
  field: string;
  message: string;
}

export class CSVService {
  /**
   * Generate CSV template for user import
   */
  static generateImportTemplate(): string {
    const headers = ['email', 'name', 'role', 'division_id', 'phone'];
    const sampleData = [
      '<EMAIL>,<PERSON>,field_staff,,+************',
      '<EMAIL>,<PERSON>,program_officer,division-uuid-here,+************',
      '<EMAIL>,Admin User,admin,,+************'
    ];

    return [headers.join(','), ...sampleData].join('\n');
  }

  /**
   * Download CSV template file
   */
  static downloadImportTemplate(): void {
    const csvContent = this.generateImportTemplate();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = window.URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'staff-import-template.csv';
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    window.URL.revokeObjectURL(url);
  }

  /**
   * Parse CSV content into user data
   */
  static parseCSV(csvContent: string): { users: UserImportData[]; errors: ValidationError[] } {
    const lines = csvContent.trim().split('\n');
    if (lines.length < 2) {
      return { users: [], errors: [{ row: 1, field: 'file', message: 'CSV file must contain headers and at least one data row' }] };
    }

    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
    const requiredHeaders = ['email', 'name', 'role'];
    const optionalHeaders = ['division_id', 'phone'];
    const allHeaders = [...requiredHeaders, ...optionalHeaders];

    // Validate headers
    const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
    if (missingHeaders.length > 0) {
      return {
        users: [],
        errors: [{
          row: 1,
          field: 'headers',
          message: `Missing required headers: ${missingHeaders.join(', ')}`
        }]
      };
    }

    const users: UserImportData[] = [];
    const errors: ValidationError[] = [];

    // Parse data rows
    for (let i = 1; i < lines.length; i++) {
      const values = this.parseCSVLine(lines[i]);
      const user: Partial<UserImportData> = {};

      // Map values to user object
      headers.forEach((header, index) => {
        if (allHeaders.includes(header) && values[index] !== undefined) {
          const value = values[index].trim();
          if (value) {
            (user as Record<string, string>)[header] = value;
          }
        }
      });

      // Validate user data
      const userErrors = this.validateUserData(user, i);
      errors.push(...userErrors);

      // Add user if basic required fields are present
      if (user.email && user.name && user.role) {
        users.push({
          email: user.email,
          name: user.name,
          role: user.role as UserRole,
          division_id: user.division_id,
          phone: user.phone
        });
      }
    }

    return { users, errors };
  }

  /**
   * Parse a single CSV line handling quoted values
   */
  private static parseCSVLine(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current);
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current);
    return result;
  }

  /**
   * Validate user data
   */
  private static validateUserData(user: Partial<UserImportData>, rowIndex: number): ValidationError[] {
    const errors: ValidationError[] = [];
    const row = rowIndex + 1;

    // Email validation
    if (!user.email) {
      errors.push({ row, field: 'email', message: 'Email is required' });
    } else if (!this.isValidEmail(user.email)) {
      errors.push({ row, field: 'email', message: 'Invalid email format' });
    }

    // Name validation
    if (!user.name) {
      errors.push({ row, field: 'name', message: 'Name is required' });
    } else if (user.name.length < 2) {
      errors.push({ row, field: 'name', message: 'Name must be at least 2 characters long' });
    }

    // Role validation
    if (!user.role) {
      errors.push({ row, field: 'role', message: 'Role is required' });
    } else if (!['admin', 'program_officer', 'field_staff'].includes(user.role)) {
      errors.push({ row, field: 'role', message: 'Role must be one of: admin, program_officer, field_staff' });
    }

    // Phone validation (if provided)
    if (user.phone && !this.isValidPhone(user.phone)) {
      errors.push({ row, field: 'phone', message: 'Invalid phone number format' });
    }

    return errors;
  }

  /**
   * Validate email format
   */
  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone number format
   */
  private static isValidPhone(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-()]+$/;
    return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
  }

  /**
   * Export staff data to CSV
   */
  static exportStaffToCSV(staffMembers: StaffMember[], filename?: string): void {
    const headers = [
      'Name',
      'Email',
      'Role',
      'Division',
      'Phone',
      'Country',
      'Status',
      'Requires Password Change',
      'Last Password Change',
      'Invitation Accepted',
      'Created At'
    ];

    const csvData = staffMembers.map(member => [
      this.escapeCsvValue(member.name),
      this.escapeCsvValue(member.email),
      this.escapeCsvValue(member.role),
      this.escapeCsvValue(member.division_name || ''),
      this.escapeCsvValue(member.phone || ''),
      this.escapeCsvValue(member.country),
      this.escapeCsvValue(member.is_active ? 'Active' : 'Inactive'),
      this.escapeCsvValue(member.requires_password_change ? 'Yes' : 'No'),
      this.escapeCsvValue(member.last_password_change ? new Date(member.last_password_change).toLocaleDateString() : ''),
      this.escapeCsvValue(member.invitation_accepted_at ? new Date(member.invitation_accepted_at).toLocaleDateString() : ''),
      this.escapeCsvValue(new Date(member.created_at).toLocaleDateString())
    ]);

    this.exportToCSV(headers, csvData, filename || `staff-export-${new Date().toISOString().split('T')[0]}.csv`);
  }

  /**
   * Generic CSV export given headers and rows
   */
  static exportToCSV(headers: string[], rows: Array<Array<string | number>>, filename: string): void {
    const safeRows = rows.map(row => row.map(v => this.escapeCsvValue(String(v ?? ''))));
    const csvContent = [headers.join(','), ...safeRows.map(row => row.join(','))].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = filename.endsWith('.csv') ? filename : `${filename}.csv`;
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    window.URL.revokeObjectURL(url);
  }

  /**
   * Export an array of records (objects) to CSV. Headers are derived from all keys.
   */
  static exportRecordsToCSV(records: Array<Record<string, unknown>>, filename: string): void {
    if (!records || records.length === 0) {
      this.exportToCSV(['Message'], [['No data']], filename);
      return;
    }

    // Derive headers from union of keys
    const headersSet = new Set<string>();
    records.forEach(rec => Object.keys(rec).forEach(k => headersSet.add(k)));
    const headers = Array.from(headersSet);

    const rows = records.map(rec => headers.map(h => {
      const val = (rec as Record<string, unknown>)[h];
      if (val == null) return '';
      if (Array.isArray(val)) return JSON.stringify(val);
      if (typeof val === 'object') return JSON.stringify(val);
      return String(val);
    }));

    this.exportToCSV(headers, rows, filename);
  }

  /**
   * Escape CSV values to handle commas, quotes, and newlines
   */
  private static escapeCsvValue(value: string): string {
    if (!value) return '';
    
    // If value contains comma, quote, or newline, wrap in quotes and escape internal quotes
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    
    return value;
  }

  /**
   * Generate error report CSV
   */
  static exportErrorReport(errors: ValidationError[], filename?: string): void {
    const headers = ['Row', 'Field', 'Error Message'];
    
    const csvData = errors.map(error => [
      error.row.toString(),
      this.escapeCsvValue(error.field),
      this.escapeCsvValue(error.message)
    ]);

    const csvContent = [
      headers.join(','),
      ...csvData.map(row => row.join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = window.URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || `import-errors-${new Date().toISOString().split('T')[0]}.csv`;
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    window.URL.revokeObjectURL(url);
  }

  /**
   * Validate CSV file before processing
   */
  static validateCSVFile(file: File): { valid: boolean; error?: string } {
    // Check file type
    if (!file.name.toLowerCase().endsWith('.csv') && file.type !== 'text/csv') {
      return { valid: false, error: 'Please select a CSV file' };
    }

    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return { valid: false, error: 'File size must be less than 5MB' };
    }

    return { valid: true };
  }
}
