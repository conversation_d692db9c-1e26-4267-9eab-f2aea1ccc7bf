/**
 * Photo Storage Manager Hook
 * Integrates tiered storage strategy with upload queue and cleanup services
 */

import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { photoUploadQueue } from '@/utils/photoUploadQueueManager';
import { photoStorageCleanup, type StorageUsageInfo, type StorageWarning } from '@/utils/photoStorageCleanup';
import { toast } from 'sonner';

// ============================================================================
// TYPES
// ============================================================================

export interface PhotoStorageConfig {
  maxStorageBytes: number;
  warningThreshold: number;
  compressionAge: number; // days
  archiveAge: number; // days
  autoCleanup: boolean;
}

export interface TieredStorageStats {
  recent: {
    count: number;
    sizeBytes: number;
    sizeMB: number;
    percentage: number;
  };
  compressed: {
    count: number;
    sizeBytes: number;
    sizeMB: number;
    percentage: number;
    compressionRatio: number;
  };
  archived: {
    count: number;
    sizeBytes: number;
    sizeMB: number;
    percentage: number;
  };
}

// ============================================================================
// PHOTO STORAGE MANAGER HOOK
// ============================================================================

export const usePhotoStorageManager = (bucketName: string = 'field-report-photos') => {
  const queryClient = useQueryClient();
  const [config, setConfig] = useState<PhotoStorageConfig>({
    maxStorageBytes: 500 * 1024 * 1024, // 500MB
    warningThreshold: 80, // 80%
    compressionAge: 30, // 30 days
    archiveAge: 180, // 6 months
    autoCleanup: true,
  });

  // ============================================================================
  // STORAGE USAGE QUERY
  // ============================================================================

  const {
    data: storageUsage,
    isLoading: isLoadingUsage,
    error: usageError,
    refetch: refetchUsage,
  } = useQuery({
    queryKey: ['storage-usage', bucketName],
    queryFn: async (): Promise<StorageUsageInfo> => {
      const { data, error } = await supabase.rpc('get_storage_usage', {
        p_bucket_name: bucketName,
      });

      if (error) {
        throw new Error(`Failed to get storage usage: ${error.message}`);
      }

      return data as StorageUsageInfo;
    },
    refetchInterval: 30000, // Refresh every 30 seconds
    staleTime: 10000, // Consider stale after 10 seconds
  });

  // ============================================================================
  // STORAGE WARNING QUERY
  // ============================================================================

  const {
    data: storageWarning,
    isLoading: isLoadingWarning,
    error: warningError,
  } = useQuery({
    queryKey: ['storage-warning', bucketName, config.warningThreshold],
    queryFn: async (): Promise<StorageWarning> => {
      const { data, error } = await supabase.rpc('check_storage_limit_warning', {
        p_bucket_name: bucketName,
        p_warning_threshold: config.warningThreshold,
      });

      if (error) {
        throw new Error(`Failed to check storage warning: ${error.message}`);
      }

      return data as StorageWarning;
    },
    refetchInterval: 60000, // Check warnings every minute
    enabled: !!storageUsage,
  });

  // ============================================================================
  // CLEANUP STATS QUERY
  // ============================================================================

  const {
    data: cleanupStats,
    isLoading: isLoadingStats,
    refetch: refetchStats,
  } = useQuery({
    queryKey: ['cleanup-stats', bucketName],
    queryFn: () => photoStorageCleanup.getCleanupStats(),
    refetchInterval: 300000, // Refresh every 5 minutes
  });

  // ============================================================================
  // MANUAL CLEANUP MUTATIONS
  // ============================================================================

  const routineCleanupMutation = useMutation({
    mutationFn: () => photoStorageCleanup.performRoutineCleanup(),
    onSuccess: (result) => {
      toast.success(`Routine cleanup completed: ${result.photosProcessed} photos processed, ${(result.bytesSaved / 1024 / 1024).toFixed(1)}MB saved`);
      queryClient.invalidateQueries({ queryKey: ['storage-usage'] });
      queryClient.invalidateQueries({ queryKey: ['cleanup-stats'] });
    },
    onError: (error) => {
      toast.error(`Cleanup failed: ${error.message}`);
    },
  });

  const aggressiveCleanupMutation = useMutation({
    mutationFn: () => photoStorageCleanup.performAggressiveCleanup(),
    onSuccess: (result) => {
      toast.success(`Aggressive cleanup completed: ${result.photosProcessed} photos processed, ${(result.bytesSaved / 1024 / 1024).toFixed(1)}MB saved`);
      queryClient.invalidateQueries({ queryKey: ['storage-usage'] });
      queryClient.invalidateQueries({ queryKey: ['cleanup-stats'] });
    },
    onError: (error) => {
      toast.error(`Aggressive cleanup failed: ${error.message}`);
    },
  });

  const emergencyCleanupMutation = useMutation({
    mutationFn: () => photoStorageCleanup.performEmergencyCleanup(),
    onSuccess: (result) => {
      toast.success(`Emergency cleanup completed: ${result.photosProcessed} photos processed, ${(result.bytesSaved / 1024 / 1024).toFixed(1)}MB saved`);
      queryClient.invalidateQueries({ queryKey: ['storage-usage'] });
      queryClient.invalidateQueries({ queryKey: ['cleanup-stats'] });
    },
    onError: (error) => {
      toast.error(`Emergency cleanup failed: ${error.message}`);
    },
  });

  // ============================================================================
  // PHOTO UPLOAD INTEGRATION
  // ============================================================================

  const checkUploadPermission = useCallback(async (fileSize: number): Promise<boolean> => {
    try {
      const usage = await photoStorageCleanup.getStorageUsage();
      const wouldExceedLimit = (usage.totalSizeBytes + fileSize) > config.maxStorageBytes;
      
      if (wouldExceedLimit) {
        const shouldBlock = await photoStorageCleanup.shouldBlockNewUploads();
        
        if (shouldBlock) {
          toast.error('Storage limit reached. Upload blocked until cleanup is performed.');
          return false;
        }
        
        // Trigger automatic cleanup if enabled
        if (config.autoCleanup) {
          toast.info('Storage approaching limit. Triggering automatic cleanup...');
          photoStorageCleanup.checkAndCleanup();
        }
      }
      
      return true;
    } catch (error) {
      console.error('Failed to check upload permission:', error);
      return true; // Allow upload if check fails
    }
  }, [config.maxStorageBytes, config.autoCleanup]);

  const uploadPhotoWithStorageCheck = useCallback(async (
    file: File,
    options: {
      fieldReportId?: string;
      distributionId?: string;
      priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    }
  ): Promise<string | null> => {
    // Check storage permission before upload
    const canUpload = await checkUploadPermission(file.size);
    if (!canUpload) {
      return null;
    }

    // Add to upload queue
    const taskId = photoUploadQueue.addToQueue(file, {
      bucket: bucketName,
      priority: options.priority || 'MEDIUM',
      fieldReportId: options.fieldReportId,
      distributionId: options.distributionId,
      userId: 'current-user-id', // This should come from auth context
    });

    // Register photo metadata
    try {
      const { error } = await supabase.from('photo_metadata').insert({
        file_path: `${bucketName}/${file.name}`, // This will be updated after upload
        bucket_name: bucketName,
        original_filename: file.name,
        file_size_bytes: file.size,
        mime_type: file.type,
        field_report_id: options.fieldReportId,
        distribution_id: options.distributionId,
        storage_tier: 'recent',
      });

      if (error) {
        console.error('Failed to register photo metadata:', error);
      }
    } catch (error) {
      console.error('Failed to register photo metadata:', error);
    }

    return taskId;
  }, [bucketName, checkUploadPermission]);

  // ============================================================================
  // TIERED STORAGE CALCULATIONS
  // ============================================================================

  const tieredStorageStats = useCallback((): TieredStorageStats | null => {
    if (!storageUsage) return null;

    const { tierBreakdown } = storageUsage;
    const totalSize = storageUsage.totalSizeBytes;

    return {
      recent: {
        count: Math.floor(tierBreakdown.recent.sizeBytes / (totalSize / storageUsage.fileCount || 1)),
        sizeBytes: tierBreakdown.recent.sizeBytes,
        sizeMB: tierBreakdown.recent.sizeMB,
        percentage: tierBreakdown.recent.percentage,
      },
      compressed: {
        count: Math.floor(tierBreakdown.compressed.sizeBytes / (totalSize / storageUsage.fileCount || 1)),
        sizeBytes: tierBreakdown.compressed.sizeBytes,
        sizeMB: tierBreakdown.compressed.sizeMB,
        percentage: tierBreakdown.compressed.percentage,
        compressionRatio: 40, // Placeholder - should be calculated from actual data
      },
      archived: {
        count: Math.floor(tierBreakdown.archived.sizeBytes / (totalSize / storageUsage.fileCount || 1)),
        sizeBytes: tierBreakdown.archived.sizeBytes,
        sizeMB: tierBreakdown.archived.sizeMB,
        percentage: tierBreakdown.archived.percentage,
      },
    };
  }, [storageUsage]);

  // ============================================================================
  // STORAGE HEALTH INDICATORS
  // ============================================================================

  const getStorageHealthStatus = useCallback(() => {
    if (!storageWarning) return 'unknown';
    
    switch (storageWarning.warningLevel) {
      case 'critical':
        return 'critical';
      case 'high':
        return 'warning';
      case 'medium':
        return 'caution';
      default:
        return 'healthy';
    }
  }, [storageWarning]);

  const getStorageHealthColor = useCallback(() => {
    const status = getStorageHealthStatus();
    switch (status) {
      case 'critical':
        return 'text-red-600 bg-red-50';
      case 'warning':
        return 'text-orange-600 bg-orange-50';
      case 'caution':
        return 'text-yellow-600 bg-yellow-50';
      case 'healthy':
        return 'text-green-600 bg-green-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  }, [getStorageHealthStatus]);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  // Monitor storage warnings and trigger automatic actions
  useEffect(() => {
    if (storageWarning && config.autoCleanup) {
      if (storageWarning.warningLevel === 'critical') {
        toast.error(storageWarning.message);
        // Auto-trigger emergency cleanup
        emergencyCleanupMutation.mutate();
      } else if (storageWarning.warningLevel === 'high') {
        toast.warning(storageWarning.message);
        // Auto-trigger aggressive cleanup
        aggressiveCleanupMutation.mutate();
      } else if (storageWarning.warningLevel === 'medium') {
        toast.info(storageWarning.message);
        // Auto-trigger routine cleanup
        routineCleanupMutation.mutate();
      }
    }
  }, [storageWarning, config.autoCleanup, emergencyCleanupMutation, aggressiveCleanupMutation, routineCleanupMutation]);

  // ============================================================================
  // RETURN INTERFACE
  // ============================================================================

  return {
    // Data
    storageUsage,
    storageWarning,
    cleanupStats,
    tieredStorageStats: tieredStorageStats(),
    config,
    
    // Loading states
    isLoading: isLoadingUsage || isLoadingWarning || isLoadingStats,
    isLoadingUsage,
    isLoadingWarning,
    isLoadingStats,
    
    // Errors
    error: usageError || warningError,
    usageError,
    warningError,
    
    // Actions
    uploadPhotoWithStorageCheck,
    checkUploadPermission,
    refetchUsage,
    refetchStats,
    setConfig,
    
    // Manual cleanup actions
    performRoutineCleanup: routineCleanupMutation.mutate,
    performAggressiveCleanup: aggressiveCleanupMutation.mutate,
    performEmergencyCleanup: emergencyCleanupMutation.mutate,
    
    // Cleanup states
    isPerformingCleanup: routineCleanupMutation.isPending || 
                        aggressiveCleanupMutation.isPending || 
                        emergencyCleanupMutation.isPending,
    
    // Health indicators
    storageHealthStatus: getStorageHealthStatus(),
    storageHealthColor: getStorageHealthColor(),
    
    // Utility functions
    formatBytes: (bytes: number) => {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    formatPercentage: (value: number) => `${value.toFixed(1)}%`,
  };
};
