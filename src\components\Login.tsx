
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { LogIn } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface LoginError {
  message?: string;
}

interface LoginProps {
  onLogin: (email: string, password: string) => Promise<{ error: LoginError | null }>;
  variant?: 'page' | 'modal';
}

const Login = ({ onLogin, variant = 'page' }: LoginProps) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { error } = await onLogin(email, password);
      if (error) {
        toast({
          title: "Login Error",
          description: error.message || "Failed to login. Please check your credentials.",
          variant: "destructive",
        });
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
      toast({
        title: "Login Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={variant === 'page' ? "min-h-screen bg-gradient-to-br from-purple-50 to-orange-50 flex items-center justify-center p-4" : ""}>
      <div className={variant === 'page' ? "w-full max-w-md" : "w-full"}>
        {variant === 'page' && (
          <div className="text-center mb-8">
            <img src="/ilead-logo.svg" alt="iLEAD Uganda Logo" className="h-20 mx-auto mb-4" />
            <p className="text-gray-600">Field Management & Monitoring Platform</p>
          </div>
        )}

        <Card className="border-t-4 border-t-ilead-green shadow-sm">
          <CardHeader>
            <div className="flex items-center gap-3 mb-2">
              <CardTitle className="text-2xl">Sign In</CardTitle>
              <div className="flex flex-col w-8 h-6 rounded-sm overflow-hidden shadow-sm border border-gray-200">
                <div className="h-2 bg-black"></div>
                <div className="h-2 bg-yellow-400"></div>
                <div className="h-2 bg-red-600"></div>
              </div>
            </div>
            <CardDescription className="flex items-center gap-2">
              Enter your credentials to access your account
            </CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="...@ileaduganda.org"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button
                type="submit"
                className="w-full bg-ilead-green hover:bg-ilead-dark-green"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                    Signing In...
                  </>
                ) : (
                  <>
                    <LogIn className="h-4 w-4 mr-2" />
                    Sign In
                  </>
                )}
              </Button>
            </CardFooter>
          </form>
        </Card>
        {variant === 'page' && (
          <p className="text-center text-sm text-gray-600 mt-4">
            © {new Date().getFullYear()} iLEAD Uganda. All rights reserved.
          </p>
        )}
      </div>
    </div>
  );
};

export default Login;
