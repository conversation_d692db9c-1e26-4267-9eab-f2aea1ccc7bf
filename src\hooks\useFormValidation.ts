import { useState, useCallback, useMemo } from 'react';
import { z } from 'zod';
import {
  validateData,
  validateField,
  type ValidationResult,
  type FormValidationState,
  type AsyncValidator,
  createAsyncValidator,
} from '@/utils/validation';

interface UseFormValidationOptions<T> {
  schema: z.ZodSchema<T>;
  initialValues?: Partial<T>;
  asyncValidators?: AsyncValidator<T>[];
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
}

interface UseFormValidationReturn<T> {
  values: Partial<T>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isValid: boolean;
  isSubmitting: boolean;
  setValue: (field: keyof T, value: T[keyof T]) => void;
  setValues: (values: Partial<T>) => void;
  setFieldError: (field: keyof T, error: string) => void;
  clearFieldError: (field: keyof T) => void;
  setTouched: (field: keyof T, touched?: boolean) => void;
  validateField: (field: keyof T) => Promise<boolean>;
  validateForm: () => Promise<ValidationResult<T>>;
  handleSubmit: (onSubmit: (data: T) => Promise<void> | void) => (e?: React.FormEvent) => Promise<void>;
  reset: (newValues?: Partial<T>) => void;
  getFieldProps: (field: keyof T) => {
    value: T[keyof T];
    onChange: (value: T[keyof T]) => void;
    onBlur: () => void;
    error: string | undefined;
    hasError: boolean;
  };
}

/**
 * Comprehensive form validation hook
 */
export function useFormValidation<T extends Record<string, unknown>>({
  schema,
  initialValues = {},
  asyncValidators = [],
  validateOnChange = false,
  validateOnBlur = true,
}: UseFormValidationOptions<T>): UseFormValidationReturn<T> {
  const [values, setValuesState] = useState<Partial<T>>(initialValues);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouchedState] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Create async validator
  const asyncValidator = useMemo(
    () => createAsyncValidator(schema, asyncValidators),
    [schema, asyncValidators]
  );

  // Check if form is valid
  const isValid = useMemo(() => {
    return Object.keys(errors).length === 0;
  }, [errors]);

  // Set single field value
  const setValue = useCallback(
    async (field: keyof T, value: T[keyof T]) => {
      setValuesState((prev) => ({ ...prev, [field]: value }));

      if (validateOnChange) {
        await validateFieldInternal(field, value);
      }
    },
    [validateOnChange, validateFieldInternal]
  );

  // Set multiple values
  const setValues = useCallback((newValues: Partial<T>) => {
    setValuesState((prev) => ({ ...prev, ...newValues }));
  }, []);

  // Set field error
  const setFieldError = useCallback((field: keyof T, error: string) => {
    setErrors((prev) => ({ ...prev, [field as string]: error }));
  }, []);

  // Clear field error
  const clearFieldError = useCallback((field: keyof T) => {
    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[field as string];
      return newErrors;
    });
  }, []);

  // Set field touched
  const setTouched = useCallback((field: keyof T, isTouched = true) => {
    setTouchedState((prev) => ({ ...prev, [field as string]: isTouched }));
  }, []);

  // Internal field validation
  const validateFieldInternal = useCallback(
    async (field: keyof T, value?: T[keyof T]) => {
      const fieldValue = value !== undefined ? value : values[field];
      
      try {
        // Create a partial object for validation
        const partialData = { [field]: fieldValue };
        const result = validateData(schema.partial(), partialData);
        
        if (result.success) {
          clearFieldError(field);
          return true;
        } else {
          const fieldError = result.fieldErrors?.[field as string];
          if (fieldError) {
            setFieldError(field, fieldError);
          }
          return false;
        }
      } catch (error) {
        setFieldError(field, 'Validation error');
        return false;
      }
    },
    [values, schema, setFieldError, clearFieldError]
  );

  // Validate single field
  const validateFieldPublic = useCallback(
    async (field: keyof T) => {
      setTouched(field, true);
      return await validateFieldInternal(field);
    },
    [validateFieldInternal, setTouched]
  );

  // Validate entire form
  const validateForm = useCallback(async (): Promise<ValidationResult<T>> => {
    const result = await asyncValidator(values);
    
    if (result.fieldErrors) {
      setErrors(result.fieldErrors);
    } else {
      setErrors({});
    }
    
    // Mark all fields as touched
    const allFields = Object.keys(values);
    const touchedState: Record<string, boolean> = {};
    allFields.forEach((field) => {
      touchedState[field] = true;
    });
    setTouchedState(touchedState);
    
    return result;
  }, [asyncValidator, values]);

  // Handle form submission
  const handleSubmit = useCallback(
    (onSubmit: (data: T) => Promise<void> | void) =>
      async (e?: React.FormEvent) => {
        if (e) {
          e.preventDefault();
        }

        setIsSubmitting(true);

        try {
          const result = await validateForm();
          
          if (result.success && result.data) {
            await onSubmit(result.data);
          }
        } catch (error) {
          console.error('Form submission error:', error);
        } finally {
          setIsSubmitting(false);
        }
      },
    [validateForm]
  );

  // Reset form
  const reset = useCallback((newValues?: Partial<T>) => {
    setValuesState(newValues || initialValues);
    setErrors({});
    setTouchedState({});
    setIsSubmitting(false);
  }, [initialValues]);

  // Get field props for easy integration
  const getFieldProps = useCallback(
    (field: keyof T) => ({
      value: values[field] || '',
      onChange: (value: T[keyof T]) => setValue(field, value),
      onBlur: () => {
        setTouched(field, true);
        if (validateOnBlur) {
          validateFieldInternal(field);
        }
      },
      error: errors[field as string],
      hasError: Boolean(errors[field as string] && touched[field as string]),
    }),
    [values, errors, touched, setValue, setTouched, validateOnBlur, validateFieldInternal]
  );

  return {
    values,
    errors,
    touched,
    isValid,
    isSubmitting,
    setValue,
    setValues,
    setFieldError,
    clearFieldError,
    setTouched,
    validateField: validateFieldPublic,
    validateForm,
    handleSubmit,
    reset,
    getFieldProps,
  };
}

/**
 * Simple validation hook for quick use cases
 */
export function useSimpleValidation<T>(schema: z.ZodSchema<T>) {
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validate = useCallback(
    (data: unknown): ValidationResult<T> => {
      const result = validateData(schema, data);
      
      if (result.fieldErrors) {
        setErrors(result.fieldErrors);
      } else {
        setErrors({});
      }
      
      return result;
    },
    [schema]
  );

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  return {
    validate,
    errors,
    clearErrors,
    hasErrors: Object.keys(errors).length > 0,
  };
}
