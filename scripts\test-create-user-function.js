// Test the create-user function with proper authentication
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function testCreateUserFunction() {
  console.log('🧪 Testing create-user Edge Function...\n');

  try {
    // First, let's test without authentication to see the error
    console.log('1. Testing without authentication (should get 401)...');
    
    try {
      const { data, error } = await supabase.functions.invoke('create-user', {
        body: { 
          email: '<EMAIL>', 
          name: 'Test User', 
          role: 'field_staff' 
        }
      });
      
      if (error) {
        console.log('Expected error (no auth):', error.message);
      } else {
        console.log('Unexpected success:', data);
      }
    } catch (error) {
      console.log('Expected error (no auth):', error.message);
    }

    // Now let's try to get a valid session
    console.log('\n2. Getting current session...');
    
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      console.log('❌ No active session found');
      console.log('📝 To test properly:');
      console.log('1. Open the app in browser: http://localhost:5173');
      console.log('2. Login as an admin user');
      console.log('3. Try creating a user through the UI');
      console.log('4. Check browser console for detailed logs');
      
      // Let's try to sign in programmatically (you'll need to update credentials)
      console.log('\n3. Attempting to sign in...');
      
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>', // Update this
        password: 'your-password' // Update this
      });
      
      if (authError) {
        console.log('❌ Sign in failed:', authError.message);
        console.log('\n📋 Next steps:');
        console.log('1. Update credentials in this script, OR');
        console.log('2. Test manually in the browser');
        return;
      }
      
      console.log('✅ Signed in successfully');
    } else {
      console.log('✅ Active session found');
    }

    // Now test with authentication
    console.log('\n4. Testing with authentication...');
    
    const testUser = {
      email: `test-${Date.now()}@example.com`,
      name: 'Test User',
      role: 'field_staff',
      phone: '+256700000000'
    };

    console.log('📤 Calling create-user function...');
    
    const { data, error } = await supabase.functions.invoke('create-user', {
      body: testUser
    });

    if (error) {
      console.log('❌ Function error:', error);
      console.log('Error message:', error.message);
      console.log('Error details:', JSON.stringify(error, null, 2));
      
      // Check if it's a specific HTTP error
      if (error.message && error.message.includes('non-2xx status code')) {
        console.log('\n🔍 This is the non-2xx error you were seeing!');
        console.log('The function is returning an error status code.');
        console.log('Check the Supabase Dashboard > Functions > Logs for details.');
      }
    } else {
      console.log('✅ Function success:', data);
      
      if (data.success) {
        console.log('🎉 User created successfully!');
        console.log('   Email:', data.user.email);
        console.log('   Name:', data.user.name);
        console.log('   Role:', data.user.role);
        console.log('   Temp Password:', data.user.tempPassword);
      } else {
        console.log('❌ User creation failed:', data.error);
      }
    }

  } catch (error) {
    console.error('❌ Test script error:', error);
    console.log('Error message:', error.message);
    console.log('Error stack:', error.stack);
  }
}

// Run the test
testCreateUserFunction().catch(console.error);
