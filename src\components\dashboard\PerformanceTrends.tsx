import React, { useMemo, useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import {
  Tren<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Calendar,
} from 'lucide-react';
import { ActivitySummary } from '@/hooks/dashboard/useDashboardMetrics';
import { useActivityDistribution } from '@/hooks/dashboard/useActivityDistribution';

interface PerformanceTrendsProps {
  activitySummary: ActivitySummary;
  isLoading?: boolean;
}

const COLORS = ['#10B981', '#3B82F6', '#8B5CF6', '#F59E0B', '#EF4444'];

const ActivityHeatmap: React.FC<{ weeklyTrend: ActivitySummary['weeklyTrend'] }> = ({ weeklyTrend }) => {
  const maxActivities = Math.max(...weeklyTrend.map(d => d.activities));
  
  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium text-gray-700">Weekly Activity Intensity</h4>
      <div className="grid grid-cols-7 gap-1">
        {weeklyTrend.map((day, index) => {
          const intensity = maxActivities > 0 ? day.activities / maxActivities : 0;
          const opacity = Math.max(0.1, intensity);
          
          return (
            <div
              key={index}
              className="aspect-square rounded-sm bg-green-500 flex items-center justify-center text-xs text-white font-medium"
              style={{ opacity }}
              title={`${new Date(day.date).toLocaleDateString()}: ${day.activities} activities`}
            >
              {new Date(day.date).getDate()}
            </div>
          );
        })}
      </div>
      <div className="flex justify-between text-xs text-gray-500">
        <span>Less</span>
        <span>More</span>
      </div>
    </div>
  );
};

const MetricsTrendChart: React.FC<{ weeklyTrend: ActivitySummary['weeklyTrend'] }> = ({ weeklyTrend }) => {
  const data = weeklyTrend.map(day => ({
    ...day,
    date: new Date(day.date).toLocaleDateString('en-US', { weekday: 'short' })
  }));

  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="date" />
        <YAxis yAxisId="left" />
        <YAxis yAxisId="right" orientation="right" />
        <Tooltip />
        <Legend />
        <Line
          yAxisId="left"
          type="monotone"
          dataKey="activities"
          stroke="#10B981"
          strokeWidth={2}
          name="Activities"
        />
        <Line
          yAxisId="left"
          type="monotone"
          dataKey="students"
          stroke="#3B82F6"
          strokeWidth={2}
          name="Students Reached"
        />
        <Line
          yAxisId="right"
          type="monotone"
          dataKey="hours"
          stroke="#8B5CF6"
          strokeWidth={2}
          name="Hours Worked"
        />
      </LineChart>
    </ResponsiveContainer>
  );
};


const ActivityDistribution: React.FC<{ daysBack: number }> = ({ daysBack }) => {
  const { data: distribution, isLoading } = useActivityDistribution(daysBack);

  const chartData = useMemo(() => {
    const counts = distribution?.byType || {};
    const primaryMap: Record<string, { label: string; color: string }> = {
      round_table_session: { label: 'Roundtable Sessions', color: '#10B981' },
      assessment: { label: 'Assessments', color: '#F59E0B' },
      meeting: { label: 'Stakeholder Meetings', color: '#8B5CF6' },
    };

    // Sum selected categories
    const primarySlices = Object.entries(primaryMap)
      .map(([key, meta]) => ({ name: meta.label, value: counts[key] || 0, color: meta.color }))
      .filter(s => s.value > 0);

    // Everything else goes into Others
    const othersValue = Object.entries(counts)
      .filter(([key]) => !(key in primaryMap))
      .reduce((sum, [, val]) => sum + (val || 0), 0);

    const result = [...primarySlices];
    if (othersValue > 0) {
      result.push({ name: 'Others', value: othersValue, color: '#EF4444' });
    }

    // If all are zero (no data), show empty state
    return result.length > 0 ? result : [];
  }, [distribution]);

  return (
    <ResponsiveContainer width="100%" height={250}>
      <PieChart>
        <Pie
          data={chartData}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
        >
          {chartData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        <Tooltip />
      </PieChart>
    </ResponsiveContainer>
  );
};

export const PerformanceTrends: React.FC<PerformanceTrendsProps> = ({
  activitySummary,
  isLoading
}) => {
  const [selectedChart, setSelectedChart] = useState<'trends' | 'distribution'>('trends');
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('7d');

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Performance Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80 bg-gray-100 rounded-lg animate-pulse" />
          </CardContent>
        </Card>
      </div>
    );
  }

  const chartOptions = [
    { value: 'trends', label: 'Activity Trends', icon: TrendingUp },
    { value: 'distribution', label: 'Activity Distribution', icon: PieChartIcon },
  ];

  const renderChart = () => {
    switch (selectedChart) {
      case 'trends':
        return <MetricsTrendChart weeklyTrend={activitySummary.weeklyTrend} />;
      case 'distribution':
        return <ActivityDistribution daysBack={timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90} />;
      default:
        return <MetricsTrendChart weeklyTrend={activitySummary.weeklyTrend} />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Activity Heatmap */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Weekly Activity Heatmap</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ActivityHeatmap weeklyTrend={activitySummary.weeklyTrend} />
        </CardContent>
      </Card>

      {/* Main Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Performance Analytics</span>
            <div className="flex items-center space-x-2">
              <Select value={timeRange} onValueChange={(value: '7d' | '30d' | '90d') => setTimeRange(value)}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">7 days</SelectItem>
                  <SelectItem value="30d">30 days</SelectItem>
                  <SelectItem value="90d">90 days</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedChart} onValueChange={(value: 'trends' | 'distribution') => setSelectedChart(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {chartOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center space-x-2">
                        <option.icon className="h-4 w-4" />
                        <span>{option.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {renderChart()}
        </CardContent>
      </Card>
    </div>
  );
};
