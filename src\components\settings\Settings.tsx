import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PageLayout, PageHeader } from '@/components/layout';
import { Settings as SettingsIcon, User, Bell, Shield, Database } from 'lucide-react';
import { useAccessControl } from '@/hooks/useAccessControl';
import ProfileSettings from './ProfileSettings';
import NotificationSettings from './NotificationSettings';
import AdminSettings from './AdminSettings';

interface SettingsProps {
  defaultTab?: string;
}

const Settings: React.FC<SettingsProps> = ({ defaultTab = 'profile' }) => {
  const [activeTab, setActiveTab] = useState(defaultTab);
  const { roleChecker } = useAccessControl();

  // Determine available tabs based on user role
  const availableTabs = [
    {
      id: 'profile',
      label: 'Profile',
      icon: User,
      component: ProfileSettings,
      description: 'Manage your personal information and preferences',
      roles: ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager']
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: Bell,
      component: NotificationSettings,
      description: 'Configure notification preferences and alerts',
      roles: ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager']
    },
    {
      id: 'admin',
      label: 'Administration',
      icon: Shield,
      component: AdminSettings,
      description: 'System-wide settings and configurations',
      roles: ['admin']
    }
  ];

  // Filter tabs based on user permissions
  const accessibleTabs = availableTabs.filter(tab => {
    if (tab.id === 'admin') {
      return roleChecker.canAccessAdminSettings();
    }
    return roleChecker.canAccessProfileSettings();
  });

  // Ensure the active tab is accessible
  React.useEffect(() => {
    if (!accessibleTabs.find(tab => tab.id === activeTab)) {
      setActiveTab(accessibleTabs[0]?.id || 'profile');
    }
  }, [activeTab, accessibleTabs]);

  return (
    <PageLayout>
      <PageHeader
        title="Settings & Preferences"
        description="Manage your account settings and application preferences"
        icon={SettingsIcon}
      />

      <div className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-3">
            {accessibleTabs.map((tab) => (
              <TabsTrigger 
                key={tab.id} 
                value={tab.id} 
                className="flex items-center gap-2"
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>

          {accessibleTabs.map((tab) => (
            <TabsContent key={tab.id} value={tab.id} className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <tab.icon className="h-5 w-5" />
                    {tab.label} Settings
                  </CardTitle>
                  <CardDescription>
                    {tab.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <tab.component />
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>

        {/* Role-based access information */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900 mb-1">Access Information</h4>
                <p className="text-sm text-blue-700">
                  {roleChecker.isAdmin() && 
                    "As an administrator, you have access to all settings including system administration."
                  }
                  {roleChecker.isProgramOfficer() && 
                    "As a program officer, you can manage your profile and notification preferences."
                  }
                  {roleChecker.isFieldStaff() && 
                    "As field staff, you can manage your profile and notification preferences."
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </PageLayout>
  );
};

export default Settings;
