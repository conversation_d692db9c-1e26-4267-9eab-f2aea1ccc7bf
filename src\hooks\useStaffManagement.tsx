import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';
import { useState } from 'react';
import { userApi, type CreateUserData } from '@/services/userApi';

// Type definitions
type UserRole = Database['public']['Enums']['user_role'];

interface StaffMember {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  division_id: string | null;
  division_name: string | null;
  phone: string | null;
  country: string;
  is_active: boolean;
  created_at: string;
  schools_allocated: number;
}

interface CreateUserData {
  email: string;
  name: string;
  role: UserRole;
  division_id?: string;
  phone?: string;
  password?: string;
}

interface UpdateUserData {
  id: string;
  name?: string;
  role?: UserRole;
  division_id?: string;
  phone?: string;
  is_active?: boolean;
}

export const useStaffManagement = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Legacy hook for backward compatibility (limited to 50 staff members)
  const {
    data: staffMembers,
    isLoading: isLoadingStaff,
    error: staffError,
    refetch: refetchStaff
  } = useQuery({
    queryKey: ['staff-members'],
    queryFn: async (): Promise<StaffMember[]> => {
      console.log('Fetching staff members with emails (limited)...');

      // Use RPC function to get staff with emails and school counts
      const { data, error } = await supabase.rpc('get_staff_with_school_counts');

      if (error) {
        console.error('Error fetching staff with emails:', error);
        throw new Error(`Failed to fetch staff: ${error.message}`);
      }

      // Transform the data to match our interface and limit to 50 for performance
      const staffWithEmails: StaffMember[] = (data || []).slice(0, 50).map(row => ({
        id: row.id,
        name: row.name,
        email: row.email || 'No email',
        role: row.role,
        division_id: row.division_id,
        division_name: row.division_name,
        phone: row.phone,
        country: row.country,
        is_active: row.is_active ?? true,
        created_at: row.created_at || new Date().toISOString(),
        schools_allocated: row.schools_allocated || 0,
      }));

      console.log('✅ Staff members fetched successfully:', staffWithEmails.length, 'members');
      return staffWithEmails;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });

  // Create new user using secure Edge Function
  const createUser = useMutation({
    mutationFn: async (userData: CreateUserData) => {
      console.log('Creating new user via secure API:', userData.email);

      const result = await userApi.createUser(userData);

      if (!result.success) {
        throw new Error(result.error || 'Failed to create user');
      }

      console.log('User created successfully:', userData.email);
      return result.user;
    },
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: `User created successfully. Temporary password: ${data?.tempPassword}`,
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update user profile
  const updateUser = useMutation({
    mutationFn: async (userData: UpdateUserData) => {
      console.log('Updating user:', userData.id);
      
      const { error } = await supabase
        .from('profiles')
        .update({
          name: userData.name,
          role: userData.role,
          division_id: userData.division_id,
          phone: userData.phone,
          is_active: userData.is_active,
        })
        .eq('id', userData.id);

      if (error) {
        console.error('Error updating user:', error);
        throw new Error(`Failed to update user: ${error.message}`);
      }

      return userData;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "User updated successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Reset user password
  const resetPassword = useMutation({
    mutationFn: async ({ userId, newPassword }: { userId: string; newPassword?: string }) => {
      console.log('Resetting password for user:', userId);

      // Use provided password or generate a new temporary password
      const passwordToSet = newPassword || `TempPass${Math.random().toString(36).slice(-8)}!`;

      // Use the PostgreSQL function we created for password reset
      const { data, error } = await supabase.rpc('reset_user_password', {
        user_id: userId,
        new_password: passwordToSet
      });

      if (error) {
        console.error('Error resetting password:', error);
        throw new Error(`Failed to reset password: ${error.message}`);
      }

      if (!data) {
        throw new Error('Password reset failed - user not found');
      }

      return { newPassword: passwordToSet };
    },
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: "Password reset successfully. The user can now log in with the new password.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update user role
  const updateUserRole = useMutation({
    mutationFn: async ({ userId, newRole }: { userId: string; newRole: UserRole }) => {
      console.log('Updating user role:', userId, newRole);

      const { error } = await supabase.rpc('update_user_role', {
        p_user_id: userId,
        p_new_role: newRole
      });

      if (error) {
        console.error('Error updating user role:', error);
        throw new Error(`Failed to update user role: ${error.message}`);
      }

      return { userId, newRole };
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "User role updated successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Toggle user status (activate/deactivate)
  const toggleUserStatus = useMutation({
    mutationFn: async ({ userId, isActive }: { userId: string; isActive: boolean }) => {
      console.log('Toggling user status:', userId, isActive);

      const { error } = await supabase.rpc('toggle_user_status', {
        p_user_id: userId,
        p_is_active: isActive
      });

      if (error) {
        console.error('Error toggling user status:', error);
        throw new Error(`Failed to update user status: ${error.message}`);
      }

      return { userId, isActive };
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "User status updated successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete user (remove from auth and profiles)
  const deleteUser = useMutation({
    mutationFn: async (userId: string) => {
      console.log('Deleting user:', userId);

      // Check if admin client is available
      if (!supabaseAdmin) {
        throw new Error(
          'Admin operations not available. Please add VITE_SUPABASE_SERVICE_ROLE_KEY to your .env file. ' +
          'Get the service_role key from your Supabase project settings > API > service_role.'
        );
      }

      // Delete from auth (this will cascade to profile due to foreign key)
      const { error: authError } = await supabaseAdmin.auth.admin.deleteUser(userId);

      if (authError) {
        console.error('Error deleting user from auth:', authError);
        throw new Error(`Failed to delete user: ${authError.message}`);
      }

      // Also delete from profiles table using admin client to bypass RLS
      const { error: profileDeleteError } = await supabaseAdmin
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (profileDeleteError) {
        console.warn('Error deleting profile (may already be deleted):', profileDeleteError);
      }

      return userId;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "User deleted successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Bulk create users using secure Edge Function
  const bulkCreateUsers = useMutation({
    mutationFn: async (users: CreateUserData[]) => {
      console.log('Bulk creating users via secure API:', users.length);

      const result = await userApi.bulkCreateUsers(users);

      if (!result.success && result.results.every(r => !r.success)) {
        throw new Error('All user creations failed');
      }

      console.log('Bulk creation completed:', result.summary);
      return result;
    },
    onSuccess: (data) => {
      const successCount = data.summary.successful;
      const errorCount = data.summary.failed;

      toast({
        title: "Bulk Creation Complete",
        description: `Successfully created ${successCount} users. ${errorCount} errors occurred.`,
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Cleanup specific orphaned profile by ID
  const cleanupOrphanedProfile = useMutation({
    mutationFn: async (profileId: string) => {
      if (!supabaseAdmin) {
        throw new Error('Admin operations not available');
      }

      console.log(`Cleaning up orphaned profile with ID: ${profileId}`);

      // Delete the orphaned profile
      const { error: deleteError } = await supabaseAdmin
        .from('profiles')
        .delete()
        .eq('id', profileId);

      if (deleteError) {
        console.error('Error deleting orphaned profile:', deleteError);
        throw new Error(`Failed to delete orphaned profile: ${deleteError.message}`);
      }

      return { message: `Profile ${profileId} deleted successfully` };
    },
    onSuccess: (data) => {
      toast({
        title: "Profile Cleaned Up",
        description: data.message,
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Cleanup Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Cleanup orphaned profiles (profiles without valid auth users)
  const cleanupOrphanedUsers = useMutation({
    mutationFn: async () => {
      if (!supabaseAdmin) {
        throw new Error('Admin operations not available');
      }

      console.log('Cleaning up orphaned data...');

      // Clean up the specific problematic profile ID if it exists
      const problematicId = '42417dff-eb97-4ae9-a4dd-199332e23e6d';

      const { data: existingProfile, error: selectError } = await supabaseAdmin
        .from('profiles')
        .select('id, email, name')
        .eq('id', problematicId)
        .single();

      if (selectError && selectError.code !== 'PGRST116') {
        // PGRST116 is "not found" which is fine
        throw new Error(`Error checking for problematic profile: ${selectError.message}`);
      }

      if (existingProfile) {
        console.log(`Found problematic profile ${problematicId} (${existingProfile.email}), deleting...`);
        const { error: deleteError } = await supabaseAdmin
          .from('profiles')
          .delete()
          .eq('id', problematicId);

        if (deleteError) {
          throw new Error(`Error deleting problematic profile: ${deleteError.message}`);
        }
      }

      return {
        message: 'Cleanup completed',
        deletedProfile: !!existingProfile,
        profileEmail: existingProfile?.email
      };
    },
    onSuccess: (data) => {
      toast({
        title: "Cleanup Complete",
        description: data.deletedProfile
          ? `Deleted problematic profile: ${data.profileEmail}`
          : "No orphaned data found.",
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Cleanup Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return {
    // Data
    staffMembers: staffMembers || [],

    // Loading states
    isLoadingStaff,
    isUpdatingRole: updateUserRole.isPending,
    isTogglingStatus: toggleUserStatus.isPending,
    isDeletingUser: deleteUser.isPending,
    isBulkCreating: bulkCreateUsers.isPending,
    isCleaningUp: cleanupOrphanedUsers.isPending,

    // Mutations
    createUser,
    updateUser,
    bulkCreateUsers: bulkCreateUsers.mutate,
    updateUserRole: updateUserRole.mutate,
    toggleUserStatus: toggleUserStatus.mutate,
    resetPassword: resetPassword.mutate,
    deleteUser: deleteUser.mutate,
    cleanupOrphanedUsers: cleanupOrphanedUsers.mutate,
    cleanupOrphanedProfile: cleanupOrphanedProfile.mutate,

    // Refetch functions
    refetchStaff,
  };
};

// New paginated staff management hook for better performance
export const usePaginatedStaffManagement = (options: {
  pageSize?: number;
  searchTerm?: string;
  role?: UserRole[];
  isActive?: boolean;
  divisionId?: string;
} = {}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    pageSize = 20,
    searchTerm,
    role,
    isActive,
    divisionId,
  } = options;

  // Simple pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTermState, setSearchTermState] = useState(searchTerm || '');

  const filters: Record<string, unknown> = {};

  if (role && role.length > 0) {
    filters.role = role;
  }

  if (isActive !== undefined) {
    filters.is_active = isActive;
  }

  if (divisionId) {
    filters.division_id = divisionId;
  }

  // Custom query function to use RPC
  const customStaffQuery = useQuery({
    queryKey: ['staff-paginated-custom', currentPage, searchTermState, filters],
    queryFn: async () => {
      console.log('Fetching paginated staff members...');

      // For now, use the existing RPC and implement client-side pagination
      // In production, you'd want to create a paginated RPC function
      const { data, error } = await supabase.rpc('get_staff_with_school_counts');

      if (error) {
        console.error('Error fetching staff:', error);
        throw new Error(`Failed to fetch staff: ${error.message}`);
      }

      let filteredData = (data || []).map(row => ({
        id: row.id,
        name: row.name,
        email: row.email || 'No email',
        role: row.role,
        division_id: row.division_id,
        division_name: row.division_name,
        phone: row.phone,
        country: row.country,
        is_active: row.is_active ?? true,
        created_at: row.created_at || new Date().toISOString(),
        schools_allocated: row.schools_allocated || 0,
      }));

      // Apply filters
      if (role && role.length > 0) {
        filteredData = filteredData.filter(staff => role.includes(staff.role));
      }

      if (isActive !== undefined) {
        filteredData = filteredData.filter(staff => staff.is_active === isActive);
      }

      if (divisionId) {
        filteredData = filteredData.filter(staff => staff.division_id === divisionId);
      }

      // Apply search
      if (searchTermState) {
        const searchLower = searchTermState.toLowerCase();
        filteredData = filteredData.filter(staff =>
          staff.name.toLowerCase().includes(searchLower) ||
          staff.email.toLowerCase().includes(searchLower)
        );
      }

      // Apply pagination
      const totalCount = filteredData.length;
      const offset = (currentPage - 1) * pageSize;
      const paginatedData = filteredData.slice(offset, offset + pageSize);
      const totalPages = Math.ceil(totalCount / pageSize);

      return {
        data: paginatedData,
        totalCount,
        hasNextPage: currentPage < totalPages,
        hasPreviousPage: currentPage > 1,
        currentPage,
        totalPages,
        pageSize,
      };
    },
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });

  return {
    // Data
    data: customStaffQuery.data?.data || [],
    totalCount: customStaffQuery.data?.totalCount || 0,
    hasNextPage: customStaffQuery.data?.hasNextPage || false,
    hasPreviousPage: customStaffQuery.data?.hasPreviousPage || false,
    currentPage: customStaffQuery.data?.currentPage || 1,
    totalPages: customStaffQuery.data?.totalPages || 0,
    pageSize: customStaffQuery.data?.pageSize || pageSize,

    // Loading states
    isLoading: customStaffQuery.isLoading,
    isFetching: customStaffQuery.isFetching,
    error: customStaffQuery.error,

    // Navigation
    goToPage: (page: number) => setCurrentPage(page),
    nextPage: () => setCurrentPage(prev => prev + 1),
    previousPage: () => setCurrentPage(prev => Math.max(1, prev - 1)),
    firstPage: () => setCurrentPage(1),
    lastPage: () => setCurrentPage(customStaffQuery.data?.totalPages || 1),

    // Search
    search: (term: string) => setSearchTermState(term),
    clearSearch: () => setSearchTermState(''),
    searchTerm: searchTermState,

    // Utilities
    refetch: customStaffQuery.refetch,
    isRefetching: customStaffQuery.isRefetching,
  };
};
