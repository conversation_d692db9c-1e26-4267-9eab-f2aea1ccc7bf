import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAccessControl } from '@/hooks/useAccessControl';
import {
  Shield,
  Database,
  Settings,
  Users,
  Bell,
  Lock,
  Server,
  Save
} from 'lucide-react';

const AdminSettings: React.FC = () => {
  const { roleChecker } = useAccessControl();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // System settings state
  const [systemSettings, setSystemSettings] = useState({
    user_registration_enabled: true,
    max_file_upload_size: 10, // MB
    session_timeout: 24, // hours
    backup_frequency: 'daily',
    log_retention_days: 30,
    email_notifications_enabled: true,
    sms_notifications_enabled: false,
    auto_backup_enabled: true,
    security_audit_enabled: true,
  });

  // Only show to admins
  if (!roleChecker.isAdmin()) {
    return (
      <div className="text-center p-8">
        <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">Access Restricted</h3>
        <p className="text-muted-foreground">
          Administrative settings are only available to system administrators.
        </p>
      </div>
    );
  }

  const handleSaveSettings = async () => {
    setIsLoading(true);
    try {
      // In a real implementation, this would save to the database
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      toast({
        title: "Settings Saved",
        description: "System settings have been updated successfully.",
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Failed to save system settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className="space-y-6">
      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            System Status
          </CardTitle>
          <CardDescription>
            Current system status and quick actions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600">Online</div>
              <div className="text-sm text-muted-foreground">System Status</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600">99.9%</div>
              <div className="text-sm text-muted-foreground">Uptime</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-purple-600">156</div>
              <div className="text-sm text-muted-foreground">Active Users</div>
            </div>
          </div>
          

        </CardContent>
      </Card>

      <Separator />

      {/* Security Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lock className="h-5 w-5" />
            Security Settings
          </CardTitle>
          <CardDescription>
            Configure security and access control settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>User Registration</Label>
              <p className="text-sm text-muted-foreground">
                Allow new users to register accounts
              </p>
            </div>
            <Switch
              checked={systemSettings.user_registration_enabled}
              onCheckedChange={(checked) => 
                setSystemSettings(prev => ({ ...prev, user_registration_enabled: checked }))
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Security Audit Logging</Label>
              <p className="text-sm text-muted-foreground">
                Enable detailed security audit logs
              </p>
            </div>
            <Switch
              checked={systemSettings.security_audit_enabled}
              onCheckedChange={(checked) => 
                setSystemSettings(prev => ({ ...prev, security_audit_enabled: checked }))
              }
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Session Timeout (hours)</Label>
              <Input
                type="number"
                value={systemSettings.session_timeout}
                onChange={(e) => 
                  setSystemSettings(prev => ({ 
                    ...prev, 
                    session_timeout: parseInt(e.target.value) || 24 
                  }))
                }
                min="1"
                max="168"
              />
            </div>

            <div className="space-y-2">
              <Label>Max File Upload Size (MB)</Label>
              <Input
                type="number"
                value={systemSettings.max_file_upload_size}
                onChange={(e) => 
                  setSystemSettings(prev => ({ 
                    ...prev, 
                    max_file_upload_size: parseInt(e.target.value) || 10 
                  }))
                }
                min="1"
                max="100"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Separator />

      {/* Data Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Data Management
          </CardTitle>
          <CardDescription>
            Configure data backup and retention policies
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Automatic Backups</Label>
              <p className="text-sm text-muted-foreground">
                Enable automatic database backups
              </p>
            </div>
            <Switch
              checked={systemSettings.auto_backup_enabled}
              onCheckedChange={(checked) => 
                setSystemSettings(prev => ({ ...prev, auto_backup_enabled: checked }))
              }
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Backup Frequency</Label>
              <Select
                value={systemSettings.backup_frequency}
                onValueChange={(value) => 
                  setSystemSettings(prev => ({ ...prev, backup_frequency: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hourly">Hourly</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Log Retention (days)</Label>
              <Input
                type="number"
                value={systemSettings.log_retention_days}
                onChange={(e) => 
                  setSystemSettings(prev => ({ 
                    ...prev, 
                    log_retention_days: parseInt(e.target.value) || 30 
                  }))
                }
                min="7"
                max="365"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Separator />

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            System Notifications
          </CardTitle>
          <CardDescription>
            Configure system-wide notification settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Email Notifications</Label>
              <p className="text-sm text-muted-foreground">
                Enable system email notifications
              </p>
            </div>
            <Switch
              checked={systemSettings.email_notifications_enabled}
              onCheckedChange={(checked) => 
                setSystemSettings(prev => ({ ...prev, email_notifications_enabled: checked }))
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>SMS Notifications</Label>
              <p className="text-sm text-muted-foreground">
                Enable SMS notifications (requires SMS provider)
              </p>
            </div>
            <Switch
              checked={systemSettings.sms_notifications_enabled}
              onCheckedChange={(checked) => 
                setSystemSettings(prev => ({ ...prev, sms_notifications_enabled: checked }))
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button onClick={handleSaveSettings} disabled={isLoading}>
          <Save className="h-4 w-4 mr-2" />
          {isLoading ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>
    </div>
  );
};

export default AdminSettings;
