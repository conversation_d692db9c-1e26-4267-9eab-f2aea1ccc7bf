import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  BookOpen,
  Users,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Target,
  ArrowRight
} from 'lucide-react';
import { DashboardMetrics, useBookDistributionMetrics } from '@/hooks/dashboard/useDashboardMetrics';

interface ImpactIndicatorsProps {
  metrics: DashboardMetrics;
  isLoading?: boolean;
  onViewDetails?: (section: string) => void;
}

interface ProgressCardProps {
  title: string;
  current: number;
  target: number;
  unit: string;
  color: 'green' | 'blue' | 'purple' | 'orange';
  icon: React.ElementType;
  trend?: number;
  customDisplay?: string; // Custom display format for special cases
  completionText?: string; // Custom text to replace "complete"
}

const ProgressCard: React.FC<ProgressCardProps> = ({
  title,
  current,
  target,
  unit,
  color,
  icon: Icon,
  trend,
  customDisplay,
  completionText = 'complete'
}) => {
  const percentage = Math.min(100, (current / target) * 100);
  const isOnTrack = percentage >= 80;
  
  const colorClasses = {
    green: 'text-green-600',
    blue: 'text-blue-600',
    purple: 'text-purple-600',
    orange: 'text-orange-600',
  };

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Icon className={`h-5 w-5 ${colorClasses[color]}`} />
            <h3 className="font-medium text-gray-900">{title}</h3>
          </div>
          {isOnTrack ? (
            <CheckCircle className="h-5 w-5 text-green-500" />
          ) : (
            <AlertCircle className="h-5 w-5 text-yellow-500" />
          )}
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Progress</span>
            <span className="font-medium">
              {customDisplay || `${current.toLocaleString()} / ${target.toLocaleString()} ${unit}`}
            </span>
          </div>
          <Progress value={percentage} className="h-2" />
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500">{percentage.toFixed(1)}% {completionText}</span>
            {trend && (
              <div className="flex items-center space-x-1">
                <TrendingUp className="h-3 w-3 text-green-500" />
                <span className="text-xs text-green-600">+{trend}%</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};





export const ImpactIndicators: React.FC<ImpactIndicatorsProps> = ({
  metrics,
  isLoading,
  onViewDetails
}) => {
  // Fetch book distribution metrics
  const { data: bookMetrics, isLoading: bookMetricsLoading } = useBookDistributionMetrics();

  if (isLoading || bookMetricsLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Impact Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-100 rounded-lg animate-pulse" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Calculate trend for book distribution (comparing current month vs previous implied trend)
  const bookDistributionTrend = bookMetrics?.booksThisMonth && bookMetrics?.totalBooksDistributed
    ? Math.round(((bookMetrics.booksThisMonth / (bookMetrics.totalBooksDistributed || 1)) * 100))
    : 0;

  const progressTargets = [
    {
      title: 'Book Distribution',
      current: bookMetrics?.totalBooksDistributed || 0,
      target: bookMetrics?.totalBooksInStock || 0, // Use actual book stock as target
      unit: 'books',
      color: 'green' as const,
      icon: BookOpen,
      trend: bookDistributionTrend,
      completionText: 'distributed',
    },
    {
      title: 'Student Engagement',
      current: metrics.programReach.studentEngagementPercentage || 0,
      target: 100, // Target 100% engagement
      unit: '%',
      color: 'blue' as const,
      icon: Users,
      trend: metrics.programReach.studentEngagementTrend || 0,
      customDisplay: `${(metrics.programReach.totalStudentsReached || 0).toLocaleString()} / ${(metrics.programReach.totalStudentsInSchools || 0).toLocaleString()} students`,
      completionText: 'reached',
    },
    {
      title: 'School Coverage',
      current: Math.round((metrics.programReach.schoolsCovered / Math.max(metrics.programReach.totalSchools, 1)) * 100),
      target: 100,
      unit: '%',
      color: 'purple' as const,
      icon: CheckCircle,
      trend: metrics.programReach.monthlySchoolsComparison || 0,
      customDisplay: `${metrics.programReach.schoolsCovered} / ${metrics.programReach.totalSchools} schools`,
      completionText: 'covered',
    },
    {
      title: 'Task Completion',
      current: Math.round(metrics.operational.taskCompletionRate),
      target: 95,
      unit: '%',
      color: 'orange' as const,
      icon: Target,
      trend: 3, // This could be calculated from actual task completion trends
      completionText: 'completed',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Progress Tracking */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Impact Progress</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onViewDetails?.('progress')}
            >
              View Details
              <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {progressTargets.map((target, index) => (
              <ProgressCard key={index} {...target} />
            ))}
          </div>
        </CardContent>
      </Card>

    </div>
  );
};
