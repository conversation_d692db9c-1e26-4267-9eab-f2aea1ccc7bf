/**
 * Optimized Dashboard Component
 * Uses single RPC calls instead of multiple individual queries
 */

import React, { useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  RefreshCw, 
  Users, 
  BookOpen, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Target
} from 'lucide-react';
import { 
  useOptimizedDashboard, 
  useDashboardCacheManager,
  useOptimizedStaffPerformance 
} from '@/hooks/dashboard/useOptimizedDashboard';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';

// ============================================================================
// METRIC CARD COMPONENT
// ============================================================================

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ReactNode;
  trend?: number;
  color?: 'default' | 'success' | 'warning' | 'error';
  isLoading?: boolean;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  color = 'default',
  isLoading = false
}) => {
  const colorClasses = {
    default: 'text-blue-600 bg-blue-50',
    success: 'text-green-600 bg-green-50',
    warning: 'text-yellow-600 bg-yellow-50',
    error: 'text-red-600 bg-red-50',
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {subtitle && (
              <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
            )}
            {trend !== undefined && (
              <div className="flex items-center mt-2">
                <TrendingUp className={`h-4 w-4 mr-1 ${trend >= 0 ? 'text-green-500' : 'text-red-500'}`} />
                <span className={`text-sm ${trend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {trend >= 0 ? '+' : ''}{trend}%
                </span>
              </div>
            )}
          </div>
          <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// ============================================================================
// ACTIVITY TIMELINE COMPONENT
// ============================================================================

interface ActivityTimelineProps {
  activities: Array<{
    date: string;
    activities: number;
    students: number;
    hours: number;
  }>;
  isLoading?: boolean;
}

const ActivityTimeline: React.FC<ActivityTimelineProps> = ({ activities, isLoading }) => {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Weekly Activity Trend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            {[...Array(7)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <div className="h-4 bg-gray-200 rounded w-16"></div>
                <div className="h-2 bg-gray-200 rounded flex-1"></div>
                <div className="h-4 bg-gray-200 rounded w-12"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const maxActivities = Math.max(...activities.map(a => a.activities));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Weekly Activity Trend</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {activities.map((day, index) => (
            <div key={index} className="flex items-center space-x-3">
              <div className="w-16 text-sm text-gray-600">
                {new Date(day.date).toLocaleDateString('en-US', { weekday: 'short' })}
              </div>
              <div className="flex-1">
                <Progress 
                  value={maxActivities > 0 ? (day.activities / maxActivities) * 100 : 0} 
                  className="h-2"
                />
              </div>
              <div className="w-12 text-sm text-gray-900 font-medium">
                {day.activities}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// ============================================================================
// CRITICAL ALERTS COMPONENT
// ============================================================================

interface CriticalAlertsProps {
  alerts: Array<{
    id: string;
    type: 'info' | 'warning' | 'error';
    message: string;
    priority: 'low' | 'medium' | 'high';
  }>;
  isLoading?: boolean;
}

const CriticalAlerts: React.FC<CriticalAlertsProps> = ({ alerts, isLoading }) => {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Critical Alerts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <div className="h-4 w-4 bg-gray-200 rounded-full"></div>
                <div className="h-4 bg-gray-200 rounded flex-1"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const alertIcons = {
    info: <CheckCircle className="h-4 w-4 text-blue-500" />,
    warning: <AlertTriangle className="h-4 w-4 text-yellow-500" />,
    error: <AlertTriangle className="h-4 w-4 text-red-500" />,
  };

  const priorityColors = {
    low: 'bg-gray-100 text-gray-800',
    medium: 'bg-yellow-100 text-yellow-800',
    high: 'bg-red-100 text-red-800',
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Critical Alerts</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {alerts.length === 0 ? (
            <div className="text-center py-4 text-gray-500">
              <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
              <p>No critical alerts</p>
            </div>
          ) : (
            alerts.map((alert) => (
              <div key={alert.id} className="flex items-start space-x-3 p-3 rounded-lg border">
                {alertIcons[alert.type]}
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{alert.message}</p>
                </div>
                <Badge className={priorityColors[alert.priority]}>
                  {alert.priority}
                </Badge>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// ============================================================================
// MAIN OPTIMIZED DASHBOARD COMPONENT
// ============================================================================

const OptimizedDashboard: React.FC = () => {
  const { profile } = useAuth();
  const { 
    metrics, 
    activity, 
    distributions, 
    isLoading, 
    isSuccess, 
    hasError, 
    refresh 
  } = useOptimizedDashboard();
  
  const { data: staffPerformance, isLoading: staffLoading } = useOptimizedStaffPerformance(5, 0);
  const { invalidateAll, getCacheStats } = useDashboardCacheManager();

  // Handle refresh
  const handleRefresh = async () => {
    try {
      await refresh();
      toast.success('Dashboard refreshed successfully');
    } catch (error) {
      toast.error('Failed to refresh dashboard');
      console.error('Dashboard refresh error:', error);
    }
  };

  // Performance monitoring
  useEffect(() => {
    if (isSuccess) {
      const stats = getCacheStats();
      console.log('📊 Dashboard cache stats:', stats);
    }
  }, [isSuccess, getCacheStats]);

  // Error handling
  useEffect(() => {
    if (hasError) {
      toast.error('Some dashboard data failed to load');
    }
  }, [hasError]);

  // Memoized metric cards
  const metricCards = useMemo(() => {
    if (!metrics.data) return [];

    const { fieldStaff, programReach, operational } = metrics.data;

    return [
      {
        title: 'Active Field Staff',
        value: fieldStaff.activeStaff,
        subtitle: `${fieldStaff.checkedInToday} checked in today`,
        icon: <Users className="h-6 w-6" />,
        trend: fieldStaff.checkInComplianceRate - 80, // Assuming 80% baseline
        color: fieldStaff.checkInComplianceRate >= 80 ? 'success' : 'warning' as const,
      },
      {
        title: 'Students Reached',
        value: programReach.totalStudentsReached.toLocaleString(),
        subtitle: `${programReach.schoolsCovered} schools covered`,
        icon: <Target className="h-6 w-6" />,
        trend: programReach.monthlySchoolsComparison,
        color: 'success' as const,
      },
      {
        title: 'Task Completion',
        value: `${operational.taskCompletionRate}%`,
        subtitle: 'This month',
        icon: <CheckCircle className="h-6 w-6" />,
        color: operational.taskCompletionRate >= 80 ? 'success' : 'warning' as const,
      },
      {
        title: 'Books Distributed',
        value: distributions.data?.totalBooksDistributed || 0,
        subtitle: `${distributions.data?.schoolsReached || 0} schools`,
        icon: <BookOpen className="h-6 w-6" />,
        color: 'default' as const,
      },
    ];
  }, [metrics.data, distributions.data]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Dashboard Overview
          </h1>
          <p className="text-gray-600">
            Real-time insights and performance metrics
          </p>
        </div>
        <Button 
          onClick={handleRefresh} 
          disabled={isLoading}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Metric Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metricCards.map((metric, index) => (
          <MetricCard
            key={index}
            {...metric}
            isLoading={metrics.isLoading}
          />
        ))}
      </div>

      {/* Activity and Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ActivityTimeline 
          activities={activity.data?.weeklyTrend || []}
          isLoading={activity.isLoading}
        />
        <CriticalAlerts 
          alerts={activity.data?.criticalAlerts || []}
          isLoading={activity.isLoading}
        />
      </div>

      {/* Staff Performance */}
      {profile?.role === 'admin' && (
        <Card>
          <CardHeader>
            <CardTitle>Top Performing Staff</CardTitle>
          </CardHeader>
          <CardContent>
            {staffLoading ? (
              <div className="animate-pulse space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-3">
                    <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                    <div className="h-6 bg-gray-200 rounded w-16"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-3">
                {staffPerformance?.map((staff) => (
                  <div key={staff.id} className="flex items-center justify-between p-3 rounded-lg border">
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{staff.name}</p>
                        <p className="text-sm text-gray-500">
                          {staff.studentsReached} students • {staff.schoolsVisited} schools
                        </p>
                      </div>
                    </div>
                    <Badge 
                      variant={staff.performanceScore >= 85 ? 'default' : 'secondary'}
                    >
                      {staff.performanceScore}%
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Performance Info */}
      {process.env.NODE_ENV === 'development' && (
        <Card className="border-dashed">
          <CardContent className="p-4">
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div>Cache Stats: {JSON.stringify(getCacheStats())}</div>
              <div>Last Updated: {metrics.data?.lastUpdated ? new Date(metrics.data.lastUpdated * 1000).toLocaleTimeString() : 'N/A'}</div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default OptimizedDashboard;
