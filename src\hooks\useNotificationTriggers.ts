import { useEffect, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/useAuth';
import { NotificationService } from '@/services/notificationService';

/**
 * Hook that sets up automatic notification triggers for various app events
 * This integrates with existing functionality to send role-aware notifications
 */
export const useNotificationTriggers = () => {
  const { user, profile } = useAuth();
  const queryClient = useQueryClient();

  const handleQueryUpdate = useCallback(async (event: { query: { queryKey: unknown[]; state: { data: unknown } } }) => {
    const queryKey = event.query.queryKey;
    const data = event.query.state.data;

    try {
      // Handle task updates
      if (queryKey[0] === 'tasks' && queryKey[1] === 'create') {
        // Task created notification
        await NotificationService.sendNotification({
          type: 'task_assigned',
          title: 'New Task Assigned',
          message: `You have been assigned a new task`,
          recipients: [data.assigned_to],
          priority: 'medium',
          metadata: { taskId: data.id }
        });
      }

      if (queryKey[0] === 'tasks' && queryKey[1] === 'update') {
        // Task status change notification
        if (data.status === 'completed') {
          await NotificationService.sendNotification({
            type: 'task_completed',
            title: 'Task Completed',
            message: `Task "${data.title}" has been completed`,
            recipients: [data.created_by],
            priority: 'low',
            metadata: { taskId: data.id }
          });
        }
      }

      // Handle field report submissions
      if (queryKey[0] === 'field-reports' && queryKey[1] === 'create') {
        // Notify program officers and admins of new field reports
        await NotificationService.sendNotification({
          type: 'field_report_submitted',
          title: 'New Field Report',
          message: `A new field report has been submitted`,
          recipients: [], // Will be filtered by role in the service
          priority: 'medium',
          metadata: { reportId: data.id }
        });
      }

      // Handle attendance notifications
      if (queryKey[0] === 'field-staff-attendance') {
        // Check-in notifications for supervisors
        await NotificationService.sendNotification({
          type: 'staff_checked_in',
          title: 'Staff Check-in',
          message: `Field staff has checked in`,
          recipients: [], // Will be filtered by role
          priority: 'low',
          metadata: { attendanceId: data.id }
        });
      }

      // Handle book distribution notifications
      if (queryKey[0] === 'book-distributions' && queryKey[1] === 'create') {
        await NotificationService.sendNotification({
          type: 'book_distribution_logged',
          title: 'Book Distribution Logged',
          message: `New book distribution has been recorded`,
          recipients: [], // Will be filtered by role
          priority: 'medium',
          metadata: { distributionId: data.id }
        });
      }

      // Handle school registration notifications
      if (queryKey[0] === 'schools' && queryKey[1] === 'create') {
        await NotificationService.sendNotification({
          type: 'school_registered',
          title: 'New School Registered',
          message: `A new school has been registered in the system`,
          recipients: [], // Will be filtered by role
          priority: 'medium',
          metadata: { schoolId: data.id }
        });
      }

      // Handle user management notifications
      if (queryKey[0] === 'users' && queryKey[1] === 'create') {
        await NotificationService.sendNotification({
          type: 'user_created',
          title: 'New User Added',
          message: `A new user has been added to the system`,
          recipients: [], // Will be filtered by role (admins only)
          priority: 'low',
          metadata: { userId: data.id }
        });
      }

      // Handle session management notifications
      if (queryKey[0] === 'sessions' && queryKey[1] === 'create') {
        await NotificationService.sendNotification({
          type: 'session_created',
          title: 'New Session Created',
          message: `A new attendance session has been created`,
          recipients: [], // Will be filtered by role
          priority: 'medium',
          metadata: { sessionId: data.id }
        });
      }

      // Handle low stock notifications for books
      if (queryKey[0] === 'books' && queryKey[1] === 'update') {
        if (data.current_quantity <= data.minimum_threshold) {
          await NotificationService.sendNotification({
            type: 'low_stock_alert',
            title: 'Low Stock Alert',
            message: `Book "${data.title}" is running low on stock`,
            recipients: [], // Will be filtered by role
            priority: 'high',
            metadata: { bookId: data.id }
          });
        }
      }

      // Handle overdue task notifications
      if (queryKey[0] === 'tasks' && queryKey[1] === 'overdue') {
        await NotificationService.sendNotification({
          type: 'task_overdue',
          title: 'Overdue Task',
          message: `You have overdue tasks that need attention`,
          recipients: [data.assigned_to],
          priority: 'high',
          metadata: { taskId: data.id }
        });
      }

      // Handle system maintenance notifications
      if (queryKey[0] === 'system' && queryKey[1] === 'maintenance') {
        await NotificationService.sendNotification({
          type: 'system_maintenance',
          title: 'System Maintenance',
          message: `System maintenance is scheduled`,
          recipients: [], // All users
          priority: 'high',
          metadata: { maintenanceId: data.id }
        });
      }

      // Handle data sync notifications
      if (queryKey[0] === 'sync' && queryKey[1] === 'status') {
        if (data.status === 'failed') {
          await NotificationService.sendNotification({
            type: 'sync_failed',
            title: 'Sync Failed',
            message: `Data synchronization has failed`,
            recipients: [user?.id], // Current user only
            priority: 'high',
            metadata: { syncId: data.id }
          });
        }
      }

      // Handle performance alerts
      if (queryKey[0] === 'performance' && queryKey[1] === 'alert') {
        await NotificationService.sendNotification({
          type: 'performance_alert',
          title: 'Performance Alert',
          message: `System performance issue detected`,
          recipients: [], // Admins only
          priority: 'high',
          metadata: { alertId: data.id }
        });
      }

    } catch (error) {
      console.error('Error sending notification:', error);
      // Don't throw - notifications shouldn't break the main flow
    }
  }, [user?.id]);

  useEffect(() => {
    if (!user || !profile) return;

    // Set up query cache listeners for automatic notifications
    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      if (event.type === 'updated' && event.query.state.data) {
        handleQueryUpdate(event);
      }
    });

    return () => {
      unsubscribe();
    };
  }, [user, profile, queryClient, handleQueryUpdate]);

  // Helper function for success notifications
  const notifyOnSuccess = (type: string, data: unknown) => {
    const queryKey = event.query.queryKey;
    const data = event.query.state.data;

    try {
      // Handle task updates
      if (queryKey[0] === 'tasks' && queryKey[1] === 'create') {
        const task = data;
        if (task.assigned_to && task.assigned_to !== user?.id) {
          await NotificationService.notifyTaskAssigned(
            task.id,
            task.title,
            task.assigned_to,
            profile?.name || 'Unknown',
            user?.id,
            profile?.role
          );
        }
      }

      // Handle field report submissions
      if (queryKey[0] === 'field-reports' && queryKey[1] === 'create') {
        const report = data;
        if (profile?.role === 'field_staff') {
          await NotificationService.notifyFieldReportSubmitted(
            report.id,
            report.school_name || 'Unknown School',
            profile.name || 'Unknown Staff',
            user?.id || ''
          );
        }
      }

      // Handle school creation
      if (queryKey[0] === 'schools' && queryKey[1] === 'create') {
        const school = data;
        await NotificationService.notifySchoolAdded(
          school.id,
          school.name,
          school.district || 'Unknown District',
          user?.id,
          profile?.role
        );
      }

      // Handle user creation
      if (queryKey[0] === 'users' && queryKey[1] === 'create') {
        const newUser = data;
        await NotificationService.notifyUserCreated(
          newUser.id,
          newUser.name || 'Unknown User',
          newUser.role || 'Unknown Role',
          user?.id,
          profile?.role
        );
      }

      // Handle book distribution
      if (queryKey[0] === 'distributions' && queryKey[1] === 'create') {
        const distribution = data;
        await NotificationService.notifyBookDistribution(
          distribution.id,
          distribution.school_name || 'Unknown School',
          distribution.book_title || 'Unknown Book',
          distribution.quantity || 0,
          user?.id,
          profile?.role
        );
      }

    } catch (error) {
      console.error('Error sending notification:', error);
    }
  };

  // Manual notification triggers that can be called from components
  const triggers = {
    // Trigger task assignment notification
    notifyTaskAssigned: async (taskId: string, taskTitle: string, assignedToId: string) => {
      if (!user || !profile) return;
      
      await NotificationService.notifyTaskAssigned(
        taskId,
        taskTitle,
        assignedToId,
        profile.name || 'Unknown',
        user.id,
        profile.role
      );
    },

    // Trigger field report status notification
    notifyFieldReportStatus: async (
      reportId: string,
      schoolName: string,
      staffId: string,
      approved: boolean,
      reason?: string
    ) => {
      if (!user || !profile) return;
      
      await NotificationService.notifyFieldReportStatus(
        reportId,
        schoolName,
        staffId,
        approved,
        reason,
        user.id,
        profile.role
      );
    },

    // Trigger low inventory notification
    notifyLowInventory: async (bookId: string, bookTitle: string, currentQuantity: number, threshold: number) => {
      await NotificationService.notifyLowInventory(bookId, bookTitle, currentQuantity, threshold);
    },

    // Trigger system update notification
    notifySystemUpdate: async (title: string, message: string, targetRoles?: string[]) => {
      await NotificationService.notifySystemUpdate(title, message, targetRoles);
    },

    // Trigger check-in reminder
    notifyCheckInReminder: async (staffId: string) => {
      await NotificationService.notifyCheckInReminder(staffId);
    },
  };

  return triggers;
};

/**
 * Hook for sending notifications when specific mutations succeed
 * This can be used in components that perform actions requiring notifications
 */
export const useNotificationOnMutation = () => {
  const { user, profile } = useAuth();

  const notifyOnSuccess = (type: string, data: Record<string, unknown>) => {
    if (!user || !profile) return;

    switch (type) {
      case 'task_assigned':
        NotificationService.notifyTaskAssigned(
          data.taskId,
          data.taskTitle,
          data.assignedToId,
          profile.name || 'Unknown',
          user.id,
          profile.role
        );
        break;

      case 'field_report_submitted':
        if (profile.role === 'field_staff') {
          NotificationService.notifyFieldReportSubmitted(
            data.reportId,
            data.schoolName,
            profile.name || 'Unknown Staff',
            user.id
          );
        }
        break;

      case 'field_report_status':
        NotificationService.notifyFieldReportStatus(
          data.reportId,
          data.schoolName,
          data.staffId,
          data.approved,
          data.reason,
          user.id,
          profile.role
        );
        break;

      case 'school_added':
        NotificationService.notifySchoolAdded(
          data.schoolId,
          data.schoolName,
          data.district,
          user.id,
          profile.role
        );
        break;

      case 'user_created':
        NotificationService.notifyUserCreated(
          data.userId,
          data.userName,
          data.userRole,
          user.id,
          profile.role
        );
        break;

      case 'book_distribution':
        NotificationService.notifyBookDistribution(
          data.distributionId,
          data.schoolName,
          data.bookTitle,
          data.quantity,
          user.id,
          profile.role
        );
        break;

      default:
        console.warn('Unknown notification type:', type);
    }
  };

  return { notifyOnSuccess };
};
