// Frontend API client for user management using Supabase Edge Functions
import { supabase } from '@/integrations/supabase/client';
import { config } from '@/config/environment';

export interface CreateUserData {
  email: string;
  name: string;
  role: string;
  division_id?: string;
  phone?: string;
}

export interface CreateUserResponse {
  success: boolean;
  user?: {
    id: string;
    email: string;
    name: string;
    role: string;
    tempPassword: string;
  };
  error?: string;
}

export interface BulkCreateResponse {
  success: boolean;
  results: Array<{
    email: string;
    success: boolean;
    user?: CreateUserResponse['user'];
    error?: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

class UserApiClient {
  private async getAuthToken(): Promise<string | null> {
    const { data: { session } } = await supabase.auth.getSession();
    return session?.access_token || null;
  }

  private async makeRequest<T>(functionName: string, data: any): Promise<T> {
    const token = await this.getAuthToken();

    if (!token) {
      throw new Error('No authentication token available');
    }

    console.log(`🔧 Calling Edge Function: ${functionName}`);
    console.log(`🔑 Token available: ${token ? 'Yes' : 'No'}`);

    const { data: result, error } = await supabase.functions.invoke(functionName, {
      body: data,
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (error) {
      console.error(`❌ Edge Function error:`, error);
      throw new Error(error.message || 'Function invocation failed');
    }

    console.log(`✅ Edge Function response:`, result);

    if (!result.success && result.error) {
      throw new Error(result.error);
    }

    return result;
  }

  async createUser(userData: CreateUserData): Promise<CreateUserResponse> {
    return this.makeRequest<CreateUserResponse>('create-user', userData);
  }

  async bulkCreateUsers(users: CreateUserData[]): Promise<BulkCreateResponse> {
    return this.makeRequest<BulkCreateResponse>('bulk-create-users', { users });
  }
}

export const userApi = new UserApiClient();
