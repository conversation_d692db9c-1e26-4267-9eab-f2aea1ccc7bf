import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Award,
  BarChart3,
  GraduationCap,
  MessageSquare,
  FileText,
  TrendingUp,
  Users,
  School,
  Target,
  Calendar,
  Download,
  Plus
} from 'lucide-react';
import { PageLayout, PageHeader } from '@/components/layout';
import { useDashboardMetrics } from '@/hooks/dashboard/useDashboardMetrics';

// Import impact components
import ComprehensiveImpactDashboard from './analytics/ComprehensiveImpactDashboard';
import StudentOutcomesModule from './student-outcomes/StudentOutcomesModule';
import SimplifiedImpactReports from './reports/SimplifiedImpactReports';

interface ImpactOverviewProps {
  defaultTab?: string;
}

const ImpactOverview: React.FC<ImpactOverviewProps> = ({
  defaultTab = 'overview'
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab);

  // Get real data from dashboard metrics
  const { data: metrics, isLoading } = useDashboardMetrics();

  const impactSections = [
    {
      id: 'overview',
      title: 'Impact Overview',
      description: 'Comprehensive view of all impact metrics and analytics',
      icon: BarChart3,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      id: 'student',
      title: 'Student',
      description: 'Student leadership development and program effectiveness tracking',
      icon: GraduationCap,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      id: 'reports',
      title: 'Reports',
      description: 'All reports across the application - impact, attendance, performance',
      icon: FileText,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    }
  ];

  const quickStats = [
    {
      title: 'Active Schools',
      value: isLoading ? '...' : (metrics?.programReach.schoolsCovered || 0).toString(),
      icon: School,
      color: 'text-blue-600',
      change: metrics?.programReach.monthlySchoolsComparison
        ? `${metrics.programReach.monthlySchoolsComparison > 0 ? '+' : ''}${Math.round(metrics.programReach.monthlySchoolsComparison)}%`
        : '+0%',
      changeType: (metrics?.programReach.monthlySchoolsComparison || 0) >= 0 ? 'positive' : 'negative'
    },
    {
      title: 'Students Reached',
      value: isLoading ? '...' : (metrics?.programReach.totalStudentsReached || 0).toLocaleString(),
      icon: Users,
      color: 'text-green-600',
      change: metrics?.programReach.weeklyStudentsComparison
        ? `${metrics.programReach.weeklyStudentsComparison > 0 ? '+' : ''}${Math.round(metrics.programReach.weeklyStudentsComparison)}%`
        : '+0%',
      changeType: (metrics?.programReach.weeklyStudentsComparison || 0) >= 0 ? 'positive' : 'negative'
    },
    {
      title: 'Student Engagement',
      value: isLoading ? '...' : `${Math.round(metrics?.programReach.studentEngagementPercentage || 0)}%`,
      icon: TrendingUp,
      color: 'text-purple-600',
      change: metrics?.programReach.studentEngagementTrend
        ? `${metrics.programReach.studentEngagementTrend > 0 ? '+' : ''}${Math.round(metrics.programReach.studentEngagementTrend)}%`
        : '+0%',
      changeType: (metrics?.programReach.studentEngagementTrend || 0) >= 0 ? 'positive' : 'negative'
    }
  ];

  const quickActions = [
    {
      label: 'Generate Impact Report',
      icon: FileText,
      action: () => setActiveTab('reports'),
    },
    {
      label: 'View Student Progress',
      icon: GraduationCap,
      action: () => setActiveTab('student'),
    },
    {
      label: 'Export Data',
      icon: Download,
      action: () => console.log('Export data'),
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <ComprehensiveImpactDashboard
            schoolId={null}
            dateRange={{ start: new Date(new Date().getFullYear(), 0, 1), end: new Date() }}
            canViewAllData={true}
          />
        );
      
      case 'student':
        return (
          <StudentOutcomesModule
            schoolId={null}
            dateRange={{ start: new Date(new Date().getFullYear(), 0, 1), end: new Date() }}
            canViewAllData={true}
          />
        );
      

      case 'reports':
        return (
          <SimplifiedImpactReports
            schoolId={null}
            dateRange={{ start: new Date(new Date().getFullYear(), 0, 1), end: new Date() }}
            canViewAllData={true}
          />
        );
      
      default:
        return (
          <ComprehensiveImpactDashboard
            schoolId={null}
            dateRange={{ start: new Date(new Date().getFullYear(), 0, 1), end: new Date() }}
            canViewAllData={true}
          />
        );
    }
  };

  const renderOverviewContent = () => (
    <div className="space-y-6">
      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className={`text-xs ${stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
                      {stat.change} from last month
                    </p>
                  </div>
                  <div className={`p-2 rounded-lg bg-gray-100`}>
                    <Icon className={`h-5 w-5 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Impact Sections */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {impactSections.slice(1).map((section) => {
          const Icon = section.icon;
          return (
            <Card
              key={section.id}
              className="cursor-pointer hover:shadow-md transition-shadow border-l-4 border-l-ilead-green"
              onClick={() => setActiveTab(section.id)}
            >
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className={`${section.bgColor} p-3 rounded-lg`}>
                    <Icon className={`h-6 w-6 ${section.color}`} />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                    <p className="text-sm text-gray-600 mt-1">{section.description}</p>
                    <Button variant="outline" size="sm" className="mt-3">
                      View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common impact measurement and reporting tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {quickActions.map((action, index) => {
              const Icon = action.icon;
              return (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={action.action}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {action.label}
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <PageLayout>
      <PageHeader
        title="Impact"
        description="Measure and analyze the impact of educational programs"
        icon={Award}
      />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          {impactSections.map((section) => (
            <TabsTrigger key={section.id} value={section.id} className="text-sm">
              {section.title}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {activeTab === 'overview' ? renderOverviewContent() : renderTabContent()}
        </TabsContent>
      </Tabs>
    </PageLayout>
  );
};

export default ImpactOverview;
