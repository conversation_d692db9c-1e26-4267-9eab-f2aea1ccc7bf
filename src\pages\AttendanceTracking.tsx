/**
 * Attendance Tracking Page
 * Comprehensive attendance tracking system for program officers and admins
 */

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Download,
  RefreshCw,
  Clock,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  FileSpreadsheet,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import { PageLayout, PageHeader } from '@/components/layout';
import AttendanceTable from '@/components/staff/AttendanceTable';
import AttendanceFiltersComponent from '@/components/staff/AttendanceFilters';
import AttendanceSummaryStatsComponent from '@/components/staff/AttendanceSummaryStats';
import {
  useAttendanceRecords,
  useAttendanceSummaryStats,
  useInvalidateAttendanceQueries,
  AttendanceFilters,
} from '@/hooks/useAttendanceTracking';
import { useAuth } from '@/hooks/useAuth';
import { exportAttendanceToExcel, exportSummaryStatsToExcel } from '@/utils/attendanceExport';

const AttendanceTracking: React.FC = () => {
  const { profile, loading: authLoading } = useAuth();
  const [filters, setFilters] = useState<AttendanceFilters>({
    orderBy: 'attendance_date',
    orderDirection: 'DESC',
  });
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
  });
  const [isExporting, setIsExporting] = useState(false);

  const { invalidateAll } = useInvalidateAttendanceQueries();

  // Data fetching
  const {
    data: records = [],
    isLoading: recordsLoading,
    error: recordsError,
    refetch: refetchRecords,
  } = useAttendanceRecords(filters, pagination);

  const {
    data: stats,
    isLoading: statsLoading,
    error: statsError,
    refetch: refetchStats,
  } = useAttendanceSummaryStats(filters);

  const handleFiltersChange = useCallback((newFilters: AttendanceFilters) => {
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page when filters change
  }, []);

  const handleRefresh = useCallback(async () => {
    try {
      await Promise.all([refetchRecords(), refetchStats()]);
      invalidateAll();
      toast.success('Attendance data refreshed successfully');
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast.error('Failed to refresh data. Please try again.');
    }
  }, [refetchRecords, refetchStats, invalidateAll]);

  const handleExportRecords = useCallback(async () => {
    if (!records.length || !stats) {
      toast.error('No data available to export');
      return;
    }

    setIsExporting(true);
    try {
      await exportAttendanceToExcel(records, filters, stats, {
        includeStats: true,
        sheetName: 'Attendance Records',
      });
      toast.success('Attendance data exported successfully');
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export data. Please try again.');
    } finally {
      setIsExporting(false);
    }
  }, [records, filters, stats]);

  const handleExportSummary = useCallback(async () => {
    if (!stats) {
      toast.error('No summary data available to export');
      return;
    }

    setIsExporting(true);
    try {
      await exportSummaryStatsToExcel(stats, filters, {
        filename: 'attendance_summary',
      });
      toast.success('Summary statistics exported successfully');
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export summary. Please try again.');
    } finally {
      setIsExporting(false);
    }
  }, [stats, filters]);

  const handlePageChange = useCallback((newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  }, []);

  // Check access permissions
  const hasAccess = profile && ['admin', 'program_officer'].includes(profile.role);

  // Show loading state while authentication is being checked
  if (authLoading) {
    return (
      <PageLayout>
        <PageHeader
          title="Attendance Tracking"
          description="Loading..."
        />
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="flex items-center gap-2 text-gray-500">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Loading attendance tracking...</span>
            </div>
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  // Access control check
  if (!hasAccess) {
    return (
      <PageLayout>
        <PageHeader
          title="Access Denied"
          description="You don't have permission to view this page"
        />
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
              <h3 className="text-lg font-medium mb-2">Access Restricted</h3>
              <p className="text-gray-600">
                Only program officers and administrators can access attendance tracking.
              </p>
            </div>
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  // Error handling
  if (recordsError || statsError) {
    return (
      <PageLayout>
        <PageHeader
          title="Attendance Tracking"
          description="Comprehensive attendance tracking for field staff"
        />
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load attendance data. Please try refreshing the page.
            {(recordsError || statsError) && (
              <div className="mt-2 text-sm">
                Error: {recordsError?.message || statsError?.message}
              </div>
            )}
          </AlertDescription>
        </Alert>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <PageHeader
        title="Attendance Tracking"
        description="Comprehensive attendance tracking for field staff check-ins and check-outs"
      >
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={recordsLoading || statsLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${(recordsLoading || statsLoading) ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button
            variant="outline"
            onClick={handleExportSummary}
            disabled={isExporting || !stats}
            className="flex items-center gap-2"
          >
            <FileSpreadsheet className="h-4 w-4" />
            Export Summary
          </Button>

          <Button
            onClick={handleExportRecords}
            disabled={isExporting || !records.length}
            className="flex items-center gap-2"
          >
            <Download className={`h-4 w-4 ${isExporting ? 'animate-pulse' : ''}`} />
            {isExporting ? 'Exporting...' : 'Export Data'}
          </Button>
        </div>
      </PageHeader>

      <div className="space-y-6">
        {/* Summary Statistics */}
        <AttendanceSummaryStatsComponent
          stats={stats || {
            total_records: 0,
            total_staff: 0,
            total_hours: 0,
            school_checkins: 0,
            office_checkins: 0,
            completed_sessions: 0,
            active_sessions: 0,
            average_duration_minutes: 0,
            total_distance_km: 0,
          }}
          isLoading={statsLoading}
          filters={filters}
        />

        {/* Filters */}
        <AttendanceFiltersComponent
          filters={filters}
          onFiltersChange={handleFiltersChange}
        />

        {/* Attendance Table */}
        <AttendanceTable
          records={records}
          isLoading={recordsLoading}
          filters={filters}
          onFiltersChange={handleFiltersChange}
        />

        {/* Pagination */}
        {records.length >= pagination.pageSize && (
          <Card>
            <CardContent className="flex items-center justify-between py-4">
              <div className="text-sm text-gray-600">
                Showing page {pagination.page} (up to {pagination.pageSize} records per page)
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1 || recordsLoading}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <span className="text-sm px-3 py-1 bg-gray-100 rounded">
                  Page {pagination.page}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={records.length < pagination.pageSize || recordsLoading}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Footer Information */}
        <Card>
          <CardContent className="py-4">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>Real-time data</span>
                </div>
                <div>
                  Last updated: {new Date().toLocaleTimeString()}
                </div>
              </div>
              <div>
                Showing {records.length} of {stats?.total_records || 0} total records
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </PageLayout>
  );
};

export default AttendanceTracking;
