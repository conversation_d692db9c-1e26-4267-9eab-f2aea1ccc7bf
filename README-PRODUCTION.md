# iLead Field Track - Production Setup Guide

## 🚀 Quick Start for Production

### 1. Environment Configuration

Copy the production environment template:
```bash
cp .env.production .env
```

Set your environment variables:
```bash
# Required
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key
VITE_SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Application
VITE_APP_ENVIRONMENT=production
```

### 2. Validate Configuration

```bash
npm run validate:config
```

### 3. Build for Production

```bash
npm run build:prod
```

### 4. Deploy

Deploy the `dist/` folder to your hosting provider.

## 🔧 Environment Variables Reference

### Required Variables
| Variable | Description | Example |
|----------|-------------|---------|
| `VITE_SUPABASE_URL` | Your Supabase project URL | `https://abc123.supabase.co` |
| `VITE_SUPABASE_ANON_KEY` | Supabase anonymous key | `eyJhbGciOiJIUzI1NiIs...` |
| `VITE_SUPABASE_SERVICE_ROLE_KEY` | Supabase service role key | `eyJhbGciOiJIUzI1NiIs...` |

### Application Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `VITE_APP_NAME` | `iLead Field Track` | Application name |
| `VITE_APP_VERSION` | `1.0.0` | Application version |
| `VITE_APP_ENVIRONMENT` | `development` | Environment (development/production/staging) |

### Feature Flags
| Variable | Default | Description |
|----------|---------|-------------|
| `VITE_ENABLE_DEBUG_MODE` | `false` | Enable debug logging |
| `VITE_ENABLE_ANALYTICS` | `true` | Enable analytics tracking |
| `VITE_ENABLE_ERROR_REPORTING` | `true` | Enable error reporting |

### Performance Settings
| Variable | Default | Description |
|----------|---------|-------------|
| `VITE_API_TIMEOUT` | `30000` | API request timeout (ms) |
| `VITE_MAX_FILE_SIZE` | `10485760` | Max file upload size (bytes) |
| `VITE_MAX_PHOTO_SIZE` | `5242880` | Max photo upload size (bytes) |

### GPS Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `VITE_GPS_TIMEOUT` | `15000` | GPS acquisition timeout (ms) |
| `VITE_GPS_MAX_AGE` | `300000` | Max age for cached GPS (ms) |
| `VITE_GPS_HIGH_ACCURACY` | `true` | Use high accuracy GPS |

### Offline Sync
| Variable | Default | Description |
|----------|---------|-------------|
| `VITE_SYNC_INTERVAL` | `300000` | Sync interval (ms) |
| `VITE_MAX_OFFLINE_STORAGE` | `104857600` | Max offline storage (bytes) |
| `VITE_RETRY_ATTEMPTS` | `3` | Number of retry attempts |

## 🔒 Security Best Practices

### Environment Variables
- ✅ Never commit `.env` files to version control
- ✅ Use deployment platform environment variables for production
- ✅ Rotate service role keys regularly
- ✅ Set `VITE_ENABLE_DEBUG_MODE=false` in production

### Supabase Security
- ✅ Enable Row Level Security (RLS) on all tables
- ✅ Configure proper authentication policies
- ✅ Restrict service role key usage
- ✅ Enable database backups

## 📱 PWA Features

The application includes Progressive Web App features:
- ✅ Offline functionality
- ✅ GPS location services
- ✅ Photo capture and upload
- ✅ Installable on mobile devices
- ✅ App-like experience

## 🚀 Deployment Platforms

### Vercel
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod

# Set environment variables in Vercel dashboard
```

### Netlify
```bash
# Build command: npm run build:prod
# Publish directory: dist
# Set environment variables in Netlify dashboard
```

### Manual Deployment
```bash
npm install
npm run build:prod
# Upload dist/ folder to your hosting provider
```

## 🔍 Validation and Testing

### Pre-deployment Checks
```bash
# Validate configuration
npm run validate:config

# Run tests
npm test

# Build and check for errors
npm run build:prod
```

### Post-deployment Verification
- [ ] User authentication works
- [ ] Admin/Program Officer user creation
- [ ] Role-based access control
- [ ] GPS check-in/check-out
- [ ] Offline sync functionality
- [ ] Photo uploads
- [ ] Report generation

## 🆘 Troubleshooting

### Common Issues

**"Admin operations not available" Error**
- Ensure `VITE_SUPABASE_SERVICE_ROLE_KEY` is set
- Verify the service role key is correct

**Build Failures**
- Run `npm run validate:config` to check configuration
- Ensure all required environment variables are set

**Authentication Issues**
- Verify Supabase URL and anon key
- Check RLS policies in Supabase dashboard

### Getting Help
1. Check browser console for errors
2. Verify environment variables are set correctly
3. Test with a fresh Supabase project
4. Review application logs

---

For detailed deployment instructions, see [DEPLOYMENT.md](./DEPLOYMENT.md)
