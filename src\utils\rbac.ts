import { Database } from '@/integrations/supabase/types';

export type UserRole = Database['public']['Enums']['user_role'];

export interface User {
  id: string;
  role: UserRole;
}

export interface AccessControlConfig {
  requiredRoles?: UserRole[];
  allowSelfAccess?: boolean;
  targetUserId?: string;
  requireOwnership?: boolean;
}

export interface AccessResult {
  hasAccess: boolean;
  reason?: string;
  isOwner?: boolean;
}

/**
 * Core role hierarchy and permissions
 */
export const ROLE_HIERARCHY: Record<UserRole, number> = {
  admin: 3,
  program_officer: 2,
  field_staff: 1,
  staff: 1,
  partner: 1,
  accountant: 1,
  social_media_manager: 1,
};

/**
 * Permission sets for each role
 */
export const ROLE_PERMISSIONS = {
  admin: [
    'manage_users',
    'manage_schools',
    'manage_books',
    'manage_distributions',
    'view_all_reports',
    'manage_tasks',
    'view_analytics',
    'manage_system_settings',
    'access_impact_data',
    'manage_staff',
  ],
  program_officer: [
    'manage_schools',
    'manage_books',
    'manage_distributions',
    'view_all_reports',
    'manage_tasks',
    'view_analytics',
    'limited_user_management',
    'access_impact_data',
  ],
  field_staff: [
    'view_own_data',
    'create_reports',
    'manage_own_tasks',
    'check_in_out',
    'view_assigned_schools',
  ],
  staff: [
    'view_own_data',
    'create_reports',
    'manage_own_tasks',
    'check_in_out',
    'view_assigned_schools',
  ],
  partner: [
    'view_own_data',
    'create_reports',
    'manage_own_tasks',
    'check_in_out',
    'view_assigned_schools',
  ],
  accountant: [
    'view_own_data',
    'create_reports',
    'manage_own_tasks',
    'check_in_out',
    'view_assigned_schools',
    'manage_books',
    'manage_distributions',
  ],
  social_media_manager: [
    'view_own_data',
    'create_reports',
    'manage_own_tasks',
    'check_in_out',
    'view_assigned_schools',
  ],
} as const;

/**
 * Check if a user has a specific permission
 */
export function hasPermission(userRole: UserRole, permission: string): boolean {
  return ROLE_PERMISSIONS[userRole]?.includes(permission as Permission) || false;
}

/**
 * Check if a user role has sufficient level for an operation
 */
export function hasRoleLevel(userRole: UserRole, requiredLevel: UserRole): boolean {
  return ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[requiredLevel];
}

/**
 * Core access control logic
 */
export function checkAccess(
  user: User | null,
  config: AccessControlConfig
): AccessResult {
  // No user means no access
  if (!user) {
    return { hasAccess: false, reason: 'User not authenticated' };
  }

  const {
    requiredRoles = [],
    allowSelfAccess = false,
    targetUserId,
    requireOwnership = false,
  } = config;

  // Check if user has required role
  const hasRequiredRole = requiredRoles.length === 0 || requiredRoles.includes(user.role);
  
  if (!hasRequiredRole) {
    return { 
      hasAccess: false, 
      reason: `Role '${user.role}' not in required roles: ${requiredRoles.join(', ')}` 
    };
  }

  // Check ownership if required
  const isOwner = targetUserId ? user.id === targetUserId : false;
  
  if (requireOwnership && !isOwner) {
    return { 
      hasAccess: false, 
      reason: 'User does not own this resource',
      isOwner: false 
    };
  }

  // Admin and program officers have elevated access
  const hasElevatedAccess = user.role === 'admin' || user.role === 'program_officer';

  // Field-level roles (field_staff, staff, partner, accountant, social_media_manager) can only access their own data unless they have elevated access
  const isFieldLevelRole = ['field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'].includes(user.role);
  if (isFieldLevelRole && targetUserId && !isOwner && !hasElevatedAccess) {
    return {
      hasAccess: false,
      reason: 'Field-level roles can only access their own data',
      isOwner: false
    };
  }

  // Self access check
  if (allowSelfAccess && targetUserId && !isOwner && !hasElevatedAccess) {
    return { 
      hasAccess: false, 
      reason: 'User can only access their own data',
      isOwner: false 
    };
  }

  return { hasAccess: true, isOwner };
}

/**
 * Predefined access configurations for common scenarios
 */
export const ACCESS_CONFIGS = {
  ADMIN_ONLY: {
    requiredRoles: ['admin'] as UserRole[],
  },
  ADMIN_PROGRAM_OFFICER: {
    requiredRoles: ['admin', 'program_officer'] as UserRole[],
  },
  ALL_ROLES: {
    requiredRoles: ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'] as UserRole[],
  },
  SELF_ACCESS: {
    requiredRoles: ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'] as UserRole[],
    allowSelfAccess: true,
  },
  FIELD_STAFF_OWN_DATA: {
    requiredRoles: ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'] as UserRole[],
    requireOwnership: false,
    allowSelfAccess: true,
  },
} as const;

/**
 * Navigation access control
 */
export function canAccessRoute(userRole: UserRole, route: string): boolean {
  const routePermissions: Record<string, UserRole[]> = {
    dashboard: ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'],
    'field-visits': ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'],
    'staff-reports': ['admin', 'program_officer'],
    tasks: ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'],
    schools: ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'],
    books: ['admin', 'program_officer', 'accountant'], // Accountant gets book access
    impact: ['admin', 'program_officer'],
    'staff-management': ['admin', 'program_officer'],
    notifications: ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'],
    settings: ['admin', 'program_officer'],
    reports: ['admin'],
  };

  return routePermissions[route]?.includes(userRole) || false;
}

/**
 * Feature access control
 */
export function canAccessFeature(userRole: UserRole, feature: string): boolean {
  const featurePermissions: Record<string, UserRole[]> = {
    'create-user': ['admin', 'program_officer'], // Both can create users, but with role restrictions
    'manage-user': ['admin'],
    'reset-password': ['admin', 'program_officer'],
    'view-all-reports': ['admin', 'program_officer'],
    'manage-books': ['admin', 'program_officer', 'accountant'], // Accountant gets book management
    'manage-distributions': ['admin', 'program_officer', 'accountant'], // Accountant gets distribution management
    'view-analytics': ['admin', 'program_officer'],
    'view-impact': ['admin', 'program_officer'],
    'check-in-out': ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'],
    'create-report': ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'],
    'manage-own-tasks': ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'],
    'assign-tasks': ['admin', 'program_officer'],
    'view-books-readonly': ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'],
    'access-profile-settings': ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'],
    'access-admin-settings': ['admin'],
    'manage-staff': ['admin', 'program_officer'],
    'view-all-staff-data': ['admin', 'program_officer'],
    'view-own-data': ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'],
    'approve-reports': ['admin', 'program_officer'],
    'manage-timesheets': ['admin', 'program_officer'],
    'export-data': ['admin', 'program_officer'],
    'access-impact-measurement': ['admin'],
    'access-documentation': ['admin'],
  };

  return featurePermissions[feature]?.includes(userRole) || false;
}

/**
 * Centralized role checking utilities
 */
export const RoleChecker = {
  /**
   * Check if user is admin
   */
  isAdmin: (userRole?: UserRole | null): boolean => {
    return userRole === 'admin';
  },

  /**
   * Check if user is program officer
   */
  isProgramOfficer: (userRole?: UserRole | null): boolean => {
    return userRole === 'program_officer';
  },

  /**
   * Check if user is field staff
   */
  isFieldStaff: (userRole?: UserRole | null): boolean => {
    return userRole === 'field_staff';
  },

  /**
   * Check if user is staff
   */
  isStaff: (userRole?: UserRole | null): boolean => {
    return userRole === 'staff';
  },

  /**
   * Check if user is partner
   */
  isPartner: (userRole?: UserRole | null): boolean => {
    return userRole === 'partner';
  },

  /**
   * Check if user is accountant
   */
  isAccountant: (userRole?: UserRole | null): boolean => {
    return userRole === 'accountant';
  },

  /**
   * Check if user is social media manager
   */
  isSocialMediaManager: (userRole?: UserRole | null): boolean => {
    return userRole === 'social_media_manager';
  },

  /**
   * Check if user is any field-level role (field_staff, staff, partner, accountant, social_media_manager)
   */
  isFieldLevelRole: (userRole?: UserRole | null): boolean => {
    return ['field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'].includes(userRole as string);
  },

  /**
   * Check if user is admin or program officer
   */
  isAdminOrProgramOfficer: (userRole?: UserRole | null): boolean => {
    return userRole === 'admin' || userRole === 'program_officer';
  },

  /**
   * Check if user can manage other users
   */
  canManageUsers: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdminOrProgramOfficer(userRole);
  },

  /**
   * Check if user can create users with a specific role
   */
  canCreateUserWithRole: (currentUserRole?: UserRole | null, targetRole?: UserRole | null): boolean => {
    if (!currentUserRole || !targetRole) return false;

    // Admins can create any role
    if (currentUserRole === 'admin') return true;

    // Program officers can only create field-level roles
    if (currentUserRole === 'program_officer') {
      const allowedRoles: UserRole[] = ['staff', 'field_staff', 'partner', 'accountant', 'social_media_manager'];
      return allowedRoles.includes(targetRole);
    }

    // Other roles cannot create users
    return false;
  },

  /**
   * Get available roles for user creation based on current user's role
   */
  getAvailableRolesForCreation: (currentUserRole?: UserRole | null): UserRole[] => {
    if (!currentUserRole) return [];

    if (currentUserRole === 'admin') {
      return ['admin', 'program_officer', 'field_staff', 'staff', 'partner', 'accountant', 'social_media_manager'];
    } else if (currentUserRole === 'program_officer') {
      return ['staff', 'field_staff', 'partner', 'accountant', 'social_media_manager'];
    }

    return [];
  },

  /**
   * Check if user can view all staff data
   */
  canViewAllStaffData: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdminOrProgramOfficer(userRole);
  },

  /**
   * Check if user can manage tasks (assign to others)
   */
  canManageTasks: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdminOrProgramOfficer(userRole);
  },

  /**
   * Check if user can create tasks
   */
  canCreateTasks: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdminOrProgramOfficer(userRole) || RoleChecker.isFieldLevelRole(userRole);
  },

  /**
   * Check if user can manage schools
   */
  canManageSchools: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdminOrProgramOfficer(userRole);
  },

  /**
   * Check if user can export data
   */
  canExportData: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdminOrProgramOfficer(userRole);
  },

  /**
   * Check if user can access reports
   */
  canAccessReports: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdminOrProgramOfficer(userRole);
  },

  /**
   * Check if user can approve reports
   */
  canApproveReports: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdminOrProgramOfficer(userRole);
  },

  /**
   * Check if user can manage timesheets
   */
  canManageTimesheets: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdminOrProgramOfficer(userRole);
  },

  /**
   * Check if user can check in/out
   */
  canCheckInOut: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdminOrProgramOfficer(userRole) || RoleChecker.isFieldLevelRole(userRole);
  },

  /**
   * Check if user can access impact measurement
   */
  canAccessImpactMeasurement: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdminOrProgramOfficer(userRole);
  },

  /**
   * Check if user can access documentation
   */
  canAccessDocumentation: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdmin(userRole);
  },

  /**
   * Check if user can access profile settings
   */
  canAccessProfileSettings: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdminOrProgramOfficer(userRole) || RoleChecker.isFieldLevelRole(userRole);
  },

  /**
   * Check if user can access admin settings
   */
  canAccessAdminSettings: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdmin(userRole);
  },

  /**
   * Check if user can view books (read-only for field-level roles)
   */
  canViewBooks: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdminOrProgramOfficer(userRole) || RoleChecker.isFieldLevelRole(userRole);
  },

  /**
   * Check if user can manage books (write access)
   */
  canManageBooks: (userRole?: UserRole | null): boolean => {
    return RoleChecker.isAdminOrProgramOfficer(userRole) || RoleChecker.isAccountant(userRole);
  },
} as const;
