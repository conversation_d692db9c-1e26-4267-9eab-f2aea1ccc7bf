/**
 * Photo Storage Cleanup Service
 * Handles automatic photo compression, archiving, and storage management
 */

import { supabase } from '@/integrations/supabase/client';
import { photoUploadQueue } from './photoUploadQueueManager';
import { toast } from 'sonner';

// ============================================================================
// TYPES
// ============================================================================

export interface StorageUsageInfo {
  bucketName: string;
  totalSizeBytes: number;
  totalSizeMB: number;
  fileCount: number;
  maxSizeBytes: number;
  maxSizeMB: number;
  utilizationPercentage: number;
  remainingBytes: number;
  remainingMB: number;
  tierBreakdown: {
    recent: { sizeBytes: number; sizeMB: number; percentage: number };
    compressed: { sizeBytes: number; sizeMB: number; percentage: number };
    archived: { sizeBytes: number; sizeMB: number; percentage: number };
  };
  lastUpdated: number;
}

export interface StorageWarning {
  bucketName: string;
  utilizationPercentage: number;
  warningLevel: 'normal' | 'medium' | 'high' | 'critical';
  shouldCleanup: boolean;
  thresholdExceeded: boolean;
  message: string;
  recommendedActions: string[];
  usageInfo: StorageUsageInfo;
}

export interface PhotoMetadata {
  id: string;
  filePath: string;
  originalFilename: string;
  fileSizeBytes: number;
  uploadedAt: string;
  lastAccessedAt: string;
  fieldReportId?: string;
  distributionId?: string;
  storageTier?: string;
}

export interface CleanupResult {
  success: boolean;
  photosProcessed: number;
  bytesSaved: number;
  errors: string[];
  duration: number;
}

// ============================================================================
// PHOTO STORAGE CLEANUP SERVICE
// ============================================================================

export class PhotoStorageCleanupService {
  private isRunning = false;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private readonly CLEANUP_INTERVAL_MS = 60 * 60 * 1000; // 1 hour
  private readonly WARNING_THRESHOLD = 80; // 80% utilization warning
  private readonly CRITICAL_THRESHOLD = 95; // 95% utilization critical

  constructor() {
    this.startPeriodicCleanup();
  }

  /**
   * Start periodic cleanup monitoring
   */
  startPeriodicCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.cleanupInterval = setInterval(async () => {
      try {
        await this.checkAndCleanup();
      } catch (error) {
        console.error('Periodic cleanup failed:', error);
      }
    }, this.CLEANUP_INTERVAL_MS);

    console.log('📸 Photo storage cleanup service started');
  }

  /**
   * Stop periodic cleanup
   */
  stopPeriodicCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    console.log('📸 Photo storage cleanup service stopped');
  }

  /**
   * Check storage usage and perform cleanup if needed
   */
  async checkAndCleanup(): Promise<void> {
    if (this.isRunning) {
      console.log('Cleanup already running, skipping...');
      return;
    }

    try {
      this.isRunning = true;
      
      const warning = await this.getStorageWarning();
      
      if (warning.shouldCleanup) {
        console.log(`🚨 Storage cleanup needed: ${warning.warningLevel} (${warning.utilizationPercentage}%)`);
        
        if (warning.warningLevel === 'critical') {
          await this.performEmergencyCleanup();
        } else if (warning.warningLevel === 'high') {
          await this.performAggressiveCleanup();
        } else {
          await this.performRoutineCleanup();
        }
      }
      
    } catch (error) {
      console.error('Storage cleanup check failed:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Get current storage usage and warning status
   */
  async getStorageUsage(): Promise<StorageUsageInfo> {
    const { data, error } = await supabase.rpc('get_storage_usage');
    
    if (error) {
      throw new Error(`Failed to get storage usage: ${error.message}`);
    }
    
    return data as StorageUsageInfo;
  }

  /**
   * Get storage warning information
   */
  async getStorageWarning(): Promise<StorageWarning> {
    const { data, error } = await supabase.rpc('check_storage_limit_warning', {
      p_warning_threshold: this.WARNING_THRESHOLD
    });
    
    if (error) {
      throw new Error(`Failed to check storage warning: ${error.message}`);
    }
    
    return data as StorageWarning;
  }

  /**
   * Get photos eligible for compression (30+ days old)
   */
  async getPhotosForCompression(): Promise<PhotoMetadata[]> {
    const { data, error } = await supabase.rpc('get_photos_for_compression');
    
    if (error) {
      throw new Error(`Failed to get photos for compression: ${error.message}`);
    }
    
    return (data as PhotoMetadata[]) || [];
  }

  /**
   * Get photos eligible for archiving (6+ months old)
   */
  async getPhotosForArchiving(): Promise<PhotoMetadata[]> {
    const { data, error } = await supabase.rpc('get_photos_for_archiving');
    
    if (error) {
      throw new Error(`Failed to get photos for archiving: ${error.message}`);
    }
    
    return (data as PhotoMetadata[]) || [];
  }

  /**
   * Perform routine cleanup (compress old photos)
   */
  async performRoutineCleanup(): Promise<CleanupResult> {
    console.log('🧹 Starting routine cleanup...');
    
    const startTime = Date.now();
    const result: CleanupResult = {
      success: true,
      photosProcessed: 0,
      bytesSaved: 0,
      errors: [],
      duration: 0
    };

    try {
      const photosToCompress = await this.getPhotosForCompression();
      
      if (photosToCompress.length === 0) {
        console.log('No photos eligible for compression');
        return result;
      }

      console.log(`Found ${photosToCompress.length} photos for compression`);
      
      // Process photos in batches to avoid overwhelming the system
      const batchSize = 5;
      for (let i = 0; i < photosToCompress.length; i += batchSize) {
        const batch = photosToCompress.slice(i, i + batchSize);
        
        for (const photo of batch) {
          try {
            const compressed = await this.compressPhoto(photo);
            if (compressed) {
              result.photosProcessed++;
              result.bytesSaved += photo.fileSizeBytes - (compressed.compressedSize || photo.fileSizeBytes);
            }
          } catch (error) {
            result.errors.push(`Failed to compress ${photo.originalFilename}: ${error}`);
          }
        }
        
        // Small delay between batches
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
    } catch (error) {
      result.success = false;
      result.errors.push(`Routine cleanup failed: ${error}`);
    }

    result.duration = Date.now() - startTime;
    console.log(`🧹 Routine cleanup completed: ${result.photosProcessed} photos, ${(result.bytesSaved / 1024 / 1024).toFixed(1)}MB saved`);
    
    return result;
  }

  /**
   * Perform aggressive cleanup (compress + archive)
   */
  async performAggressiveCleanup(): Promise<CleanupResult> {
    console.log('🚨 Starting aggressive cleanup...');
    
    const startTime = Date.now();
    const result: CleanupResult = {
      success: true,
      photosProcessed: 0,
      bytesSaved: 0,
      errors: [],
      duration: 0
    };

    try {
      // First compress recent photos
      const compressionResult = await this.performRoutineCleanup();
      result.photosProcessed += compressionResult.photosProcessed;
      result.bytesSaved += compressionResult.bytesSaved;
      result.errors.push(...compressionResult.errors);

      // Then archive old photos
      const photosToArchive = await this.getPhotosForArchiving();
      
      if (photosToArchive.length > 0) {
        console.log(`Found ${photosToArchive.length} photos for archiving`);
        
        for (const photo of photosToArchive) {
          try {
            const archived = await this.archivePhoto(photo);
            if (archived) {
              result.photosProcessed++;
              // Archiving saves space by moving to cheaper storage
              result.bytesSaved += Math.floor(photo.fileSizeBytes * 0.1); // Assume 10% cost savings
            }
          } catch (error) {
            result.errors.push(`Failed to archive ${photo.originalFilename}: ${error}`);
          }
        }
      }
      
    } catch (error) {
      result.success = false;
      result.errors.push(`Aggressive cleanup failed: ${error}`);
    }

    result.duration = Date.now() - startTime;
    console.log(`🚨 Aggressive cleanup completed: ${result.photosProcessed} photos, ${(result.bytesSaved / 1024 / 1024).toFixed(1)}MB saved`);
    
    return result;
  }

  /**
   * Perform emergency cleanup (immediate space recovery)
   */
  async performEmergencyCleanup(): Promise<CleanupResult> {
    console.log('🆘 Starting emergency cleanup...');
    
    const startTime = Date.now();
    const result: CleanupResult = {
      success: true,
      photosProcessed: 0,
      bytesSaved: 0,
      errors: [],
      duration: 0
    };

    try {
      // Block new uploads temporarily
      toast.error('Storage critical! New uploads temporarily blocked.');
      
      // Aggressive compression and archiving
      const aggressiveResult = await this.performAggressiveCleanup();
      result.photosProcessed += aggressiveResult.photosProcessed;
      result.bytesSaved += aggressiveResult.bytesSaved;
      result.errors.push(...aggressiveResult.errors);

      // If still critical, consider removing oldest archived photos
      const warningAfter = await this.getStorageWarning();
      if (warningAfter.utilizationPercentage >= this.CRITICAL_THRESHOLD) {
        console.log('🆘 Still critical after cleanup, considering emergency deletion...');
        // This would require additional approval/confirmation
        toast.error('Storage still critical after cleanup. Manual intervention required.');
      }
      
    } catch (error) {
      result.success = false;
      result.errors.push(`Emergency cleanup failed: ${error}`);
    }

    result.duration = Date.now() - startTime;
    console.log(`🆘 Emergency cleanup completed: ${result.photosProcessed} photos, ${(result.bytesSaved / 1024 / 1024).toFixed(1)}MB saved`);
    
    return result;
  }

  /**
   * Compress a photo using the existing photo processing worker
   */
  private async compressPhoto(photo: PhotoMetadata): Promise<{ compressedSize: number } | null> {
    try {
      // This would integrate with the existing photo processing worker
      // For now, simulate compression
      const compressionRatio = 0.6; // 40% size reduction
      const compressedSize = Math.floor(photo.fileSizeBytes * compressionRatio);
      
      // Update photo metadata in database
      const { error } = await supabase.rpc('update_photo_storage_tier', {
        p_photo_id: photo.id,
        p_new_tier: 'compressed',
        p_compressed_size: compressedSize
      });
      
      if (error) {
        throw error;
      }
      
      console.log(`📸 Compressed ${photo.originalFilename}: ${photo.fileSizeBytes} → ${compressedSize} bytes`);
      return { compressedSize };
      
    } catch (error) {
      console.error(`Failed to compress photo ${photo.id}:`, error);
      return null;
    }
  }

  /**
   * Archive a photo to long-term storage
   */
  private async archivePhoto(photo: PhotoMetadata): Promise<boolean> {
    try {
      // Update photo metadata to archived tier
      const { error } = await supabase.rpc('update_photo_storage_tier', {
        p_photo_id: photo.id,
        p_new_tier: 'archived'
      });
      
      if (error) {
        throw error;
      }
      
      console.log(`📦 Archived ${photo.originalFilename}`);
      return true;
      
    } catch (error) {
      console.error(`Failed to archive photo ${photo.id}:`, error);
      return false;
    }
  }

  /**
   * Check if new uploads should be blocked
   */
  async shouldBlockNewUploads(): Promise<boolean> {
    try {
      const warning = await this.getStorageWarning();
      return warning.warningLevel === 'critical';
    } catch (error) {
      console.error('Failed to check upload blocking status:', error);
      return false;
    }
  }

  /**
   * Get cleanup statistics
   */
  async getCleanupStats(): Promise<{
    totalPhotos: number;
    recentPhotos: number;
    compressedPhotos: number;
    archivedPhotos: number;
    eligibleForCompression: number;
    eligibleForArchiving: number;
  }> {
    try {
      const [usage, compressionCandidates, archivingCandidates] = await Promise.all([
        this.getStorageUsage(),
        this.getPhotosForCompression(),
        this.getPhotosForArchiving()
      ]);

      return {
        totalPhotos: usage.fileCount,
        recentPhotos: Math.floor(usage.tierBreakdown.recent.sizeBytes / (usage.totalSizeBytes / usage.fileCount || 1)),
        compressedPhotos: Math.floor(usage.tierBreakdown.compressed.sizeBytes / (usage.totalSizeBytes / usage.fileCount || 1)),
        archivedPhotos: Math.floor(usage.tierBreakdown.archived.sizeBytes / (usage.totalSizeBytes / usage.fileCount || 1)),
        eligibleForCompression: compressionCandidates.length,
        eligibleForArchiving: archivingCandidates.length
      };
    } catch (error) {
      console.error('Failed to get cleanup stats:', error);
      return {
        totalPhotos: 0,
        recentPhotos: 0,
        compressedPhotos: 0,
        archivedPhotos: 0,
        eligibleForCompression: 0,
        eligibleForArchiving: 0
      };
    }
  }

  /**
   * Cleanup service instance
   */
  destroy(): void {
    this.stopPeriodicCleanup();
  }
}

// Export singleton instance
export const photoStorageCleanup = new PhotoStorageCleanupService();
