/**
 * Photo Processing Web Worker
 * Handles image compression, resizing, and upload preparation in background
 */

// ============================================================================
// TYPES
// ============================================================================

export interface PhotoProcessingTask {
  id: string;
  file: File;
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    format?: 'jpeg' | 'webp' | 'png';
    progressive?: boolean;
  };
  metadata?: {
    fieldReportId?: string;
    distributionId?: string;
    timestamp?: number;
  };
}

export interface PhotoProcessingResult {
  id: string;
  success: boolean;
  processedBlob?: Blob;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  dimensions: {
    width: number;
    height: number;
  };
  error?: string;
  processingTime: number;
}

export interface ProgressUpdate {
  id: string;
  stage: 'reading' | 'processing' | 'compressing' | 'complete' | 'error';
  progress: number;
  message: string;
}

// ============================================================================
// COMPRESSION UTILITIES
// ============================================================================

class PhotoProcessor {
  private canvas: OffscreenCanvas;
  private ctx: OffscreenCanvasRenderingContext2D;

  constructor() {
    this.canvas = new OffscreenCanvas(1, 1);
    this.ctx = this.canvas.getContext('2d')!;
  }

  /**
   * Process and compress image
   */
  async processImage(
    file: File, 
    options: PhotoProcessingTask['options'],
    onProgress: (update: ProgressUpdate) => void
  ): Promise<PhotoProcessingResult> {
    const startTime = performance.now();
    const taskId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // Stage 1: Reading file
      onProgress({
        id: taskId,
        stage: 'reading',
        progress: 10,
        message: 'Reading image file...'
      });

      const imageBitmap = await createImageBitmap(file);
      const originalSize = file.size;

      // Stage 2: Processing dimensions
      onProgress({
        id: taskId,
        stage: 'processing',
        progress: 30,
        message: 'Processing image dimensions...'
      });

      const { width, height } = this.calculateDimensions(
        imageBitmap.width,
        imageBitmap.height,
        options.maxWidth || 1920,
        options.maxHeight || 1080
      );

      // Stage 3: Compressing
      onProgress({
        id: taskId,
        stage: 'compressing',
        progress: 60,
        message: 'Compressing image...'
      });

      // Resize canvas
      this.canvas.width = width;
      this.canvas.height = height;

      // Clear and draw
      this.ctx.clearRect(0, 0, width, height);
      
      // Apply image smoothing for better quality
      this.ctx.imageSmoothingEnabled = true;
      this.ctx.imageSmoothingQuality = 'high';
      
      this.ctx.drawImage(imageBitmap, 0, 0, width, height);

      // Convert to blob with compression
      const format = options.format || 'jpeg';
      const quality = options.quality || 0.8;
      
      const processedBlob = await this.canvas.convertToBlob({
        type: `image/${format}`,
        quality: format === 'jpeg' ? quality : undefined
      });

      const compressedSize = processedBlob.size;
      const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100;

      // Stage 4: Complete
      onProgress({
        id: taskId,
        stage: 'complete',
        progress: 100,
        message: 'Image processing complete'
      });

      const processingTime = performance.now() - startTime;

      return {
        id: taskId,
        success: true,
        processedBlob,
        originalSize,
        compressedSize,
        compressionRatio,
        dimensions: { width, height },
        processingTime
      };

    } catch (error) {
      const processingTime = performance.now() - startTime;
      
      onProgress({
        id: taskId,
        stage: 'error',
        progress: 0,
        message: `Processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      });

      return {
        id: taskId,
        success: false,
        originalSize: file.size,
        compressedSize: 0,
        compressionRatio: 0,
        dimensions: { width: 0, height: 0 },
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime
      };
    }
  }

  /**
   * Calculate optimal dimensions maintaining aspect ratio
   */
  private calculateDimensions(
    originalWidth: number,
    originalHeight: number,
    maxWidth: number,
    maxHeight: number
  ): { width: number; height: number } {
    const aspectRatio = originalWidth / originalHeight;

    let width = originalWidth;
    let height = originalHeight;

    // Scale down if larger than max dimensions
    if (width > maxWidth) {
      width = maxWidth;
      height = width / aspectRatio;
    }

    if (height > maxHeight) {
      height = maxHeight;
      width = height * aspectRatio;
    }

    return {
      width: Math.round(width),
      height: Math.round(height)
    };
  }

  /**
   * Batch process multiple images
   */
  async processBatch(
    tasks: PhotoProcessingTask[],
    onProgress: (update: ProgressUpdate) => void,
    onComplete: (result: PhotoProcessingResult) => void
  ): Promise<PhotoProcessingResult[]> {
    const results: PhotoProcessingResult[] = [];
    
    for (let i = 0; i < tasks.length; i++) {
      const task = tasks[i];
      
      // Update batch progress
      onProgress({
        id: 'batch',
        stage: 'processing',
        progress: (i / tasks.length) * 100,
        message: `Processing image ${i + 1} of ${tasks.length}...`
      });

      const result = await this.processImage(task.file, task.options, onProgress);
      results.push(result);
      onComplete(result);
    }

    return results;
  }
}

// ============================================================================
// WORKER MESSAGE HANDLING
// ============================================================================

const processor = new PhotoProcessor();

// Handle messages from main thread
self.onmessage = async (event: MessageEvent) => {
  const { type, payload } = event.data;

  switch (type) {
    case 'PROCESS_SINGLE':
      await handleSingleProcess(payload);
      break;
      
    case 'PROCESS_BATCH':
      await handleBatchProcess(payload);
      break;
      
    case 'HEALTH_CHECK':
      self.postMessage({
        type: 'HEALTH_RESPONSE',
        payload: {
          status: 'healthy',
          timestamp: Date.now(),
          capabilities: {
            offscreenCanvas: !!self.OffscreenCanvas,
            createImageBitmap: !!self.createImageBitmap,
            webp: true, // Assume WebP support
          }
        }
      });
      break;
      
    default:
      self.postMessage({
        type: 'ERROR',
        payload: {
          error: `Unknown message type: ${type}`
        }
      });
  }
};

/**
 * Handle single image processing
 */
async function handleSingleProcess(task: PhotoProcessingTask) {
  try {
    const result = await processor.processImage(
      task.file,
      task.options,
      (progress) => {
        self.postMessage({
          type: 'PROGRESS',
          payload: progress
        });
      }
    );

    self.postMessage({
      type: 'RESULT',
      payload: result
    });

  } catch (error) {
    self.postMessage({
      type: 'ERROR',
      payload: {
        id: task.id,
        error: error instanceof Error ? error.message : 'Processing failed'
      }
    });
  }
}

/**
 * Handle batch image processing
 */
async function handleBatchProcess(tasks: PhotoProcessingTask[]) {
  try {
    const results = await processor.processBatch(
      tasks,
      (progress) => {
        self.postMessage({
          type: 'PROGRESS',
          payload: progress
        });
      },
      (result) => {
        self.postMessage({
          type: 'RESULT',
          payload: result
        });
      }
    );

    self.postMessage({
      type: 'BATCH_COMPLETE',
      payload: {
        totalProcessed: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        totalCompressionRatio: results.reduce((sum, r) => sum + r.compressionRatio, 0) / results.length,
        totalProcessingTime: results.reduce((sum, r) => sum + r.processingTime, 0)
      }
    });

  } catch (error) {
    self.postMessage({
      type: 'ERROR',
      payload: {
        error: error instanceof Error ? error.message : 'Batch processing failed'
      }
    });
  }
}

// ============================================================================
// ERROR HANDLING
// ============================================================================

self.onerror = (error) => {
  self.postMessage({
    type: 'ERROR',
    payload: {
      error: `Worker error: ${error.message}`,
      filename: error.filename,
      lineno: error.lineno
    }
  });
};

self.onunhandledrejection = (event) => {
  self.postMessage({
    type: 'ERROR',
    payload: {
      error: `Unhandled promise rejection: ${event.reason}`
    }
  });
};

// Export types for TypeScript
export type {
  PhotoProcessingTask,
  PhotoProcessingResult,
  ProgressUpdate
};
