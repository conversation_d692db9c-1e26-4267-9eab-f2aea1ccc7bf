/**
 * Office Location Selector Component
 * Displays available office locations with distance and validation status
 */

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Building, MapPin, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { useOfficeLocations } from '@/hooks/useOfficeLocations';
import { validateOfficeGeofence, formatDistance, type LocationData } from '@/utils/officeGeofencing';
import { OfficeLocation } from '@/constants/officeLocations';

interface OfficeLocationSelectorProps {
  selectedOfficeId: string;
  onOfficeChange: (officeId: string) => void;
  userLocation?: LocationData;
  disabled?: boolean;
  className?: string;
}

const OfficeLocationSelector: React.FC<OfficeLocationSelectorProps> = ({
  selectedOfficeId,
  onOfficeChange,
  userLocation,
  disabled = false,
  className = '',
}) => {
  const { data: offices, isLoading, error } = useOfficeLocations();
  const [validationResults, setValidationResults] = useState<Record<string, { isValid: boolean; distance: number; errorMessage?: string; warningMessage?: string }>>({});

  // Validate user location against all offices when location changes
  useEffect(() => {
    if (!userLocation || !offices) return;

    const results: Record<string, { isValid: boolean; distance: number; errorMessage?: string; warningMessage?: string }> = {};
    offices.forEach(office => {
      if (office.isActive) {
        results[office.id] = validateOfficeGeofence(userLocation, office);
      }
    });
    setValidationResults(results);
  }, [userLocation, offices]);

  const selectedOffice = offices?.find(office => office.id === selectedOfficeId);
  const selectedValidation = selectedOfficeId ? validationResults[selectedOfficeId] : null;

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-6">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading office locations...</span>
        </CardContent>
      </Card>
    );
  }

  if (error || !offices || offices.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="py-6">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Unable to load office locations. Please try again later.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building className="h-5 w-5" />
          Office Location
        </CardTitle>
        <CardDescription>
          Select the office location for check-in
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Office Selection */}
        <div>
          <label className="block text-sm font-medium mb-2">Office *</label>
          <Select 
            value={selectedOfficeId} 
            onValueChange={onOfficeChange}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select office location" />
            </SelectTrigger>
            <SelectContent>
              {offices
                .filter(office => office.isActive)
                .map(office => {
                  const validation = validationResults[office.id];
                  const isValid = validation?.isValid;
                  const distance = validation?.distance;

                  return (
                    <SelectItem key={office.id} value={office.id}>
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4" />
                          <span>{office.name}</span>
                        </div>
                        <div className="flex items-center gap-2 ml-4">
                          {distance !== undefined && (
                            <Badge variant="outline" className="text-xs">
                              {formatDistance(distance)}
                            </Badge>
                          )}
                          {isValid !== undefined && (
                            <Badge 
                              variant={isValid ? "default" : "destructive"}
                              className="text-xs"
                            >
                              {isValid ? "In Range" : "Too Far"}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </SelectItem>
                  );
                })}
            </SelectContent>
          </Select>
        </div>

        {/* Selected Office Details */}
        {selectedOffice && (
          <div className="p-3 bg-gray-50 rounded-lg border">
            <div className="flex items-start justify-between">
              <div>
                <h4 className="font-medium text-gray-900">{selectedOffice.name}</h4>
                {selectedOffice.address && (
                  <p className="text-sm text-gray-600 mt-1">{selectedOffice.address}</p>
                )}
                {selectedOffice.description && (
                  <p className="text-sm text-gray-500 mt-1">{selectedOffice.description}</p>
                )}
                <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                  <MapPin className="h-3 w-3" />
                  <span>
                    {selectedOffice.coordinates.latitude.toFixed(6)}, {selectedOffice.coordinates.longitude.toFixed(6)}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    {formatDistance(selectedOffice.geofenceRadius)} radius
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Location Validation Status */}
        {userLocation && selectedValidation && (
          <div>
            {selectedValidation.isValid ? (
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  <strong>Location verified!</strong> You are {formatDistance(selectedValidation.distance)} from {selectedOffice?.name}.
                  {selectedValidation.warningMessage && (
                    <div className="mt-1 text-sm">{selectedValidation.warningMessage}</div>
                  )}
                </AlertDescription>
              </Alert>
            ) : (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {selectedValidation.errorMessage}
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* No Location Warning */}
        {!userLocation && (
          <Alert>
            <MapPin className="h-4 w-4" />
            <AlertDescription>
              Location access is required to validate office proximity. Please enable location services.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default OfficeLocationSelector;
