/**
 * Database Connection Manager
 * Optimizes Supabase client connections and implements connection pooling strategies
 */

import { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/integrations/supabase/types';
import { supabase } from '@/integrations/supabase/client';

// ============================================================================
// TYPES
// ============================================================================

export interface ConnectionConfig {
  maxConnections: number;
  connectionTimeout: number;
  idleTimeout: number;
  retryAttempts: number;
  retryDelay: number;
  enablePooling: boolean;
  enablePreparedStatements: boolean;
}

export interface ConnectionStats {
  activeConnections: number;
  idleConnections: number;
  totalConnections: number;
  failedConnections: number;
  averageResponseTime: number;
  lastHealthCheck: number;
}

export interface ConnectionHealth {
  isHealthy: boolean;
  latency: number;
  errorRate: number;
  lastError?: string;
  timestamp: number;
}

// ============================================================================
// CONNECTION MANAGER CLASS
// ============================================================================

export class DatabaseConnectionManager {
  private static instance: DatabaseConnectionManager;
  private primaryClient: SupabaseClient<Database>;
  private readOnlyClient: SupabaseClient<Database>;
  private connectionPool: Map<string, SupabaseClient<Database>> = new Map();
  private connectionStats: ConnectionStats;
  private config: ConnectionConfig;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private connectionMetrics: Map<string, number[]> = new Map();

  private constructor() {
    this.config = this.getOptimalConfig();
    this.connectionStats = {
      activeConnections: 0,
      idleConnections: 0,
      totalConnections: 0,
      failedConnections: 0,
      averageResponseTime: 0,
      lastHealthCheck: Date.now(),
    };

    this.initializeConnections();
    this.startHealthMonitoring();
  }

  public static getInstance(): DatabaseConnectionManager {
    if (!DatabaseConnectionManager.instance) {
      DatabaseConnectionManager.instance = new DatabaseConnectionManager();
    }
    return DatabaseConnectionManager.instance;
  }

  // ============================================================================
  // CONNECTION INITIALIZATION
  // ============================================================================

  private getOptimalConfig(): ConnectionConfig {
    const isProduction = process.env.NODE_ENV === 'production';
    
    return {
      maxConnections: isProduction ? 10 : 5,
      connectionTimeout: 30000, // 30 seconds
      idleTimeout: 300000, // 5 minutes
      retryAttempts: 3,
      retryDelay: 1000,
      enablePooling: true,
      enablePreparedStatements: true,
    };
  }

  private initializeConnections(): void {
    // Use the existing Supabase client instead of creating new ones
    // This avoids environment variable issues and reuses the configured client

    // Primary connection for read/write operations (use existing client)
    this.primaryClient = supabase;

    // Read-only connection - for now, use the same client
    // In a production environment with connection pooling, this could be a separate client
    this.readOnlyClient = supabase;

    // Add to connection pool
    this.connectionPool.set('primary', this.primaryClient);
    this.connectionPool.set('readonly', this.readOnlyClient);

    this.connectionStats.totalConnections = this.connectionPool.size;
    this.connectionStats.activeConnections = this.connectionPool.size;

    console.log('🔗 Database connections initialized:', this.connectionStats);
  }

  // ============================================================================
  // CONNECTION MANAGEMENT
  // ============================================================================

  /**
   * Get the optimal client for a specific operation
   */
  public getClient(operation: 'read' | 'write' | 'realtime' = 'read'): SupabaseClient<Database> {
    switch (operation) {
      case 'write':
      case 'realtime':
        return this.primaryClient;
      case 'read':
        // Use read-only client for better performance if available
        return this.readOnlyClient || this.primaryClient;
      default:
        return this.primaryClient;
    }
  }

  /**
   * Execute query with connection optimization
   */
  public async executeQuery<T>(
    operation: 'read' | 'write',
    queryFn: (client: SupabaseClient<Database>) => Promise<T>,
    retries = this.config.retryAttempts
  ): Promise<T> {
    const startTime = performance.now();
    const client = this.getClient(operation);
    const connectionId = operation === 'write' ? 'primary' : 'readonly';

    try {
      const result = await Promise.race([
        queryFn(client),
        this.createTimeoutPromise<T>(this.config.connectionTimeout),
      ]);

      // Record successful query metrics
      this.recordQueryMetrics(connectionId, performance.now() - startTime);
      
      return result;

    } catch (error) {
      console.error(`Database query failed (${operation}):`, error);
      
      // Record failed query
      this.connectionStats.failedConnections++;
      
      // Retry logic
      if (retries > 0 && this.shouldRetry(error)) {
        console.log(`Retrying query, attempts remaining: ${retries - 1}`);
        await this.delay(this.config.retryDelay);
        return this.executeQuery(operation, queryFn, retries - 1);
      }

      throw error;
    }
  }

  /**
   * Create timeout promise for query execution
   */
  private createTimeoutPromise<T>(timeout: number): Promise<T> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Query timeout after ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * Determine if error is retryable
   */
  private shouldRetry(error: unknown): boolean {
    if (error instanceof Error) {
      const retryableErrors = [
        'connection timeout',
        'network error',
        'temporary failure',
        'rate limit',
        'server error',
      ];
      
      return retryableErrors.some(retryableError => 
        error.message.toLowerCase().includes(retryableError)
      );
    }
    return false;
  }

  /**
   * Record query performance metrics
   */
  private recordQueryMetrics(connectionId: string, duration: number): void {
    if (!this.connectionMetrics.has(connectionId)) {
      this.connectionMetrics.set(connectionId, []);
    }

    const metrics = this.connectionMetrics.get(connectionId)!;
    metrics.push(duration);

    // Keep only last 100 measurements
    if (metrics.length > 100) {
      metrics.shift();
    }

    // Update average response time
    this.connectionStats.averageResponseTime = 
      Array.from(this.connectionMetrics.values())
        .flat()
        .reduce((sum, time, _, arr) => sum + time / arr.length, 0);
  }

  // ============================================================================
  // HEALTH MONITORING
  // ============================================================================

  private startHealthMonitoring(): void {
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, 30000); // Every 30 seconds
  }

  private async performHealthCheck(): Promise<void> {
    const healthChecks = Array.from(this.connectionPool.entries()).map(
      async ([connectionId, client]) => {
        const startTime = performance.now();
        
        try {
          // Simple health check query
          await client.from('profiles').select('id').limit(1);
          
          const latency = performance.now() - startTime;
          return {
            connectionId,
            isHealthy: true,
            latency,
            errorRate: 0,
            timestamp: Date.now(),
          };
          
        } catch (error) {
          console.warn(`Health check failed for ${connectionId}:`, error);
          
          return {
            connectionId,
            isHealthy: false,
            latency: -1,
            errorRate: 100,
            lastError: error instanceof Error ? error.message : 'Unknown error',
            timestamp: Date.now(),
          };
        }
      }
    );

    const results = await Promise.allSettled(healthChecks);
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const health = result.value;
        console.log(`🏥 Health check ${health.connectionId}:`, {
          healthy: health.isHealthy,
          latency: `${health.latency.toFixed(1)}ms`,
        });
      }
    });

    this.connectionStats.lastHealthCheck = Date.now();
  }

  // ============================================================================
  // CONNECTION OPTIMIZATION
  // ============================================================================

  /**
   * Optimize connection settings based on usage patterns
   */
  public optimizeConnections(): void {
    const avgResponseTime = this.connectionStats.averageResponseTime;
    const errorRate = this.connectionStats.failedConnections / 
      Math.max(this.connectionStats.totalConnections, 1);

    // Adjust timeouts based on performance
    if (avgResponseTime > 5000) { // > 5 seconds
      this.config.connectionTimeout = Math.min(this.config.connectionTimeout * 1.5, 60000);
      console.log('🔧 Increased connection timeout due to slow responses');
    } else if (avgResponseTime < 1000) { // < 1 second
      this.config.connectionTimeout = Math.max(this.config.connectionTimeout * 0.8, 10000);
      console.log('🔧 Decreased connection timeout due to fast responses');
    }

    // Adjust retry attempts based on error rate
    if (errorRate > 0.1) { // > 10% error rate
      this.config.retryAttempts = Math.min(this.config.retryAttempts + 1, 5);
      console.log('🔧 Increased retry attempts due to high error rate');
    } else if (errorRate < 0.01) { // < 1% error rate
      this.config.retryAttempts = Math.max(this.config.retryAttempts - 1, 1);
      console.log('🔧 Decreased retry attempts due to low error rate');
    }
  }

  /**
   * Preload connections for better performance
   */
  public async preloadConnections(): Promise<void> {
    const preloadPromises = Array.from(this.connectionPool.values()).map(
      async (client) => {
        try {
          // Warm up connection with a simple query
          await client.from('profiles').select('id').limit(1);
        } catch (error) {
          console.warn('Connection preload failed:', error);
        }
      }
    );

    await Promise.allSettled(preloadPromises);
    console.log('🔥 Database connections preloaded');
  }

  // ============================================================================
  // UTILITIES
  // ============================================================================

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  public getConnectionStats(): ConnectionStats {
    return { ...this.connectionStats };
  }

  public getConfig(): ConnectionConfig {
    return { ...this.config };
  }

  public async getConnectionHealth(): Promise<ConnectionHealth[]> {
    const healthChecks = Array.from(this.connectionPool.entries()).map(
      async ([connectionId, client]) => {
        const startTime = performance.now();
        
        try {
          await client.from('profiles').select('id').limit(1);
          
          return {
            connectionId,
            isHealthy: true,
            latency: performance.now() - startTime,
            errorRate: 0,
            timestamp: Date.now(),
          };
          
        } catch (error) {
          return {
            connectionId,
            isHealthy: false,
            latency: -1,
            errorRate: 100,
            lastError: error instanceof Error ? error.message : 'Unknown error',
            timestamp: Date.now(),
          };
        }
      }
    );

    const results = await Promise.allSettled(healthChecks);
    return results
      .filter((result): result is PromiseFulfilledResult<ConnectionHealth> => 
        result.status === 'fulfilled'
      )
      .map(result => result.value);
  }

  /**
   * Cleanup connections on app shutdown
   */
  public cleanup(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    // Close all connections
    this.connectionPool.clear();
    this.connectionMetrics.clear();
    
    console.log('🧹 Database connections cleaned up');
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

// Lazy singleton instance getter to avoid initialization issues
export const getDbConnectionManager = () => DatabaseConnectionManager.getInstance();

// Convenience functions
export const getOptimizedClient = (operation: 'read' | 'write' | 'realtime' = 'read') => {
  return getDbConnectionManager().getClient(operation);
};

export const executeOptimizedQuery = <T>(
  operation: 'read' | 'write',
  queryFn: (client: SupabaseClient<Database>) => Promise<T>
) => {
  return getDbConnectionManager().executeQuery(operation, queryFn);
};

export const preloadDatabaseConnections = () => {
  return getDbConnectionManager().preloadConnections();
};

export const getDatabaseStats = () => {
  return getDbConnectionManager().getConnectionStats();
};

export const optimizeDatabaseConnections = () => {
  return getDbConnectionManager().optimizeConnections();
};
