/**
 * Progressive Photo Upload Component
 * Implements chunked uploads with resume capability and real-time progress
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Upload, 
  X, 
  Pause, 
  Play, 
  RotateCcw, 
  CheckCircle, 
  AlertCircle,
  Image as ImageIcon,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import { photoUploadQueue, type UploadProgressEvent } from '@/utils/photoUploadQueueManager';
import { useAuth } from '@/hooks/useAuth';

// ============================================================================
// TYPES
// ============================================================================

interface PhotoUploadItem {
  id: string;
  file: File;
  preview: string;
  status: 'pending' | 'processing' | 'uploading' | 'completed' | 'failed' | 'paused';
  progress: number;
  stage: string;
  error?: string;
  uploadedBytes?: number;
  totalBytes?: number;
  compressionRatio?: number;
  uploadTime?: number;
}

interface ProgressivePhotoUploadProps {
  fieldReportId?: string;
  distributionId?: string;
  bucket?: string;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  maxFiles?: number;
  maxFileSize?: number; // in MB
  acceptedTypes?: string[];
  onUploadComplete?: (urls: string[]) => void;
  onUploadError?: (error: string) => void;
  className?: string;
}

// ============================================================================
// PROGRESSIVE PHOTO UPLOAD COMPONENT
// ============================================================================

const ProgressivePhotoUpload: React.FC<ProgressivePhotoUploadProps> = ({
  fieldReportId,
  distributionId,
  bucket,
  priority = 'MEDIUM',
  maxFiles = 10,
  maxFileSize = 10, // 10MB default
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp'],
  onUploadComplete,
  onUploadError,
  className = '',
}) => {
  const { user } = useAuth();
  const [uploadItems, setUploadItems] = useState<PhotoUploadItem[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dragCounterRef = useRef(0);

  // ============================================================================
  // FILE HANDLING
  // ============================================================================

  const validateFile = useCallback((file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `File type ${file.type} not supported. Please use ${acceptedTypes.join(', ')}.`;
    }
    
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size ${(file.size / 1024 / 1024).toFixed(1)}MB exceeds limit of ${maxFileSize}MB.`;
    }
    
    return null;
  }, [acceptedTypes, maxFileSize]);

  const createPreview = useCallback((file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.readAsDataURL(file);
    });
  }, []);

  const addFiles = useCallback(async (files: FileList | File[]) => {
    const fileArray = Array.from(files);
    
    if (uploadItems.length + fileArray.length > maxFiles) {
      toast.error(`Maximum ${maxFiles} files allowed`);
      return;
    }

    const newItems: PhotoUploadItem[] = [];

    for (const file of fileArray) {
      const validationError = validateFile(file);
      if (validationError) {
        toast.error(validationError);
        continue;
      }

      const preview = await createPreview(file);
      const item: PhotoUploadItem = {
        id: `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        file,
        preview,
        status: 'pending',
        progress: 0,
        stage: 'Ready to upload',
        totalBytes: file.size,
      };

      newItems.push(item);
    }

    setUploadItems(prev => [...prev, ...newItems]);
  }, [uploadItems.length, maxFiles, validateFile, createPreview]);

  // ============================================================================
  // DRAG AND DROP HANDLERS
  // ============================================================================

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current++;
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current--;
    if (dragCounterRef.current === 0) {
      setIsDragOver(false);
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
    dragCounterRef.current = 0;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      addFiles(files);
    }
  }, [addFiles]);

  // ============================================================================
  // UPLOAD MANAGEMENT
  // ============================================================================

  const startUpload = useCallback(async (item: PhotoUploadItem) => {
    if (!user?.id) {
      toast.error('User not authenticated');
      return;
    }

    try {
      setUploadItems(prev => prev.map(i => 
        i.id === item.id ? { ...i, status: 'processing', stage: 'Queuing upload...' } : i
      ));

      const taskId = photoUploadQueue.addToQueue(item.file, {
        bucket,
        priority,
        fieldReportId,
        distributionId,
        userId: user.id,
        maxRetries: 3,
      });

      // Set up progress monitoring
      photoUploadQueue.onProgress(taskId, (event: UploadProgressEvent) => {
        setUploadItems(prev => prev.map(i => {
          if (i.id === item.id) {
            const status = event.stage === 'COMPLETE' ? 'completed' : 
                          event.stage === 'ERROR' ? 'failed' : 
                          event.stage === 'UPLOADING' ? 'uploading' : 'processing';
            
            return {
              ...i,
              status,
              progress: event.progress,
              stage: event.message,
              uploadedBytes: event.bytesUploaded,
              error: event.stage === 'ERROR' ? event.message : undefined,
            };
          }
          return i;
        }));

        // Handle completion
        if (event.stage === 'COMPLETE') {
          photoUploadQueue.offProgress(taskId);
          toast.success(`Photo uploaded successfully`);
        } else if (event.stage === 'ERROR') {
          photoUploadQueue.offProgress(taskId);
          toast.error(`Upload failed: ${event.message}`);
        }
      });

    } catch (error) {
      setUploadItems(prev => prev.map(i => 
        i.id === item.id ? { 
          ...i, 
          status: 'failed', 
          error: error instanceof Error ? error.message : 'Upload failed' 
        } : i
      ));
      toast.error('Failed to start upload');
    }
  }, [user?.id, bucket, priority, fieldReportId, distributionId]);

  const startAllUploads = useCallback(async () => {
    setIsUploading(true);
    const pendingItems = uploadItems.filter(item => item.status === 'pending');
    
    for (const item of pendingItems) {
      await startUpload(item);
    }
    
    setIsUploading(false);
  }, [uploadItems, startUpload]);

  const removeItem = useCallback((itemId: string) => {
    setUploadItems(prev => prev.filter(item => item.id !== itemId));
  }, []);

  const retryItem = useCallback((item: PhotoUploadItem) => {
    setUploadItems(prev => prev.map(i => 
      i.id === item.id ? { ...i, status: 'pending', error: undefined, progress: 0 } : i
    ));
  }, []);

  const clearCompleted = useCallback(() => {
    setUploadItems(prev => prev.filter(item => item.status !== 'completed'));
  }, []);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    const completedItems = uploadItems.filter(item => item.status === 'completed');
    if (completedItems.length > 0 && onUploadComplete) {
      // This would need to be enhanced to get actual URLs from the upload queue
      onUploadComplete([]);
    }
  }, [uploadItems, onUploadComplete]);

  // ============================================================================
  // RENDER HELPERS
  // ============================================================================

  const getStatusIcon = (status: PhotoUploadItem['status']) => {
    switch (status) {
      case 'pending':
        return <Upload className="h-4 w-4 text-gray-500" />;
      case 'processing':
      case 'uploading':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-500" />;
      default:
        return <Upload className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: PhotoUploadItem['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'processing':
      case 'uploading':
        return 'bg-blue-100 text-blue-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <Card 
        className={`border-2 border-dashed transition-colors ${
          isDragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
        }`}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <CardContent className="p-8 text-center">
          <ImageIcon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Upload Photos
          </h3>
          <p className="text-gray-600 mb-4">
            Drag and drop photos here, or click to select files
          </p>
          <Button 
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
          >
            Select Photos
          </Button>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={acceptedTypes.join(',')}
            onChange={(e) => e.target.files && addFiles(e.target.files)}
            className="hidden"
          />
          <p className="text-sm text-gray-500 mt-2">
            Max {maxFiles} files, {maxFileSize}MB each. Supports {acceptedTypes.join(', ')}
          </p>
        </CardContent>
      </Card>

      {/* Upload Queue */}
      {uploadItems.length > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Upload Queue ({uploadItems.length})</CardTitle>
            <div className="flex space-x-2">
              <Button 
                onClick={startAllUploads}
                disabled={isUploading || uploadItems.every(item => item.status !== 'pending')}
                size="sm"
              >
                Upload All
              </Button>
              <Button 
                onClick={clearCompleted}
                variant="outline"
                size="sm"
                disabled={!uploadItems.some(item => item.status === 'completed')}
              >
                Clear Completed
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {uploadItems.map((item) => (
                <div key={item.id} className="flex items-center space-x-4 p-3 border rounded-lg">
                  {/* Preview */}
                  <img 
                    src={item.preview} 
                    alt="Preview" 
                    className="h-12 w-12 object-cover rounded"
                  />
                  
                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {item.file.name}
                      </p>
                      <Badge className={getStatusColor(item.status)}>
                        {item.status}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <span>{formatFileSize(item.file.size)}</span>
                      {item.uploadedBytes && item.totalBytes && (
                        <span>• {formatFileSize(item.uploadedBytes)} / {formatFileSize(item.totalBytes)}</span>
                      )}
                    </div>
                    
                    {/* Progress Bar */}
                    {(item.status === 'processing' || item.status === 'uploading') && (
                      <div className="mt-2">
                        <Progress value={item.progress} className="h-2" />
                        <p className="text-xs text-gray-500 mt-1">{item.stage}</p>
                      </div>
                    )}
                    
                    {/* Error Message */}
                    {item.error && (
                      <p className="text-xs text-red-600 mt-1">{item.error}</p>
                    )}
                  </div>
                  
                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(item.status)}
                    
                    {item.status === 'pending' && (
                      <Button
                        onClick={() => startUpload(item)}
                        size="sm"
                        variant="outline"
                      >
                        <Play className="h-4 w-4" />
                      </Button>
                    )}
                    
                    {item.status === 'failed' && (
                      <Button
                        onClick={() => retryItem(item)}
                        size="sm"
                        variant="outline"
                      >
                        <RotateCcw className="h-4 w-4" />
                      </Button>
                    )}
                    
                    <Button
                      onClick={() => removeItem(item.id)}
                      size="sm"
                      variant="ghost"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ProgressivePhotoUpload;
