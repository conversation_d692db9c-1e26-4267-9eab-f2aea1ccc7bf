/**
 * Test to verify that assessment data flows correctly from form submission to display
 */

// Mock localStorage for testing
const mockLocalStorage = (() => {
  let store: { [key: string]: string } = {};
  
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value;
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    }
  };
})();

// Replace global localStorage with mock
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

describe('Assessment Data Flow', () => {
  beforeEach(() => {
    mockLocalStorage.clear();
  });

  test('should store assessment data in localStorage', () => {
    // Simulate assessment form submission
    const assessmentData = {
      id: 'test_assessment_123',
      school_id: 'school-123',
      assessment_type: 'baseline',
      assessment_date: '2024-01-15',
      academic_year: '2024',
      assessor_name: 'Test Assessor',
      notes: 'Test notes',
      participant_full_name: '<PERSON>',
      participant_age: 15,
      participant_gender: 'male',
      participant_learning_goal: 'Improve leadership skills',
      consider_myself_leader: 4,
      strive_to_finish_started: 5,
      understand_importance_decisions: 3,
      take_full_responsibility: 4,
      understand_leader_values: 5,
      strive_push_limits: 3,
      value_people_around: 4,
      good_example_to_others: 5,
      responsible_with_homework: 4,
      responsible_with_housework: 3,
      confident_in_myself: 4,
      created_at: new Date().toISOString()
    };

    // Store assessment data
    const existingAssessments = JSON.parse(localStorage.getItem('ilead_assessments') || '[]');
    existingAssessments.push(assessmentData);
    localStorage.setItem('ilead_assessments', JSON.stringify(existingAssessments));

    // Verify data is stored
    const storedData = JSON.parse(localStorage.getItem('ilead_assessments') || '[]');
    expect(storedData).toHaveLength(1);
    expect(storedData[0].id).toBe('test_assessment_123');
    expect(storedData[0].participant_full_name).toBe('John Doe');
  });

  test('should convert stored assessments to learning outcomes format', () => {
    // Store multiple assessments for the same school
    const assessments = [
      {
        id: 'assessment_1',
        school_id: 'school-123',
        consider_myself_leader: 4,
        strive_to_finish_started: 5,
        understand_importance_decisions: 3,
        take_full_responsibility: 4,
        understand_leader_values: 5,
        strive_push_limits: 3,
        value_people_around: 4,
        good_example_to_others: 5,
        responsible_with_homework: 4,
        responsible_with_housework: 3,
        confident_in_myself: 4
      },
      {
        id: 'assessment_2',
        school_id: 'school-123',
        consider_myself_leader: 3,
        strive_to_finish_started: 4,
        understand_importance_decisions: 4,
        take_full_responsibility: 3,
        understand_leader_values: 4,
        strive_push_limits: 4,
        value_people_around: 3,
        good_example_to_others: 4,
        responsible_with_homework: 3,
        responsible_with_housework: 4,
        confident_in_myself: 3
      }
    ];

    localStorage.setItem('ilead_assessments', JSON.stringify(assessments));

    // Simulate the conversion logic from StudentOutcomesModule
    const storedAssessments = JSON.parse(localStorage.getItem('ilead_assessments') || '[]');
    const schoolGroups = new Map<string, unknown[]>();

    // Group assessments by school
    storedAssessments.forEach((assessment: Record<string, unknown>) => {
      if (!schoolGroups.has(assessment.school_id)) {
        schoolGroups.set(assessment.school_id, []);
      }
      schoolGroups.get(assessment.school_id)!.push(assessment);
    });

    // Convert to learning outcomes format
    const outcomes = [];
    for (const [schoolId, schoolAssessments] of schoolGroups) {
      // Calculate average leadership score
      const totalScores = schoolAssessments.map((a: Record<string, unknown>) => {
        const scores = [
          a.consider_myself_leader, a.strive_to_finish_started, a.understand_importance_decisions,
          a.take_full_responsibility, a.understand_leader_values, a.strive_push_limits,
          a.value_people_around, a.good_example_to_others, a.responsible_with_homework,
          a.responsible_with_housework, a.confident_in_myself
        ];
        return scores.reduce((sum: number, score: number) => sum + score, 0) / scores.length;
      });
      
      const avgScore = totalScores.reduce((sum: number, score: number) => sum + score, 0) / totalScores.length;

      outcomes.push({
        school_id: schoolId,
        school_name: 'Test School',
        subject: 'leadership_skills',
        total_students: schoolAssessments.length,
        avg_pre_score: avgScore,
        avg_post_score: null,
        avg_improvement: null,
        students_improved: null,
        improvement_rate: null
      });
    }

    // Verify conversion
    expect(outcomes).toHaveLength(1);
    expect(outcomes[0].school_id).toBe('school-123');
    expect(outcomes[0].total_students).toBe(2);
    expect(outcomes[0].avg_pre_score).toBeCloseTo(3.73, 2); // Average of the two assessments
  });
});
