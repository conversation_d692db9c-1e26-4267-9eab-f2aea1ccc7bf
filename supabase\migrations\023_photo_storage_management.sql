-- Photo Storage Management Migration
-- Migration 023: Comprehensive photo storage lifecycle management system

-- ============================================================================
-- PHOTO METADATA AND STORAGE TRACKING TABLES
-- ============================================================================

-- Photo metadata table for tracking all photos and their lifecycle
CREATE TABLE IF NOT EXISTS photo_metadata (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_path TEXT NOT NULL,
    bucket_name TEXT NOT NULL DEFAULT 'field-report-photos',
    original_filename TEXT NOT NULL,
    file_size_bytes BIGINT NOT NULL,
    compressed_size_bytes BIGINT,
    mime_type TEXT NOT NULL,
    width INTEGER,
    height INTEGER,
    storage_tier TEXT NOT NULL DEFAULT 'recent' CHECK (storage_tier IN ('recent', 'compressed', 'archived')),
    compression_ratio DECIMAL(5,2),
    
    -- Lifecycle tracking
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    compressed_at TIMESTAMP WITH TIME ZONE,
    archived_at TIMESTAMP WITH TIME ZONE,
    
    -- Relationships
    field_report_id UUID REFERENCES field_reports(id) ON DELETE SET NULL,
    distribution_id UUID REFERENCES book_distributions(id) ON DELETE SET NULL,
    uploaded_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    tags TEXT[],
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Storage usage tracking table
CREATE TABLE IF NOT EXISTS storage_usage_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bucket_name TEXT NOT NULL,
    total_size_bytes BIGINT NOT NULL,
    file_count INTEGER NOT NULL,
    recent_tier_size BIGINT DEFAULT 0,
    compressed_tier_size BIGINT DEFAULT 0,
    archived_tier_size BIGINT DEFAULT 0,
    utilization_percentage DECIMAL(5,2),
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Photo cleanup log for tracking cleanup operations
CREATE TABLE IF NOT EXISTS photo_cleanup_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    operation_type TEXT NOT NULL CHECK (operation_type IN ('compress', 'archive', 'delete')),
    photos_processed INTEGER NOT NULL,
    bytes_saved BIGINT NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status TEXT NOT NULL DEFAULT 'completed' CHECK (status IN ('running', 'completed', 'failed')),
    error_message TEXT,
    metadata JSONB DEFAULT '{}'
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_photo_metadata_bucket_tier ON photo_metadata(bucket_name, storage_tier);
CREATE INDEX IF NOT EXISTS idx_photo_metadata_uploaded_at ON photo_metadata(uploaded_at);
CREATE INDEX IF NOT EXISTS idx_photo_metadata_last_accessed ON photo_metadata(last_accessed_at);
CREATE INDEX IF NOT EXISTS idx_photo_metadata_field_report ON photo_metadata(field_report_id);
CREATE INDEX IF NOT EXISTS idx_photo_metadata_distribution ON photo_metadata(distribution_id);
CREATE INDEX IF NOT EXISTS idx_photo_metadata_size ON photo_metadata(file_size_bytes);

CREATE INDEX IF NOT EXISTS idx_storage_usage_log_recorded_at ON storage_usage_log(recorded_at DESC);
CREATE INDEX IF NOT EXISTS idx_storage_usage_log_bucket ON storage_usage_log(bucket_name, recorded_at DESC);

CREATE INDEX IF NOT EXISTS idx_photo_cleanup_log_started_at ON photo_cleanup_log(started_at DESC);
CREATE INDEX IF NOT EXISTS idx_photo_cleanup_log_operation ON photo_cleanup_log(operation_type, started_at DESC);

-- ============================================================================
-- STORAGE MONITORING FUNCTIONS
-- ============================================================================

-- Function to get current storage usage
CREATE OR REPLACE FUNCTION get_storage_usage(p_bucket_name TEXT DEFAULT 'field-report-photos')
RETURNS JSON AS $$
DECLARE
    result JSON;
    total_size BIGINT;
    file_count INTEGER;
    recent_size BIGINT;
    compressed_size BIGINT;
    archived_size BIGINT;
    max_size_bytes BIGINT := 500 * 1024 * 1024; -- 500MB limit
BEGIN
    -- Get current storage statistics
    SELECT 
        COALESCE(SUM(file_size_bytes), 0),
        COUNT(*),
        COALESCE(SUM(CASE WHEN storage_tier = 'recent' THEN file_size_bytes ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN storage_tier = 'compressed' THEN file_size_bytes ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN storage_tier = 'archived' THEN file_size_bytes ELSE 0 END), 0)
    INTO total_size, file_count, recent_size, compressed_size, archived_size
    FROM photo_metadata
    WHERE bucket_name = p_bucket_name;
    
    -- Build result JSON
    SELECT json_build_object(
        'bucketName', p_bucket_name,
        'totalSizeBytes', total_size,
        'totalSizeMB', ROUND(total_size / 1024.0 / 1024.0, 2),
        'fileCount', file_count,
        'maxSizeBytes', max_size_bytes,
        'maxSizeMB', ROUND(max_size_bytes / 1024.0 / 1024.0, 2),
        'utilizationPercentage', CASE 
            WHEN max_size_bytes > 0 THEN ROUND((total_size::DECIMAL / max_size_bytes) * 100, 2)
            ELSE 0 
        END,
        'remainingBytes', GREATEST(max_size_bytes - total_size, 0),
        'remainingMB', ROUND(GREATEST(max_size_bytes - total_size, 0) / 1024.0 / 1024.0, 2),
        'tierBreakdown', json_build_object(
            'recent', json_build_object(
                'sizeBytes', recent_size,
                'sizeMB', ROUND(recent_size / 1024.0 / 1024.0, 2),
                'percentage', CASE WHEN total_size > 0 THEN ROUND((recent_size::DECIMAL / total_size) * 100, 1) ELSE 0 END
            ),
            'compressed', json_build_object(
                'sizeBytes', compressed_size,
                'sizeMB', ROUND(compressed_size / 1024.0 / 1024.0, 2),
                'percentage', CASE WHEN total_size > 0 THEN ROUND((compressed_size::DECIMAL / total_size) * 100, 1) ELSE 0 END
            ),
            'archived', json_build_object(
                'sizeBytes', archived_size,
                'sizeMB', ROUND(archived_size / 1024.0 / 1024.0, 2),
                'percentage', CASE WHEN total_size > 0 THEN ROUND((archived_size::DECIMAL / total_size) * 100, 1) ELSE 0 END
            )
        ),
        'lastUpdated', EXTRACT(EPOCH FROM NOW())
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log storage usage
CREATE OR REPLACE FUNCTION log_storage_usage(p_bucket_name TEXT DEFAULT 'field-report-photos')
RETURNS VOID AS $$
DECLARE
    total_size BIGINT;
    file_count INTEGER;
    recent_size BIGINT;
    compressed_size BIGINT;
    archived_size BIGINT;
    max_size_bytes BIGINT := 500 * 1024 * 1024; -- 500MB limit
    utilization DECIMAL(5,2);
BEGIN
    -- Get current storage statistics
    SELECT 
        COALESCE(SUM(file_size_bytes), 0),
        COUNT(*),
        COALESCE(SUM(CASE WHEN storage_tier = 'recent' THEN file_size_bytes ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN storage_tier = 'compressed' THEN file_size_bytes ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN storage_tier = 'archived' THEN file_size_bytes ELSE 0 END), 0)
    INTO total_size, file_count, recent_size, compressed_size, archived_size
    FROM photo_metadata
    WHERE bucket_name = p_bucket_name;
    
    -- Calculate utilization percentage
    utilization := CASE 
        WHEN max_size_bytes > 0 THEN ROUND((total_size::DECIMAL / max_size_bytes) * 100, 2)
        ELSE 0 
    END;
    
    -- Insert log entry
    INSERT INTO storage_usage_log (
        bucket_name,
        total_size_bytes,
        file_count,
        recent_tier_size,
        compressed_tier_size,
        archived_tier_size,
        utilization_percentage
    ) VALUES (
        p_bucket_name,
        total_size,
        file_count,
        recent_size,
        compressed_size,
        archived_size,
        utilization
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get photos eligible for compression (30+ days old)
CREATE OR REPLACE FUNCTION get_photos_for_compression(p_bucket_name TEXT DEFAULT 'field-report-photos')
RETURNS JSON AS $$
DECLARE
    result JSON;
    cutoff_date TIMESTAMP WITH TIME ZONE := NOW() - INTERVAL '30 days';
BEGIN
    SELECT json_agg(
        json_build_object(
            'id', id,
            'filePath', file_path,
            'originalFilename', original_filename,
            'fileSizeBytes', file_size_bytes,
            'uploadedAt', uploaded_at,
            'lastAccessedAt', last_accessed_at,
            'fieldReportId', field_report_id,
            'distributionId', distribution_id
        )
    ) INTO result
    FROM photo_metadata
    WHERE bucket_name = p_bucket_name
      AND storage_tier = 'recent'
      AND uploaded_at < cutoff_date
    ORDER BY uploaded_at ASC;
    
    RETURN COALESCE(result, '[]'::json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get photos eligible for archiving (6+ months old)
CREATE OR REPLACE FUNCTION get_photos_for_archiving(p_bucket_name TEXT DEFAULT 'field-report-photos')
RETURNS JSON AS $$
DECLARE
    result JSON;
    cutoff_date TIMESTAMP WITH TIME ZONE := NOW() - INTERVAL '6 months';
BEGIN
    SELECT json_agg(
        json_build_object(
            'id', id,
            'filePath', file_path,
            'originalFilename', original_filename,
            'fileSizeBytes', file_size_bytes,
            'uploadedAt', uploaded_at,
            'lastAccessedAt', last_accessed_at,
            'storageTier', storage_tier
        )
    ) INTO result
    FROM photo_metadata
    WHERE bucket_name = p_bucket_name
      AND storage_tier IN ('recent', 'compressed')
      AND uploaded_at < cutoff_date
    ORDER BY uploaded_at ASC;
    
    RETURN COALESCE(result, '[]'::json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update photo metadata after compression/archiving
CREATE OR REPLACE FUNCTION update_photo_storage_tier(
    p_photo_id UUID,
    p_new_tier TEXT,
    p_new_file_path TEXT DEFAULT NULL,
    p_compressed_size BIGINT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE photo_metadata
    SET 
        storage_tier = p_new_tier,
        file_path = COALESCE(p_new_file_path, file_path),
        compressed_size_bytes = COALESCE(p_compressed_size, compressed_size_bytes),
        compression_ratio = CASE 
            WHEN p_compressed_size IS NOT NULL AND file_size_bytes > 0 
            THEN ROUND(((file_size_bytes - p_compressed_size)::DECIMAL / file_size_bytes) * 100, 2)
            ELSE compression_ratio
        END,
        compressed_at = CASE WHEN p_new_tier = 'compressed' THEN NOW() ELSE compressed_at END,
        archived_at = CASE WHEN p_new_tier = 'archived' THEN NOW() ELSE archived_at END,
        updated_at = NOW()
    WHERE id = p_photo_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if storage limit is approaching
CREATE OR REPLACE FUNCTION check_storage_limit_warning(
    p_bucket_name TEXT DEFAULT 'field-report-photos',
    p_warning_threshold DECIMAL DEFAULT 80.0
)
RETURNS JSON AS $$
DECLARE
    usage_info JSON;
    utilization DECIMAL;
    should_cleanup BOOLEAN := FALSE;
    warning_level TEXT := 'normal';
BEGIN
    -- Get current usage
    SELECT get_storage_usage(p_bucket_name) INTO usage_info;
    
    -- Extract utilization percentage
    utilization := (usage_info->>'utilizationPercentage')::DECIMAL;
    
    -- Determine warning level and cleanup recommendation
    IF utilization >= 95 THEN
        warning_level := 'critical';
        should_cleanup := TRUE;
    ELSIF utilization >= 90 THEN
        warning_level := 'high';
        should_cleanup := TRUE;
    ELSIF utilization >= p_warning_threshold THEN
        warning_level := 'medium';
        should_cleanup := TRUE;
    END IF;
    
    RETURN json_build_object(
        'bucketName', p_bucket_name,
        'utilizationPercentage', utilization,
        'warningLevel', warning_level,
        'shouldCleanup', should_cleanup,
        'thresholdExceeded', utilization >= p_warning_threshold,
        'message', CASE 
            WHEN utilization >= 95 THEN 'CRITICAL: Storage almost full! Immediate cleanup required.'
            WHEN utilization >= 90 THEN 'HIGH: Storage usage very high. Cleanup recommended.'
            WHEN utilization >= p_warning_threshold THEN 'MEDIUM: Storage usage approaching limit.'
            ELSE 'Storage usage is within normal limits.'
        END,
        'recommendedActions', CASE 
            WHEN utilization >= 95 THEN json_build_array('immediate_compression', 'emergency_archiving', 'block_new_uploads')
            WHEN utilization >= 90 THEN json_build_array('compress_old_photos', 'archive_oldest_photos')
            WHEN utilization >= p_warning_threshold THEN json_build_array('schedule_compression')
            ELSE json_build_array()
        END,
        'usageInfo', usage_info
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC LOGGING
-- ============================================================================

-- Trigger to update last_accessed_at when photo metadata is queried
CREATE OR REPLACE FUNCTION update_photo_last_accessed()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_accessed_at := NOW();
    NEW.updated_at := NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically log storage usage changes
CREATE OR REPLACE FUNCTION trigger_storage_usage_log()
RETURNS TRIGGER AS $$
BEGIN
    -- Log storage usage after any photo metadata change
    PERFORM log_storage_usage(COALESCE(NEW.bucket_name, OLD.bucket_name));
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers
DROP TRIGGER IF EXISTS photo_metadata_update_accessed ON photo_metadata;
CREATE TRIGGER photo_metadata_update_accessed
    BEFORE UPDATE ON photo_metadata
    FOR EACH ROW
    EXECUTE FUNCTION update_photo_last_accessed();

DROP TRIGGER IF EXISTS photo_metadata_log_usage ON photo_metadata;
CREATE TRIGGER photo_metadata_log_usage
    AFTER INSERT OR UPDATE OR DELETE ON photo_metadata
    FOR EACH ROW
    EXECUTE FUNCTION trigger_storage_usage_log();

-- ============================================================================
-- GRANT PERMISSIONS
-- ============================================================================

GRANT SELECT, INSERT, UPDATE ON photo_metadata TO authenticated;
GRANT SELECT ON storage_usage_log TO authenticated;
GRANT SELECT ON photo_cleanup_log TO authenticated;

GRANT EXECUTE ON FUNCTION get_storage_usage TO authenticated;
GRANT EXECUTE ON FUNCTION get_photos_for_compression TO authenticated;
GRANT EXECUTE ON FUNCTION get_photos_for_archiving TO authenticated;
GRANT EXECUTE ON FUNCTION check_storage_limit_warning TO authenticated;

-- Admin-only functions
GRANT EXECUTE ON FUNCTION log_storage_usage TO service_role;
GRANT EXECUTE ON FUNCTION update_photo_storage_tier TO service_role;
