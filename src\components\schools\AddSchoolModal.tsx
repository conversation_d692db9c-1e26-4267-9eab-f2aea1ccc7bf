import React, { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { School, Plus, AlertCircle, Loader2, MapPin, Users, ChevronDown, ChevronUp, Trash2, Calendar } from 'lucide-react';
import { useSchoolOperations } from '@/hooks/useSchoolOperations';
import { useAdminDivisions } from '@/hooks/useAdminDivisions';
import { Database } from '@/integrations/supabase/types';
import { toast } from 'sonner';
import FieldStaffSelector from './FieldStaffSelector';

interface AddSchoolModalProps {
  isOpen: boolean;
  onClose: () => void;
}

type SchoolType = Database['public']['Enums']['school_type'];
type OwnershipType = Database['public']['Enums']['ownership_type'];

interface ContactInfo {
  id: string;
  name: string;
  phone?: string;
  email?: string; // Made optional for champion teachers
}

interface SchoolFormData {
  // Required fields
  name: string;
  school_type: SchoolType;
  division_id: string; // District is required
  champion_teachers: ContactInfo[]; // At least 1 champion teacher required
  student_count: string; // Now required
  champion_teacher_count: string; // Now required

  // Optional fields
  code?: string; // Made optional
  school_category?: 'day' | 'boarding' | 'both';
  contact_phone?: string;
  email?: string;
  head_teacher_name?: string;
  head_teacher_phone?: string;
  head_teacher_email?: string; // Made optional
  deputy_head_teacher_name?: string;
  deputy_head_teacher_phone?: string;
  deputy_head_teacher_email?: string; // Made optional
  date_joined_ilead?: string; // Made optional
  ownership_type?: OwnershipType;
  location_coordinates?: string;
  is_partner_managed?: boolean;
  partner_name?: string;
  field_staff_id?: string;
  assistant_champion_teachers?: ContactInfo[]; // Made optional
}

const AddSchoolModal: React.FC<AddSchoolModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [formData, setFormData] = useState<SchoolFormData>({
    // Required fields
    name: '',
    school_type: 'primary',
    division_id: '',
    champion_teachers: [
      { id: '1', name: '', phone: '', email: '' } // Start with 1 required champion teacher
    ],

    // Required fields with defaults
    student_count: '',
    champion_teacher_count: '',

    // Optional fields with defaults
    code: '',
    school_category: 'day',
    contact_phone: '',
    email: '',
    head_teacher_name: '',
    head_teacher_phone: '',
    head_teacher_email: '',
    deputy_head_teacher_name: '',
    deputy_head_teacher_phone: '',
    deputy_head_teacher_email: '',
    date_joined_ilead: '',
    ownership_type: 'government',
    location_coordinates: '',
    is_partner_managed: false,
    partner_name: '',
    field_staff_id: '',
    assistant_champion_teachers: [], // Start empty, optional
  });

  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [showQuickTips, setShowQuickTips] = useState(false);

  const { addSchool, isAddingSchool } = useSchoolOperations(null);
  const { data: divisions = [] } = useAdminDivisions();

  // Helper functions for managing dynamic contacts
  const addContact = (type: 'champion_teachers' | 'assistant_champion_teachers') => {
    const newContact: ContactInfo = {
      id: Date.now().toString(),
      name: '',
      phone: '',
      email: ''
    };
    setFormData(prev => ({
      ...prev,
      [type]: [...prev[type], newContact]
    }));
  };

  const removeContact = (type: 'champion_teachers' | 'assistant_champion_teachers', id: string) => {
    setFormData(prev => {
      const contacts = prev[type] || [];
      // For champion teachers, ensure at least 1 remains
      if (type === 'champion_teachers' && contacts.length <= 1) {
        return prev; // Don't allow removal if only 1 champion teacher
      }
      return {
        ...prev,
        [type]: contacts.filter(contact => contact.id !== id)
      };
    });
  };

  const updateContact = (type: 'champion_teachers' | 'assistant_champion_teachers', id: string, field: keyof ContactInfo, value: string) => {
    setFormData(prev => {
      const contacts = prev[type] || [];
      return {
        ...prev,
        [type]: contacts.map(contact =>
          contact.id === id ? { ...contact, [field]: value } : contact
        )
      };
    });
  };

  const validateForm = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // Required field validation
    if (!formData.name.trim()) errors.push('School name is required');
    if (!formData.school_type) errors.push('School type is required');
    if (!formData.division_id) errors.push('District is required');
    if (!formData.student_count.trim()) errors.push('Student count is required');
    if (!formData.champion_teacher_count.trim()) errors.push('Champion teacher count is required');

    // Optional email validation (only if provided)
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.push('Please enter a valid school email address');
    }

    // Optional phone validation (only if provided)
    if (formData.contact_phone && !/^[\d\s\-+()]+$/.test(formData.contact_phone)) {
      errors.push('Please enter a valid phone number');
    }

    // Optional head teacher email validation (only if provided)
    if (formData.head_teacher_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.head_teacher_email)) {
      errors.push('Please enter a valid head teacher email address');
    }

    // Optional head teacher phone validation (only if provided)
    if (formData.head_teacher_phone && !/^[\d\s\-+()]+$/.test(formData.head_teacher_phone)) {
      errors.push('Please enter a valid head teacher phone number');
    }

    // Optional deputy head teacher email validation (only if provided)
    if (formData.deputy_head_teacher_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.deputy_head_teacher_email)) {
      errors.push('Please enter a valid deputy head teacher email address');
    }

    // Optional deputy head teacher phone validation (only if provided)
    if (formData.deputy_head_teacher_phone && !/^[\d\s\-+()]+$/.test(formData.deputy_head_teacher_phone)) {
      errors.push('Please enter a valid deputy head teacher phone number');
    }

    // Required numeric field validation
    if (isNaN(Number(formData.student_count)) || Number(formData.student_count) < 0) {
      errors.push('Student count must be a valid positive number');
    }

    if (isNaN(Number(formData.champion_teacher_count)) || Number(formData.champion_teacher_count) < 0) {
      errors.push('Champion teacher count must be a valid positive number');
    }

    // Partner management validation
    if (formData.is_partner_managed && !formData.partner_name.trim()) {
      errors.push('Partner name is required when school is partner managed');
    }

    // Champion teacher validation (at least 1 required with name)
    const validChampionTeachers = formData.champion_teachers.filter(ct => ct.name.trim());
    if (validChampionTeachers.length < 1) {
      errors.push('At least 1 champion teacher with a name is required');
    }

    // Champion teacher email validation (only if provided)
    formData.champion_teachers.forEach((ct, index) => {
      if (ct.email && ct.email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(ct.email)) {
        errors.push(`Champion teacher ${index + 1}: Please enter a valid email address`);
      }
    });

    // Assistant champion teacher email validation (only if provided)
    if (formData.assistant_champion_teachers) {
      formData.assistant_champion_teachers.forEach((act, index) => {
        if (act.email && act.email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(act.email)) {
          errors.push(`Assistant champion teacher ${index + 1}: Please enter a valid email address`);
        }
      });
    }

    return { isValid: errors.length === 0, errors };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validation = validateForm();
    if (!validation.isValid) {
      setValidationErrors(validation.errors);
      return;
    }

    try {
      const schoolData = {
        ...formData,
        // Convert required numeric fields
        student_count: parseInt(formData.student_count),
        champion_teacher_count: parseInt(formData.champion_teacher_count),

        // Convert optional string fields (empty strings to undefined)
        code: formData.code && formData.code.trim() ? formData.code.trim() : undefined,
        date_joined_ilead: formData.date_joined_ilead && formData.date_joined_ilead.trim() ? formData.date_joined_ilead : undefined,
        head_teacher_email: formData.head_teacher_email && formData.head_teacher_email.trim() ? formData.head_teacher_email.trim() : undefined,
        deputy_head_teacher_email: formData.deputy_head_teacher_email && formData.deputy_head_teacher_email.trim() ? formData.deputy_head_teacher_email.trim() : undefined,
        field_staff_id: formData.field_staff_id && formData.field_staff_id.trim() ? formData.field_staff_id : undefined,

        // Filter out empty contacts (keep only contacts with at least a name)
        champion_teachers: formData.champion_teachers.filter(ct => ct.name.trim()),
        assistant_champion_teachers: formData.assistant_champion_teachers.filter(act => act.name.trim()),
      };

      await addSchool(schoolData);
      
      // Reset form and close modal
      setFormData({
        // Required fields
        name: '',
        school_type: 'primary',
        division_id: '',
        champion_teachers: [
          { id: '1', name: '', phone: '', email: '' } // Start with 1 required champion teacher
        ],
        student_count: '',
        champion_teacher_count: '',

        // Optional fields with defaults
        code: '',
        school_category: 'day',
        contact_phone: '',
        email: '',
        head_teacher_name: '',
        head_teacher_phone: '',
        head_teacher_email: '',
        deputy_head_teacher_name: '',
        deputy_head_teacher_phone: '',
        deputy_head_teacher_email: '',
        date_joined_ilead: '',
        ownership_type: 'government',
        location_coordinates: '',
        is_partner_managed: false,
        partner_name: '',
        field_staff_id: '',
        assistant_champion_teachers: [], // Start empty, optional
      });
      setValidationErrors([]);
      onClose();
      toast.success('School registered successfully!');
    } catch (error) {
      console.error('Failed to add school:', error);
      toast.error('Failed to register school. Please try again.');
    }
  };

  const handleClose = () => {
    setValidationErrors([]);
    onClose();
  };

  const updateField = (field: keyof SchoolFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear validation errors when user starts typing
    if (validationErrors.length > 0) {
      setValidationErrors([]);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <School className="h-5 w-5 text-purple-600" />
            Register New School
          </DialogTitle>
          <DialogDescription>
            Add a new school to the iLEAD program with complete information.
          </DialogDescription>
        </DialogHeader>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">School Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => updateField('name', e.target.value)}
                    placeholder="e.g., Kampala Primary School"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="code">Centre Number (Optional)</Label>
                  <Input
                    id="code"
                    value={formData.code || ''}
                    onChange={(e) => updateField('code', e.target.value)}
                    placeholder="e.g., 12345"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="school_type">School Type *</Label>
                  <Select value={formData.school_type} onValueChange={(value: SchoolType) => updateField('school_type', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="primary">Primary School</SelectItem>
                      <SelectItem value="secondary">Secondary School</SelectItem>
                      <SelectItem value="tertiary">Tertiary School</SelectItem>
                      <SelectItem value="vocational">Vocational School</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="school_category">School Category</Label>
                  <Select value={formData.school_category || 'day'} onValueChange={(value: 'day' | 'boarding' | 'both') => updateField('school_category', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="day">Day</SelectItem>
                      <SelectItem value="boarding">Boarding</SelectItem>
                      <SelectItem value="both">Both</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="ownership_type">Ownership Type</Label>
                  <Select value={formData.ownership_type || 'government'} onValueChange={(value: OwnershipType) => updateField('ownership_type', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="government">Government</SelectItem>
                      <SelectItem value="private">Private</SelectItem>
                      <SelectItem value="community">Community</SelectItem>
                      <SelectItem value="religious">Religious</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="date_joined_ilead">Date Joined iLEAD (Optional)</Label>
                  <Input
                    id="date_joined_ilead"
                    type="date"
                    value={formData.date_joined_ilead || ''}
                    onChange={(e) => updateField('date_joined_ilead', e.target.value)}
                  />
                </div>
              </div>

              {/* Partner Management Section */}
              <div className="space-y-3 border-t pt-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_partner_managed"
                    checked={formData.is_partner_managed}
                    onCheckedChange={(checked) => updateField('is_partner_managed', checked as boolean)}
                  />
                  <Label htmlFor="is_partner_managed" className="text-sm font-medium">
                    Partner Managed School
                  </Label>
                </div>
                <p className="text-xs text-gray-500 ml-6">
                  Some schools are managed by other iLEAD partners
                </p>
                {formData.is_partner_managed && (
                  <div className="ml-6 space-y-2">
                    <Label htmlFor="partner_name">Partner Name *</Label>
                    <Input
                      id="partner_name"
                      value={formData.partner_name}
                      onChange={(e) => updateField('partner_name', e.target.value)}
                      placeholder="Enter partner organization name"
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Contact & Location */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Contact & Location</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="division_id">District *</Label>
                  <Select value={formData.division_id} onValueChange={(value) => updateField('division_id', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select district" />
                    </SelectTrigger>
                    <SelectContent>
                      {divisions.map((division) => (
                        <SelectItem key={division.id} value={division.id}>
                          {division.district} - {division.sub_county}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact_phone">Contact Phone (Optional)</Label>
                  <Input
                    id="contact_phone"
                    value={formData.contact_phone || ''}
                    onChange={(e) => updateField('contact_phone', e.target.value)}
                    placeholder="e.g., +256 700 123 456"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address (Optional)</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email || ''}
                    onChange={(e) => updateField('email', e.target.value)}
                    placeholder="e.g., <EMAIL>"
                  />
                </div>
                <div className="space-y-2">
                  <FieldStaffSelector
                    value={formData.field_staff_id}
                    onValueChange={(value) => updateField('field_staff_id', value)}
                    required={false}
                    placeholder="Select field staff member..."
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* School Leadership */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Users className="h-4 w-4" />
                School Leadership
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Head Teacher Section */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium text-gray-700 border-b pb-2">Head Teacher</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="head_teacher_name">Name (Optional)</Label>
                    <Input
                      id="head_teacher_name"
                      value={formData.head_teacher_name || ''}
                      onChange={(e) => updateField('head_teacher_name', e.target.value)}
                      placeholder="e.g., John Doe"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="head_teacher_phone">Phone Number (Optional)</Label>
                    <Input
                      id="head_teacher_phone"
                      value={formData.head_teacher_phone || ''}
                      onChange={(e) => updateField('head_teacher_phone', e.target.value)}
                      placeholder="e.g., +256 700 123 456"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="head_teacher_email">Email Address (Optional)</Label>
                    <Input
                      id="head_teacher_email"
                      type="email"
                      value={formData.head_teacher_email || ''}
                      onChange={(e) => updateField('head_teacher_email', e.target.value)}
                      placeholder="e.g., <EMAIL>"
                    />
                  </div>
                </div>
              </div>

              {/* Deputy Head Teacher Section */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium text-gray-700 border-b pb-2">Deputy Head Teacher</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="deputy_head_teacher_name">Name (Optional)</Label>
                    <Input
                      id="deputy_head_teacher_name"
                      value={formData.deputy_head_teacher_name || ''}
                      onChange={(e) => updateField('deputy_head_teacher_name', e.target.value)}
                      placeholder="e.g., Jane Smith"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="deputy_head_teacher_phone">Phone Number (Optional)</Label>
                    <Input
                      id="deputy_head_teacher_phone"
                      value={formData.deputy_head_teacher_phone || ''}
                      onChange={(e) => updateField('deputy_head_teacher_phone', e.target.value)}
                      placeholder="e.g., +256 700 123 456"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="deputy_head_teacher_email">Email Address (Optional)</Label>
                    <Input
                      id="deputy_head_teacher_email"
                      type="email"
                      value={formData.deputy_head_teacher_email || ''}
                      onChange={(e) => updateField('deputy_head_teacher_email', e.target.value)}
                      placeholder="e.g., <EMAIL>"
                    />
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="location_coordinates">
                  <MapPin className="h-4 w-4 inline mr-1" />
                  Location Coordinates
                </Label>
                <Input
                  id="location_coordinates"
                  value={formData.location_coordinates}
                  onChange={(e) => updateField('location_coordinates', e.target.value)}
                  placeholder="e.g., 0.3476, 32.5825 (Latitude, Longitude)"
                />
                <p className="text-xs text-gray-500">
                  Use Google Maps to find coordinates. Right-click on location and copy coordinates.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Additional Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Additional Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="student_count">Student Count *</Label>
                  <Input
                    id="student_count"
                    type="number"
                    value={formData.student_count}
                    onChange={(e) => updateField('student_count', e.target.value)}
                    placeholder="e.g., 500"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="champion_teacher_count">Champion Teacher Count *</Label>
                  <Input
                    id="champion_teacher_count"
                    type="number"
                    value={formData.champion_teacher_count}
                    onChange={(e) => updateField('champion_teacher_count', e.target.value)}
                    placeholder="e.g., 5"
                    required
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Champion Teachers Contact Details */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Users className="h-4 w-4" />
                Champion Teachers Contact Details (At least 1 required)
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.champion_teachers.map((teacher, index) => (
                <div key={teacher.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium">
                      Champion Teacher {index + 1} {index === 0 && <span className="text-red-500">*</span>}
                    </h4>
                    {formData.champion_teachers.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeContact('champion_teachers', teacher.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div className="space-y-2">
                      <Label htmlFor={`ct_name_${teacher.id}`}>
                        Name {index === 0 && <span className="text-red-500">*</span>}
                      </Label>
                      <Input
                        id={`ct_name_${teacher.id}`}
                        value={teacher.name}
                        onChange={(e) => updateContact('champion_teachers', teacher.id, 'name', e.target.value)}
                        placeholder="Full name"
                        required={index === 0}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`ct_phone_${teacher.id}`}>Phone (Optional)</Label>
                      <Input
                        id={`ct_phone_${teacher.id}`}
                        value={teacher.phone || ''}
                        onChange={(e) => updateContact('champion_teachers', teacher.id, 'phone', e.target.value)}
                        placeholder="+256 700 123 456"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`ct_email_${teacher.id}`}>Email (Optional)</Label>
                      <Input
                        id={`ct_email_${teacher.id}`}
                        type="email"
                        value={teacher.email || ''}
                        onChange={(e) => updateContact('champion_teachers', teacher.id, 'email', e.target.value)}
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                onClick={() => addContact('champion_teachers')}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Champion Teacher
              </Button>
            </CardContent>
          </Card>

          {/* Assistant Champion Teachers Contact Details */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Users className="h-4 w-4" />
                Assistant Champion Teachers Contact Details (Optional)
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.assistant_champion_teachers && formData.assistant_champion_teachers.length > 0 ? (
                formData.assistant_champion_teachers.map((teacher, index) => (
                  <div key={teacher.id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium">Assistant Champion Teacher {index + 1}</h4>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeContact('assistant_champion_teachers', teacher.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      <div className="space-y-2">
                        <Label htmlFor={`act_name_${teacher.id}`}>Name (Optional)</Label>
                        <Input
                          id={`act_name_${teacher.id}`}
                          value={teacher.name}
                          onChange={(e) => updateContact('assistant_champion_teachers', teacher.id, 'name', e.target.value)}
                          placeholder="Full name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor={`act_phone_${teacher.id}`}>Phone (Optional)</Label>
                        <Input
                          id={`act_phone_${teacher.id}`}
                          value={teacher.phone || ''}
                          onChange={(e) => updateContact('assistant_champion_teachers', teacher.id, 'phone', e.target.value)}
                          placeholder="+256 700 123 456"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor={`act_email_${teacher.id}`}>Email (Optional)</Label>
                        <Input
                          id={`act_email_${teacher.id}`}
                          type="email"
                          value={teacher.email || ''}
                          onChange={(e) => updateContact('assistant_champion_teachers', teacher.id, 'email', e.target.value)}
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500 text-center py-4">
                  No assistant champion teachers added yet. Click the button below to add one.
                </p>
              )}
              <Button
                type="button"
                variant="outline"
                onClick={() => addContact('assistant_champion_teachers')}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Assistant Champion Teacher (Optional)
              </Button>
            </CardContent>
          </Card>

          {/* Quick Tips Section */}
          <Card>
            <CardHeader className="pb-3">
              <Collapsible open={showQuickTips} onOpenChange={setShowQuickTips}>
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                    <CardTitle className="text-sm">Quick Tips</CardTitle>
                    {showQuickTips ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <CardContent className="pt-4 space-y-3">
                    <div className="text-sm text-gray-600 space-y-2">
                      <p><strong>School Categories:</strong> Day schools operate during daytime only, Boarding schools provide accommodation, Both indicates mixed operations.</p>
                      <p><strong>Coordinates:</strong> Right-click on Google Maps at the school location and copy the coordinates (latitude, longitude).</p>
                      <p><strong>Champion Teachers:</strong> These are key teachers who lead iLEAD programs at the school. Ensure accurate contact details for program coordination.</p>
                      <p><strong>Partner Schools:</strong> Some schools are managed by other iLEAD partner organizations. Check this option and specify the partner name if applicable.</p>
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </CardHeader>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-purple-600 hover:bg-purple-700"
              disabled={isAddingSchool}
            >
              {isAddingSchool ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Registering...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  Register School
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddSchoolModal;
