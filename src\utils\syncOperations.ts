/**
 * Sync operations for offline data
 * Handles the main sync logic, retry mechanisms, and batch processing
 */

import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { FieldOperationError, <PERSON>rror<PERSON>andler, exponentialBackoff } from '@/utils/errorHandling';
import { 
  OfflineData, 
  SyncResult, 
  BatchSyncOptions,
  DEFAULT_CONFIG,
  SYNC_TYPES 
} from '@/types/offlineSync.types';
import { 
  loadOfflineData, 
  saveOfflineData, 
  updateOfflineQueueItem,
  removeFromOfflineQueue 
} from '@/utils/offlineStorage';
import { 
  detectConflicts, 
  createConflict, 
  addConflict 
} from '@/utils/conflictResolution';

/**
 * Sync a single item to the server
 */
export const syncSingleItem = async (item: OfflineData): Promise<{ success: boolean; error?: string }> => {
  try {
    let result;
    
    switch (item.type) {
      case SYNC_TYPES.CHECK_IN:
        result = await syncCheckIn(item);
        break;
      case SYNC_TYPES.CHECK_OUT:
        result = await syncCheckOut(item);
        break;
      case SYNC_TYPES.FIELD_REPORT:
        result = await syncFieldReport(item);
        break;
      case SYNC_TYPES.PHOTO_UPLOAD:
        result = await syncPhotoUpload(item);
        break;
      default:
        throw new Error(`Unknown sync type: ${item.type}`);
    }
    
    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown sync error';
    console.error(`Failed to sync item ${item.id}:`, error);
    return { success: false, error: errorMessage };
  }
};

/**
 * Sync check-in data
 */
const syncCheckIn = async (item: OfflineData) => {
  // Use the field_staff_checkin RPC function instead of direct table insert
  const { data, error } = await supabase.rpc('field_staff_checkin', {
    p_latitude: item.data.latitude,
    p_longitude: item.data.longitude,
    p_school_id: item.data.school_id || null,
    p_office_id: item.data.office_id || null,
    p_check_in_type: item.data.check_in_type || 'school',
    p_accuracy: item.data.accuracy,
    p_address: item.data.address,
    p_verification_method: item.data.verification_method || 'gps',
    p_device_info: item.data.device_info || {},
    p_network_info: item.data.network_info || {},
    p_offline_sync: true
  });

  if (error) {
    throw new FieldOperationError('Failed to sync check-in', 'SYNC_ERROR', {
      originalError: error,
      itemId: item.id
    });
  }

  return data;
};

/**
 * Sync check-out data
 */
const syncCheckOut = async (item: OfflineData) => {
  // Use the field_staff_checkout RPC function instead of direct table update
  const { data, error } = await supabase.rpc('field_staff_checkout', {
    p_attendance_id: item.data.attendance_id,
    p_latitude: item.data.latitude || null,
    p_longitude: item.data.longitude || null,
    p_accuracy: item.data.accuracy || null,
    p_address: item.data.address || null,
    p_notes: item.data.notes || null,
    p_activity_type: item.data.activity_type,
    p_round_table_sessions: item.data.round_table_sessions || 0,
    p_total_students: item.data.total_students || 0,
    p_students_per_session: item.data.students_per_session || 8,
    p_activities_conducted: item.data.activities_conducted || [],
    p_topics_covered: item.data.topics_covered || [],
    p_challenges: item.data.challenges || null,
    p_wins: item.data.wins || null,
    p_observations: item.data.observations || null,
    p_lessons_learned: item.data.lessons_learned || null,
    p_follow_up_required: item.data.follow_up_required || false,
    p_follow_up_actions: item.data.follow_up_actions || null,
    p_photos: item.data.photos || [],
    p_offline_sync: true
  });

  if (error) {
    throw new FieldOperationError('Failed to sync check-out', 'SYNC_ERROR', {
      originalError: error,
      itemId: item.id
    });
  }

  return data;
};

/**
 * Sync field report data
 */
const syncFieldReport = async (item: OfflineData) => {
  const { data, error } = await supabase
    .from('field_reports')
    .insert(item.data)
    .select()
    .single();
    
  if (error) {
    throw new FieldOperationError('Failed to sync field report', 'SYNC_ERROR', {
      originalError: error,
      itemId: item.id
    });
  }
  
  return data;
};

/**
 * Sync photo upload
 */
const syncPhotoUpload = async (item: OfflineData) => {
  const { photo_data, file_name, ...metadata } = item.data as {
    photo_data: string | Blob;
    file_name: string;
    [key: string]: unknown;
  };
  
  if (!photo_data || !file_name) {
    throw new FieldOperationError('Invalid photo upload data', 'SYNC_ERROR', {
      itemId: item.id
    });
  }
  
  // Convert base64 to blob if needed (async to avoid blocking)
  let blob: Blob;
  if (typeof photo_data === 'string') {
    // Use async processing to avoid blocking the main thread
    blob = await new Promise((resolve) => {
      requestIdleCallback(() => {
        const base64Data = photo_data.split(',')[1];
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        resolve(new Blob([new Uint8Array(byteNumbers)], { type: 'image/jpeg' }));
      }, { timeout: 1000 });
    });
  } else {
    blob = item.blobData || photo_data;
  }
  
  // Upload to Supabase storage with fallback
  let uploadResult = await supabase.storage
    .from('field-photos')
    .upload(file_name, blob);

  let uploadError = uploadResult.error;
  let uploadData = uploadResult.data;

  // If field-photos bucket doesn't exist, fallback to general-files bucket
  if (uploadError && uploadError.message?.includes('Bucket not found')) {
    console.warn('field-photos bucket not found, falling back to general-files bucket');
    uploadResult = await supabase.storage
      .from('general-files')
      .upload(file_name, blob);

    uploadError = uploadResult.error;
    uploadData = uploadResult.data;
  }

  if (uploadError) {
    throw new FieldOperationError('Failed to upload photo', 'SYNC_ERROR', {
      originalError: uploadError,
      itemId: item.id
    });
  }
  
  // Save metadata to database
  const { data, error } = await supabase
    .from('field_photos')
    .insert({
      ...metadata,
      file_path: uploadData.path,
      file_size: blob.size
    })
    .select()
    .single();
    
  if (error) {
    throw new FieldOperationError('Failed to save photo metadata', 'SYNC_ERROR', {
      originalError: error,
      itemId: item.id
    });
  }
  
  return data;
};

/**
 * Batch sync multiple items with concurrency control
 */
export const batchSyncItems = async (
  items: OfflineData[],
  options: BatchSyncOptions = {}
): Promise<SyncResult> => {
  const {
    batchSize = DEFAULT_CONFIG.syncBatchSize,
    maxConcurrent = DEFAULT_CONFIG.maxConcurrentUploads,
    retryFailedItems = true,
    prioritizeHighPriority = true
  } = options;
  
  let processed = 0;
  let failed = 0;
  let conflicts = 0;
  const errors: string[] = [];
  
  // Sort items by priority if requested
  const sortedItems = prioritizeHighPriority 
    ? [...items].sort((a, b) => {
        const priorityOrder = { CRITICAL: 4, HIGH: 3, MEDIUM: 2, LOW: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      })
    : items;
  
  // Process items in batches
  for (let batchStart = 0; batchStart < sortedItems.length; batchStart += batchSize) {
    const batch = sortedItems.slice(batchStart, batchStart + batchSize);
    
    const batchPromises = batch.map(async (item) => {
      try {
        // Apply exponential backoff for retries
        const result = await exponentialBackoff(
          () => syncSingleItem(item),
          item.retryCount,
          DEFAULT_CONFIG.retryDelayMs
        );
        
        if (result.success) {
          // Remove successfully synced item
          removeFromOfflineQueue(item.id);
          processed++;
        } else {
          // Update retry count
          const newRetryCount = item.retryCount + 1;
          if (newRetryCount >= item.maxRetries) {
            errors.push(`Max retries exceeded for item ${item.id}: ${result.error}`);
            failed++;
          } else if (retryFailedItems) {
            updateOfflineQueueItem(item.id, { retryCount: newRetryCount });
          }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        
        if (error instanceof FieldOperationError && error.code === 'SYNC_CONFLICT') {
          conflicts++;
        } else {
          errors.push(`Failed to sync item ${item.id}: ${errorMessage}`);
          failed++;
        }
      }
    });
    
    // Wait for all batch items to complete
    await Promise.all(batchPromises);
  }
  
  return {
    success: failed === 0 && conflicts === 0,
    processed,
    failed,
    conflicts,
    errors
  };
};

/**
 * Sync all offline data
 */
export const syncAllOfflineData = async (options?: BatchSyncOptions): Promise<SyncResult> => {
  const offlineData = loadOfflineData();
  
  if (offlineData.length === 0) {
    return {
      success: true,
      processed: 0,
      failed: 0,
      conflicts: 0,
      errors: []
    };
  }
  
  return batchSyncItems(offlineData, options);
};

/**
 * Retry failed sync items
 */
export const retryFailedItems = async (): Promise<SyncResult> => {
  const offlineData = loadOfflineData();
  const failedItems = offlineData.filter(item => item.retryCount > 0 && item.retryCount < item.maxRetries);
  
  if (failedItems.length === 0) {
    return {
      success: true,
      processed: 0,
      failed: 0,
      conflicts: 0,
      errors: []
    };
  }
  
  return batchSyncItems(failedItems, { retryFailedItems: true });
};

/**
 * Check if device is online
 */
export const checkOnlineStatus = (): boolean => {
  return navigator.onLine;
};

/**
 * Test server connectivity
 */
export const testServerConnectivity = async (): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('attendance_sessions')
      .select('id')
      .limit(1);
      
    return !error;
  } catch (error) {
    console.error('Server connectivity test failed:', error);
    return false;
  }
};
