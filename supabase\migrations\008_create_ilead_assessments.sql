-- Create iLead Assessments Table
-- This migration creates the table structure for iLead leadership assessments

-- Create iLead assessments table
CREATE TABLE ilead_assessments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    school_id UUID REFERENCES schools(id) NOT NULL,
    assessment_type assessment_type NOT NULL,
    assessment_date DATE NOT NULL,
    academic_year VARCHAR(10) NOT NULL,
    assessor_name VARCHAR(100),
    notes TEXT,
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Participant Information
    participant_full_name VARCHAR(255) NOT NULL,
    participant_age INTEGER,
    participant_gender VARCHAR(10) CHECK (participant_gender IN ('male', 'female')),
    participant_learning_goal TEXT,
    
    -- Leadership Assessment Scores (1-5 scale)
    consider_myself_leader INTEGER CHECK (consider_myself_leader <PERSON>ET<PERSON>EE<PERSON> 1 AND 5) NOT NULL,
    strive_to_finish_started INTEGER CHECK (strive_to_finish_started BETWEEN 1 AND 5) NOT NULL,
    understand_importance_decisions INTEGER CHECK (understand_importance_decisions BETWEEN 1 AND 5) NOT NULL,
    take_full_responsibility INTEGER CHECK (take_full_responsibility BETWEEN 1 AND 5) NOT NULL,
    understand_leader_values INTEGER CHECK (understand_leader_values BETWEEN 1 AND 5) NOT NULL,
    strive_push_limits INTEGER CHECK (strive_push_limits BETWEEN 1 AND 5) NOT NULL,
    value_people_around INTEGER CHECK (value_people_around BETWEEN 1 AND 5) NOT NULL,
    good_example_to_others INTEGER CHECK (good_example_to_others BETWEEN 1 AND 5) NOT NULL,
    responsible_with_homework INTEGER CHECK (responsible_with_homework BETWEEN 1 AND 5) NOT NULL,
    responsible_with_housework INTEGER CHECK (responsible_with_housework BETWEEN 1 AND 5) NOT NULL,
    confident_in_myself INTEGER CHECK (confident_in_myself BETWEEN 1 AND 5) NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_ilead_assessments_school_id ON ilead_assessments(school_id);
CREATE INDEX idx_ilead_assessments_date ON ilead_assessments(assessment_date);
CREATE INDEX idx_ilead_assessments_type ON ilead_assessments(assessment_type);
CREATE INDEX idx_ilead_assessments_participant ON ilead_assessments(participant_full_name);

-- Create trigger for updated_at column
CREATE TRIGGER update_ilead_assessments_updated_at
    BEFORE UPDATE ON ilead_assessments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE ilead_assessments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for iLead Assessments
CREATE POLICY "Users can view assessments from their schools" ON ilead_assessments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        created_by = auth.uid() OR
        school_id IN (
            SELECT school_id FROM tasks
            WHERE assigned_to = auth.uid() OR created_by = auth.uid()
        )
    );

CREATE POLICY "Users can insert assessments" ON ilead_assessments
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );

CREATE POLICY "Users can update their assessments" ON ilead_assessments
    FOR UPDATE USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- Create function to get iLead assessment outcomes
CREATE OR REPLACE FUNCTION get_ilead_assessment_outcomes(
    p_school_id UUID DEFAULT NULL,
    p_academic_year VARCHAR DEFAULT NULL
)
RETURNS TABLE (
    school_id UUID,
    school_name VARCHAR,
    total_participants INTEGER,
    avg_leadership_score DECIMAL,
    assessment_type assessment_type,
    assessment_count INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.id as school_id,
        s.name as school_name,
        COUNT(DISTINCT ia.id) as total_participants,
        ROUND(AVG((
            ia.consider_myself_leader + 
            ia.strive_to_finish_started + 
            ia.understand_importance_decisions + 
            ia.take_full_responsibility + 
            ia.understand_leader_values + 
            ia.strive_push_limits + 
            ia.value_people_around + 
            ia.good_example_to_others + 
            ia.responsible_with_homework + 
            ia.responsible_with_housework + 
            ia.confident_in_myself
        )::DECIMAL / 11), 2) as avg_leadership_score,
        ia.assessment_type,
        COUNT(ia.id) as assessment_count
    FROM ilead_assessments ia
    JOIN schools s ON ia.school_id = s.id
    WHERE 
        (p_school_id IS NULL OR ia.school_id = p_school_id) AND
        (p_academic_year IS NULL OR ia.academic_year = p_academic_year)
    GROUP BY s.id, s.name, ia.assessment_type
    ORDER BY s.name, ia.assessment_type;
END;
$$;
