-- Office Check-in Support
-- Migration 023: Add office check-in functionality to existing attendance system

-- Create check-in type enum
CREATE TYPE check_in_type AS ENUM ('school', 'office');

-- Create office locations table
CREATE TABLE office_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    location_coordinates POINT NOT NULL,
    geofence_radius_meters INTEGER DEFAULT 1000,
    address TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add office check-in support to field_staff_attendance
ALTER TABLE field_staff_attendance 
ADD COLUMN check_in_type check_in_type DEFAULT 'school',
ADD COLUMN office_id UUID REFERENCES office_locations(id);

-- Make school_id optional for office check-ins
ALTER TABLE field_staff_attendance 
ALTER COLUMN school_id DROP NOT NULL;

-- Add constraint to ensure either school_id or office_id is provided
ALTER TABLE field_staff_attendance 
ADD CONSTRAINT check_school_or_office_provided 
CHECK (
    (check_in_type = 'school' AND school_id IS NOT NULL AND office_id IS NULL) OR
    (check_in_type = 'office' AND office_id IS NOT NULL AND school_id IS NULL)
);

-- Insert main office location (coordinates: 0.32581475334630144, 32.57564046659227)
INSERT INTO office_locations (
    name, 
    location_coordinates, 
    geofence_radius_meters, 
    address, 
    description
) VALUES (
    'Main Office',
    POINT(32.57564046659227, 0.32581475334630144),
    1000,
    'iLead Main Office',
    'Primary office location for administrative staff and field staff office days'
);

-- Create indexes for performance
CREATE INDEX idx_field_staff_attendance_check_in_type ON field_staff_attendance(check_in_type);
CREATE INDEX idx_field_staff_attendance_office_id ON field_staff_attendance(office_id);
CREATE INDEX idx_office_locations_active ON office_locations(is_active);
CREATE INDEX idx_office_locations_coordinates ON office_locations USING GIST(location_coordinates);

-- Update existing records to have 'school' check-in type
UPDATE field_staff_attendance 
SET check_in_type = 'school' 
WHERE check_in_type IS NULL;

-- Create function to get office locations
CREATE OR REPLACE FUNCTION get_office_locations()
RETURNS TABLE (
    id UUID,
    name VARCHAR(255),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    geofence_radius_meters INTEGER,
    address TEXT,
    description TEXT,
    is_active BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ol.id,
        ol.name,
        ST_Y(ol.location_coordinates)::DECIMAL(10,8) as latitude,
        ST_X(ol.location_coordinates)::DECIMAL(11,8) as longitude,
        ol.geofence_radius_meters,
        ol.address,
        ol.description,
        ol.is_active
    FROM office_locations ol
    WHERE ol.is_active = true
    ORDER BY ol.name;
END;
$$;

-- Grant permissions
GRANT SELECT ON office_locations TO authenticated;
GRANT EXECUTE ON FUNCTION get_office_locations() TO authenticated;

-- Add helpful comments
COMMENT ON TABLE office_locations IS 'Office locations for staff check-in with geofencing support';
COMMENT ON COLUMN field_staff_attendance.check_in_type IS 'Type of check-in: school visit or office attendance';
COMMENT ON COLUMN field_staff_attendance.office_id IS 'Reference to office location for office check-ins';
COMMENT ON CONSTRAINT check_school_or_office_provided ON field_staff_attendance IS 'Ensures either school_id or office_id is provided based on check_in_type';
