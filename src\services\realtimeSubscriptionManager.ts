/**
 * Real-time Subscription Manager
 * Centralized management of Supabase real-time subscriptions with connection pooling
 */

import { RealtimeChannel, RealtimeClient } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// ============================================================================
// TYPES
// ============================================================================

export interface SubscriptionConfig {
  table: string;
  filter?: string;
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  schema?: string;
  priority: 'high' | 'medium' | 'low';
}

export interface SubscriptionCallback {
  id: string;
  callback: (payload: Record<string, unknown>) => void;
  filter?: (payload: Record<string, unknown>) => boolean;
}

export interface SubscriptionHealth {
  connectionId: string;
  isConnected: boolean;
  lastHeartbeat: number;
  subscriptionCount: number;
  errorCount: number;
  latency: number;
}

export interface ConnectionPoolStats {
  totalConnections: number;
  activeConnections: number;
  totalSubscriptions: number;
  averageLatency: number;
  errorRate: number;
}

// ============================================================================
// SUBSCRIPTION MANAGER CLASS
// ============================================================================

export class RealtimeSubscriptionManager {
  private static instance: RealtimeSubscriptionManager;
  private subscriptions: Map<string, RealtimeChannel> = new Map();
  private callbacks: Map<string, SubscriptionCallback[]> = new Map();
  private connectionPool: Map<string, RealtimeClient> = new Map();
  private healthStats: Map<string, SubscriptionHealth> = new Map();
  private isInitialized = false;
  private maxConnections = 3;
  private heartbeatInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.initializeConnectionPool();
    this.startHealthMonitoring();
  }

  public static getInstance(): RealtimeSubscriptionManager {
    if (!RealtimeSubscriptionManager.instance) {
      RealtimeSubscriptionManager.instance = new RealtimeSubscriptionManager();
    }
    return RealtimeSubscriptionManager.instance;
  }

  // ============================================================================
  // CONNECTION POOL MANAGEMENT
  // ============================================================================

  private initializeConnectionPool(): void {
    try {
      // Create primary connection for high-priority subscriptions
      const primaryConnection = supabase.realtime;
      this.connectionPool.set('primary', primaryConnection);
      
      // Initialize health stats
      this.healthStats.set('primary', {
        connectionId: 'primary',
        isConnected: false,
        lastHeartbeat: Date.now(),
        subscriptionCount: 0,
        errorCount: 0,
        latency: 0,
      });

      this.isInitialized = true;
      console.log('📡 Real-time subscription manager initialized');
    } catch (error) {
      console.error('Failed to initialize subscription manager:', error);
      toast.error('Real-time features may not work properly');
    }
  }

  private getOptimalConnection(priority: 'high' | 'medium' | 'low'): RealtimeClient {
    // For now, use the primary connection for all priorities
    // In the future, we can implement load balancing across multiple connections
    const connection = this.connectionPool.get('primary');
    if (!connection) {
      throw new Error('No available connections in pool');
    }
    return connection;
  }

  // ============================================================================
  // SUBSCRIPTION MANAGEMENT
  // ============================================================================

  public subscribe(
    config: SubscriptionConfig,
    callback: (payload: Record<string, unknown>) => void,
    callbackId?: string
  ): string {
    const subscriptionKey = this.generateSubscriptionKey(config);
    const callbackKey = callbackId || `callback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // Check if subscription already exists
      let channel = this.subscriptions.get(subscriptionKey);
      
      if (!channel) {
        // Create new subscription
        channel = this.createSubscription(config);
        this.subscriptions.set(subscriptionKey, channel);
      }

      // Add callback to subscription
      if (!this.callbacks.has(subscriptionKey)) {
        this.callbacks.set(subscriptionKey, []);
      }

      const callbackConfig: SubscriptionCallback = {
        id: callbackKey,
        callback,
      };

      this.callbacks.get(subscriptionKey)!.push(callbackConfig);

      // Update health stats
      const health = this.healthStats.get('primary');
      if (health) {
        health.subscriptionCount++;
      }

      console.log(`📡 Subscribed to ${subscriptionKey} with callback ${callbackKey}`);
      return callbackKey;

    } catch (error) {
      console.error(`Failed to subscribe to ${subscriptionKey}:`, error);
      toast.error('Failed to establish real-time connection');
      throw error;
    }
  }

  public unsubscribe(callbackId: string): void {
    try {
      // Find and remove the callback
      for (const [subscriptionKey, callbacks] of this.callbacks.entries()) {
        const callbackIndex = callbacks.findIndex(cb => cb.id === callbackId);
        
        if (callbackIndex !== -1) {
          callbacks.splice(callbackIndex, 1);
          
          // If no more callbacks, remove the subscription
          if (callbacks.length === 0) {
            const channel = this.subscriptions.get(subscriptionKey);
            if (channel) {
              channel.unsubscribe();
              this.subscriptions.delete(subscriptionKey);
              this.callbacks.delete(subscriptionKey);
              
              // Update health stats
              const health = this.healthStats.get('primary');
              if (health) {
                health.subscriptionCount--;
              }
              
              console.log(`📡 Unsubscribed from ${subscriptionKey}`);
            }
          }
          
          console.log(`📡 Removed callback ${callbackId}`);
          return;
        }
      }
      
      console.warn(`Callback ${callbackId} not found for unsubscription`);
    } catch (error) {
      console.error(`Failed to unsubscribe callback ${callbackId}:`, error);
    }
  }

  private createSubscription(config: SubscriptionConfig): RealtimeChannel {
    const connection = this.getOptimalConnection(config.priority);
    
    // Create channel name
    const channelName = `${config.schema || 'public'}:${config.table}${config.filter ? `:${config.filter}` : ''}`;
    
    const channel = connection.channel(channelName);
    
    // Set up event listener
    channel.on(
      'postgres_changes',
      {
        event: config.event || '*',
        schema: config.schema || 'public',
        table: config.table,
        filter: config.filter,
      },
      (payload: Record<string, unknown>) => {
        this.handleSubscriptionEvent(this.generateSubscriptionKey(config), payload);
      }
    );

    // Handle subscription status
    channel.on('system', {}, (payload: Record<string, unknown>) => {
      if (payload.status === 'ok') {
        console.log(`📡 Subscription established: ${channelName}`);
      } else if (payload.status === 'error') {
        console.error(`📡 Subscription error: ${channelName}`, payload);
        this.handleSubscriptionError(this.generateSubscriptionKey(config), payload);
      }
    });

    // Subscribe to the channel
    channel.subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        console.log(`📡 Successfully subscribed to ${channelName}`);
        this.updateConnectionHealth('primary', true);
      } else if (status === 'CHANNEL_ERROR') {
        console.error(`📡 Channel error for ${channelName}`);
        this.updateConnectionHealth('primary', false);
      }
    });

    return channel;
  }

  private handleSubscriptionEvent(subscriptionKey: string, payload: Record<string, unknown>): void {
    const callbacks = this.callbacks.get(subscriptionKey);
    if (!callbacks) return;

    // Execute all callbacks for this subscription
    callbacks.forEach(callbackConfig => {
      try {
        // Apply filter if specified
        if (callbackConfig.filter && !callbackConfig.filter(payload)) {
          return;
        }
        
        callbackConfig.callback(payload);
      } catch (error) {
        console.error(`Error in subscription callback ${callbackConfig.id}:`, error);
        
        // Update error count
        const health = this.healthStats.get('primary');
        if (health) {
          health.errorCount++;
        }
      }
    });
  }

  private handleSubscriptionError(subscriptionKey: string, error: Record<string, unknown>): void {
    console.error(`Subscription error for ${subscriptionKey}:`, error);
    
    // Update error count
    const health = this.healthStats.get('primary');
    if (health) {
      health.errorCount++;
    }

    // Attempt to reconnect after a delay
    setTimeout(() => {
      this.attemptReconnection(subscriptionKey);
    }, 5000);
  }

  private attemptReconnection(subscriptionKey: string): void {
    try {
      const channel = this.subscriptions.get(subscriptionKey);
      if (channel) {
        console.log(`📡 Attempting to reconnect subscription: ${subscriptionKey}`);
        // Supabase handles reconnection automatically, but we can trigger it manually if needed
        channel.subscribe();
      }
    } catch (error) {
      console.error(`Failed to reconnect subscription ${subscriptionKey}:`, error);
    }
  }

  // ============================================================================
  // HEALTH MONITORING
  // ============================================================================

  private startHealthMonitoring(): void {
    this.heartbeatInterval = setInterval(() => {
      this.performHealthCheck();
      this.performAutomaticCleanup();
    }, 30000); // Check every 30 seconds
  }

  private performHealthCheck(): void {
    for (const [connectionId, health] of this.healthStats.entries()) {
      const connection = this.connectionPool.get(connectionId);
      if (connection) {
        // Update heartbeat
        health.lastHeartbeat = Date.now();
        
        // Check connection status (simplified)
        health.isConnected = connection.isConnected();
        
        // Calculate latency (simplified - would need actual ping/pong)
        health.latency = Date.now() % 100; // Simplified placeholder
      }
    }
  }

  private updateConnectionHealth(connectionId: string, isConnected: boolean): void {
    const health = this.healthStats.get(connectionId);
    if (health) {
      health.isConnected = isConnected;
      health.lastHeartbeat = Date.now();
    }
  }

  /**
   * Automatic cleanup of unused subscriptions and stale connections
   */
  private performAutomaticCleanup(): void {
    const now = Date.now();
    const staleThreshold = 5 * 60 * 1000; // 5 minutes
    const inactiveThreshold = 30 * 60 * 1000; // 30 minutes

    // Clean up stale subscriptions (no callbacks for 5+ minutes)
    for (const [subscriptionKey, callbacks] of this.callbacks.entries()) {
      if (callbacks.length === 0) {
        const channel = this.subscriptions.get(subscriptionKey);
        if (channel) {
          console.log(`🧹 Cleaning up empty subscription: ${subscriptionKey}`);
          channel.unsubscribe();
          this.subscriptions.delete(subscriptionKey);
          this.callbacks.delete(subscriptionKey);
        }
      }
    }

    // Clean up inactive connections (no heartbeat for 30+ minutes)
    for (const [connectionId, health] of this.healthStats.entries()) {
      const timeSinceHeartbeat = now - health.lastHeartbeat;

      if (timeSinceHeartbeat > inactiveThreshold && !health.isConnected) {
        console.log(`🧹 Cleaning up inactive connection: ${connectionId}`);

        // Remove all subscriptions for this connection
        const subscriptionsToRemove: string[] = [];
        for (const [subscriptionKey] of this.subscriptions.entries()) {
          // For now, all subscriptions use the primary connection
          if (connectionId === 'primary') {
            subscriptionsToRemove.push(subscriptionKey);
          }
        }

        subscriptionsToRemove.forEach(key => {
          const channel = this.subscriptions.get(key);
          if (channel) {
            channel.unsubscribe();
            this.subscriptions.delete(key);
            this.callbacks.delete(key);
          }
        });

        // Reset health stats
        health.subscriptionCount = 0;
        health.errorCount = 0;
        health.lastHeartbeat = now;
      }
    }

    // Log cleanup statistics
    const totalSubscriptions = this.subscriptions.size;
    const totalCallbacks = Array.from(this.callbacks.values()).reduce((sum, callbacks) => sum + callbacks.length, 0);

    if (totalSubscriptions > 0) {
      console.log(`📊 Subscription cleanup complete: ${totalSubscriptions} active subscriptions, ${totalCallbacks} total callbacks`);
    }
  }

  /**
   * Force cleanup of all subscriptions (for testing or emergency cleanup)
   */
  public forceCleanup(): void {
    console.log('🚨 Force cleaning up all subscriptions');

    // Unsubscribe from all channels
    for (const [key, channel] of this.subscriptions.entries()) {
      try {
        channel.unsubscribe();
        console.log(`🧹 Force unsubscribed from ${key}`);
      } catch (error) {
        console.error(`Error force unsubscribing from ${key}:`, error);
      }
    }

    // Clear all data structures
    this.subscriptions.clear();
    this.callbacks.clear();

    // Reset health stats but keep connections
    for (const [connectionId, health] of this.healthStats.entries()) {
      health.subscriptionCount = 0;
      health.errorCount = 0;
      health.lastHeartbeat = Date.now();
    }

    console.log('🧹 Force cleanup completed');
  }

  /**
   * Get cleanup statistics
   */
  public getCleanupStats(): {
    totalSubscriptions: number;
    totalCallbacks: number;
    emptySubscriptions: number;
    staleConnections: number;
    lastCleanup: number;
  } {
    const now = Date.now();
    const staleThreshold = 30 * 60 * 1000; // 30 minutes

    const emptySubscriptions = Array.from(this.callbacks.values()).filter(callbacks => callbacks.length === 0).length;
    const staleConnections = Array.from(this.healthStats.values()).filter(health =>
      (now - health.lastHeartbeat) > staleThreshold && !health.isConnected
    ).length;

    return {
      totalSubscriptions: this.subscriptions.size,
      totalCallbacks: Array.from(this.callbacks.values()).reduce((sum, callbacks) => sum + callbacks.length, 0),
      emptySubscriptions,
      staleConnections,
      lastCleanup: now,
    };
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  private generateSubscriptionKey(config: SubscriptionConfig): string {
    return `${config.schema || 'public'}:${config.table}:${config.event || '*'}:${config.filter || 'all'}`;
  }

  public getConnectionStats(): ConnectionPoolStats {
    const totalConnections = this.connectionPool.size;
    const activeConnections = Array.from(this.healthStats.values())
      .filter(health => health.isConnected).length;
    const totalSubscriptions = Array.from(this.healthStats.values())
      .reduce((sum, health) => sum + health.subscriptionCount, 0);
    const averageLatency = Array.from(this.healthStats.values())
      .reduce((sum, health) => sum + health.latency, 0) / this.healthStats.size;
    const totalErrors = Array.from(this.healthStats.values())
      .reduce((sum, health) => sum + health.errorCount, 0);
    const errorRate = totalSubscriptions > 0 ? (totalErrors / totalSubscriptions) * 100 : 0;

    return {
      totalConnections,
      activeConnections,
      totalSubscriptions,
      averageLatency,
      errorRate,
    };
  }

  public getHealthStatus(): SubscriptionHealth[] {
    return Array.from(this.healthStats.values());
  }

  public cleanup(): void {
    // Unsubscribe from all channels
    for (const [key, channel] of this.subscriptions.entries()) {
      try {
        channel.unsubscribe();
      } catch (error) {
        console.error(`Error unsubscribing from ${key}:`, error);
      }
    }

    // Clear all data structures
    this.subscriptions.clear();
    this.callbacks.clear();
    this.healthStats.clear();

    // Stop health monitoring
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    console.log('📡 Subscription manager cleaned up');
  }

  // ============================================================================
  // CONVENIENCE METHODS FOR COMMON SUBSCRIPTIONS
  // ============================================================================

  public subscribeToNotifications(userId: string, callback: (payload: Record<string, unknown>) => void): string {
    return this.subscribe(
      {
        table: 'notifications',
        filter: `recipient_id=eq.${userId}`,
        event: 'INSERT',
        priority: 'high',
      },
      callback
    );
  }

  public subscribeToTasks(userId: string, callback: (payload: Record<string, unknown>) => void): string {
    return this.subscribe(
      {
        table: 'tasks',
        filter: `assigned_to=eq.${userId}`,
        event: '*',
        priority: 'high',
      },
      callback
    );
  }

  public subscribeToActivityFeed(callback: (payload: Record<string, unknown>) => void): string {
    return this.subscribe(
      {
        table: 'activities',
        event: 'INSERT',
        priority: 'medium',
      },
      callback
    );
  }

  public subscribeToAttendance(staffId: string, callback: (payload: Record<string, unknown>) => void): string {
    return this.subscribe(
      {
        table: 'field_staff_attendance',
        filter: `staff_id=eq.${staffId}`,
        event: '*',
        priority: 'high',
      },
      callback
    );
  }
}

// Export singleton instance
export const realtimeSubscriptionManager = RealtimeSubscriptionManager.getInstance();
