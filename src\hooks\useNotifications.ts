import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

export type NotificationType = 
  | 'task_assigned'
  | 'task_completed'
  | 'task_overdue'
  | 'field_report_submitted'
  | 'field_report_approved'
  | 'field_report_rejected'
  | 'book_distribution_created'
  | 'book_distribution_updated'
  | 'school_added'
  | 'school_updated'
  | 'user_created'
  | 'user_updated'
  | 'system_update'
  | 'weekly_summary'
  | 'monthly_report'
  | 'low_inventory'
  | 'check_in_reminder'
  | 'check_out_reminder'
  | 'session_starting'
  | 'session_completed';

export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';
export type NotificationStatus = 'unread' | 'read' | 'archived' | 'deleted';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  priority: NotificationPriority;
  status: NotificationStatus;
  recipient_id: string;
  recipient_role: string;
  sender_id?: string;
  sender_role?: string;
  related_entity_type?: string;
  related_entity_id?: string;
  metadata?: Record<string, unknown>;
  action_url?: string;
  delivered_at: string;
  read_at?: string;
  archived_at?: string;
  created_at: string;
  updated_at: string;
  expires_at?: string;
}

export interface CreateNotificationData {
  type: NotificationType;
  title: string;
  message: string;
  recipient_id: string;
  recipient_role: string;
  priority?: NotificationPriority;
  sender_id?: string;
  sender_role?: string;
  related_entity_type?: string;
  related_entity_id?: string;
  metadata?: Record<string, unknown>;
  action_url?: string;
  expires_at?: string;
}

// Hook to get notifications for current user
export const useNotifications = (options?: {
  status?: NotificationStatus;
  type?: NotificationType;
  limit?: number;
  unreadOnly?: boolean;
}) => {
  const { user, profile } = useAuth();

  return useQuery({
    queryKey: ['notifications', user?.id, options],
    queryFn: async (): Promise<Notification[]> => {
      if (!user || !profile) {
        return [];
      }

      let query = supabase
        .from('notifications')
        .select('*')
        .eq('recipient_id', user.id)
        .order('created_at', { ascending: false });

      // Apply filters
      if (options?.status) {
        query = query.eq('status', options.status);
      }

      if (options?.type) {
        query = query.eq('type', options.type);
      }

      if (options?.unreadOnly) {
        query = query.eq('status', 'unread');
      }

      if (options?.limit) {
        query = query.limit(options.limit);
      }

      // Filter out expired notifications
      query = query.or('expires_at.is.null,expires_at.gt.' + new Date().toISOString());

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching notifications:', error);
        throw error;
      }

      return data || [];
    },
    enabled: !!user && !!profile,
    staleTime: 30000, // 30 seconds
    cacheTime: 300000, // 5 minutes
  });
};

// Hook to get unread notification count
export const useUnreadNotificationCount = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['unread-notification-count', user?.id],
    queryFn: async (): Promise<number> => {
      if (!user) {
        return 0;
      }

      const { data, error } = await supabase
        .rpc('get_unread_notification_count', { user_id: user.id });

      if (error) {
        console.error('Error fetching unread count:', error);
        return 0;
      }

      return data || 0;
    },
    enabled: !!user,
    staleTime: 10000, // 10 seconds
    cacheTime: 60000, // 1 minute
    refetchInterval: 30000, // Refetch every 30 seconds
  });
};

// Hook to create a notification
export const useCreateNotification = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (notificationData: CreateNotificationData): Promise<Notification> => {
      const { data, error } = await supabase
        .from('notifications')
        .insert({
          ...notificationData,
          metadata: notificationData.metadata || {},
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating notification:', error);
        throw error;
      }

      return data;
    },
    onSuccess: (data) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.invalidateQueries({ queryKey: ['unread-notification-count'] });
      
      // Show success toast for manual notifications
      if (data.sender_id) {
        toast({
          title: 'Notification Sent',
          description: 'Notification has been sent successfully.',
        });
      }
    },
    onError: (error) => {
      console.error('Failed to create notification:', error);
      toast({
        title: 'Error',
        description: 'Failed to send notification. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

// Hook to mark notification as read
export const useMarkNotificationRead = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (notificationId: string): Promise<boolean> => {
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .rpc('mark_notification_read', {
          notification_id: notificationId,
          user_id: user.id
        });

      if (error) {
        console.error('Error marking notification as read:', error);
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.invalidateQueries({ queryKey: ['unread-notification-count'] });
    },
  });
};

// Hook to mark all notifications as read
export const useMarkAllNotificationsRead = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (): Promise<number> => {
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .rpc('mark_all_notifications_read', { user_id: user.id });

      if (error) {
        console.error('Error marking all notifications as read:', error);
        throw error;
      }

      return data || 0;
    },
    onSuccess: (count) => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.invalidateQueries({ queryKey: ['unread-notification-count'] });
      
      if (count > 0) {
        toast({
          title: 'Notifications Updated',
          description: `Marked ${count} notification${count !== 1 ? 's' : ''} as read.`,
        });
      }
    },
  });
};

// Hook to archive notification
export const useArchiveNotification = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (notificationId: string): Promise<boolean> => {
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .rpc('archive_notification', {
          notification_id: notificationId,
          user_id: user.id
        });

      if (error) {
        console.error('Error archiving notification:', error);
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.invalidateQueries({ queryKey: ['unread-notification-count'] });
    },
  });
};
